---
services:
    management:
        build:
            dockerfile: Dockerfile
            args:
                GITLAB_USER: ${GITLAB_USER}
                GITLAB_ACCESS_TOKEN: ${GITLAB_ACCESS_TOKEN}
                CMD_NAME: management
                CMD_BIN_PATH: bin/management
                CMD_CONFIG_PATH: config/management.yml
                CMD_CONFIG_FOLDER: config
    relay:
        build:
            dockerfile: Dockerfile
            args:
                GITLAB_USER: ${GITLAB_USER}
                GITLAB_ACCESS_TOKEN: ${GITLAB_ACCESS_TOKEN}
                CMD_NAME: relay
                CMD_BIN_PATH: bin/relay
                CMD_CONFIG_PATH: config/relay.yml
                CMD_CONFIG_FOLDER: config
