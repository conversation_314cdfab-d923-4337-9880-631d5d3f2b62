syntax = "proto3";

package relay.v1;

option go_package = "git.moderntv.eu/multicast-probe/proto/relay;relay";

// gRPC service definition for the Relay Configuration API
service RelayConfigurationService {
  // Returns the current configuration of the Relay
  rpc Configuration(ConfigurationRequest) returns (ConfigurationResponse);

  // Sets the configuration of the Relay
  rpc SetConfiguration(SetConfigurationRequest) returns (SetConfigurationResponse);
}

// --- GET

message ConfigurationRequest {}

message ConfigurationResponse {
  Configuration configuration = 1;
}

// --- SET

message SetConfigurationRequest {
  Configuration configuration = 1;
}

message SetConfigurationResponse {}

// --- TYPES

message Configuration {
  int64 scan_timeout = 1;
  int64 cleanup_delay = 2;
  int64 dispatcher_buffer_size = 3;
  int64 stream_channel_buffer_size = 4;
  int64 join_buffer_size = 5;
  int64 joins_per_second = 6;
  int64 connection_timeout = 7;
  int64 udp_packet_size = 8;
  repeated NetworkInterface network_interfaces = 9;
}

message NetworkInterface {
  string name = 1;
  string prefix = 2;
}

