// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.26.1
// source: relay/v1/stream_service.proto

package relay

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MulticastIp   string                 `protobuf:"bytes,1,opt,name=multicast_ip,json=multicastIp,proto3" json:"multicast_ip,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanRequest) Reset() {
	*x = ScanRequest{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanRequest) ProtoMessage() {}

func (x *ScanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanRequest.ProtoReflect.Descriptor instead.
func (*ScanRequest) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{0}
}

func (x *ScanRequest) GetMulticastIp() string {
	if x != nil {
		return x.MulticastIp
	}
	return ""
}

func (x *ScanRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

type ScanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanResponse) Reset() {
	*x = ScanResponse{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanResponse) ProtoMessage() {}

func (x *ScanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanResponse.ProtoReflect.Descriptor instead.
func (*ScanResponse) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{1}
}

type StreamsInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamsInfoRequest) Reset() {
	*x = StreamsInfoRequest{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamsInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamsInfoRequest) ProtoMessage() {}

func (x *StreamsInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamsInfoRequest.ProtoReflect.Descriptor instead.
func (*StreamsInfoRequest) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{2}
}

type StreamsInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Streams       []*StreamInfo          `protobuf:"bytes,1,rep,name=streams,proto3" json:"streams,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamsInfoResponse) Reset() {
	*x = StreamsInfoResponse{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamsInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamsInfoResponse) ProtoMessage() {}

func (x *StreamsInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamsInfoResponse.ProtoReflect.Descriptor instead.
func (*StreamsInfoResponse) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{3}
}

func (x *StreamsInfoResponse) GetStreams() []*StreamInfo {
	if x != nil {
		return x.Streams
	}
	return nil
}

type StreamInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MulticastIp   string                 `protobuf:"bytes,1,opt,name=multicast_ip,json=multicastIp,proto3" json:"multicast_ip,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfoRequest) Reset() {
	*x = StreamInfoRequest{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfoRequest) ProtoMessage() {}

func (x *StreamInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfoRequest.ProtoReflect.Descriptor instead.
func (*StreamInfoRequest) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{4}
}

func (x *StreamInfoRequest) GetMulticastIp() string {
	if x != nil {
		return x.MulticastIp
	}
	return ""
}

func (x *StreamInfoRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

type StreamInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stream        *StreamInfo            `protobuf:"bytes,1,opt,name=stream,proto3" json:"stream,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfoResponse) Reset() {
	*x = StreamInfoResponse{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfoResponse) ProtoMessage() {}

func (x *StreamInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfoResponse.ProtoReflect.Descriptor instead.
func (*StreamInfoResponse) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{5}
}

func (x *StreamInfoResponse) GetStream() *StreamInfo {
	if x != nil {
		return x.Stream
	}
	return nil
}

type StreamInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MulticastIp   string                 `protobuf:"bytes,1,opt,name=multicast_ip,json=multicastIp,proto3" json:"multicast_ip,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Bitrate       int32                  `protobuf:"varint,3,opt,name=bitrate,proto3" json:"bitrate,omitempty"`
	Clients       int64                  `protobuf:"varint,4,opt,name=clients,proto3" json:"clients,omitempty"`
	Codec         string                 `protobuf:"bytes,5,opt,name=codec,proto3" json:"codec,omitempty"`
	Programs      []*Program             `protobuf:"bytes,6,rep,name=programs,proto3" json:"programs,omitempty"`
	Metrics       *StreamMetrics         `protobuf:"bytes,7,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfo) Reset() {
	*x = StreamInfo{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfo) ProtoMessage() {}

func (x *StreamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfo.ProtoReflect.Descriptor instead.
func (*StreamInfo) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{6}
}

func (x *StreamInfo) GetMulticastIp() string {
	if x != nil {
		return x.MulticastIp
	}
	return ""
}

func (x *StreamInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *StreamInfo) GetBitrate() int32 {
	if x != nil {
		return x.Bitrate
	}
	return 0
}

func (x *StreamInfo) GetClients() int64 {
	if x != nil {
		return x.Clients
	}
	return 0
}

func (x *StreamInfo) GetCodec() string {
	if x != nil {
		return x.Codec
	}
	return ""
}

func (x *StreamInfo) GetPrograms() []*Program {
	if x != nil {
		return x.Programs
	}
	return nil
}

func (x *StreamInfo) GetMetrics() *StreamMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type StreamMetrics struct {
	state                         protoimpl.MessageState `protogen:"open.v1"`
	StreamPresentDuration         int64                  `protobuf:"varint,1,opt,name=stream_present_duration,json=streamPresentDuration,proto3" json:"stream_present_duration,omitempty"`
	StreamReceivingDuration       int64                  `protobuf:"varint,2,opt,name=stream_receiving_duration,json=streamReceivingDuration,proto3" json:"stream_receiving_duration,omitempty"`
	StreamReceivedUdpPacketsTotal int64                  `protobuf:"varint,3,opt,name=stream_received_udp_packets_total,json=streamReceivedUdpPacketsTotal,proto3" json:"stream_received_udp_packets_total,omitempty"`
	StreamReceivedTsPacketsTotal  int64                  `protobuf:"varint,4,opt,name=stream_received_ts_packets_total,json=streamReceivedTsPacketsTotal,proto3" json:"stream_received_ts_packets_total,omitempty"`
	StreamReceivedBytesTotal      int64                  `protobuf:"varint,5,opt,name=stream_received_bytes_total,json=streamReceivedBytesTotal,proto3" json:"stream_received_bytes_total,omitempty"`
	StreamReceiverCount           int64                  `protobuf:"varint,6,opt,name=stream_receiver_count,json=streamReceiverCount,proto3" json:"stream_receiver_count,omitempty"`
	StreamDiscontinuitiesTotal    int64                  `protobuf:"varint,7,opt,name=stream_discontinuities_total,json=streamDiscontinuitiesTotal,proto3" json:"stream_discontinuities_total,omitempty"`
	StreamMissingUdpPacketsTotal  int64                  `protobuf:"varint,8,opt,name=stream_missing_udp_packets_total,json=streamMissingUdpPacketsTotal,proto3" json:"stream_missing_udp_packets_total,omitempty"`
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *StreamMetrics) Reset() {
	*x = StreamMetrics{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamMetrics) ProtoMessage() {}

func (x *StreamMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamMetrics.ProtoReflect.Descriptor instead.
func (*StreamMetrics) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{7}
}

func (x *StreamMetrics) GetStreamPresentDuration() int64 {
	if x != nil {
		return x.StreamPresentDuration
	}
	return 0
}

func (x *StreamMetrics) GetStreamReceivingDuration() int64 {
	if x != nil {
		return x.StreamReceivingDuration
	}
	return 0
}

func (x *StreamMetrics) GetStreamReceivedUdpPacketsTotal() int64 {
	if x != nil {
		return x.StreamReceivedUdpPacketsTotal
	}
	return 0
}

func (x *StreamMetrics) GetStreamReceivedTsPacketsTotal() int64 {
	if x != nil {
		return x.StreamReceivedTsPacketsTotal
	}
	return 0
}

func (x *StreamMetrics) GetStreamReceivedBytesTotal() int64 {
	if x != nil {
		return x.StreamReceivedBytesTotal
	}
	return 0
}

func (x *StreamMetrics) GetStreamReceiverCount() int64 {
	if x != nil {
		return x.StreamReceiverCount
	}
	return 0
}

func (x *StreamMetrics) GetStreamDiscontinuitiesTotal() int64 {
	if x != nil {
		return x.StreamDiscontinuitiesTotal
	}
	return 0
}

func (x *StreamMetrics) GetStreamMissingUdpPacketsTotal() int64 {
	if x != nil {
		return x.StreamMissingUdpPacketsTotal
	}
	return 0
}

type Program struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Provider      string                 `protobuf:"bytes,3,opt,name=provider,proto3" json:"provider,omitempty"`
	Tracks        []*Track               `protobuf:"bytes,4,rep,name=tracks,proto3" json:"tracks,omitempty"`
	Metrics       *ProgramMetrics        `protobuf:"bytes,5,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Program) Reset() {
	*x = Program{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Program) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Program) ProtoMessage() {}

func (x *Program) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Program.ProtoReflect.Descriptor instead.
func (*Program) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{8}
}

func (x *Program) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Program) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Program) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Program) GetTracks() []*Track {
	if x != nil {
		return x.Tracks
	}
	return nil
}

func (x *Program) GetMetrics() *ProgramMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type ProgramMetrics struct {
	state                         protoimpl.MessageState `protogen:"open.v1"`
	ProgramReceiverCount          int64                  `protobuf:"varint,1,opt,name=program_receiver_count,json=programReceiverCount,proto3" json:"program_receiver_count,omitempty"`
	ProgramDiscontinuities        int64                  `protobuf:"varint,2,opt,name=program_discontinuities,json=programDiscontinuities,proto3" json:"program_discontinuities,omitempty"`
	ProgramReceivedBytesTotal     int64                  `protobuf:"varint,3,opt,name=program_received_bytes_total,json=programReceivedBytesTotal,proto3" json:"program_received_bytes_total,omitempty"`
	ProgramReceivedTsPacketsTotal int64                  `protobuf:"varint,4,opt,name=program_received_ts_packets_total,json=programReceivedTsPacketsTotal,proto3" json:"program_received_ts_packets_total,omitempty"`
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *ProgramMetrics) Reset() {
	*x = ProgramMetrics{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProgramMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgramMetrics) ProtoMessage() {}

func (x *ProgramMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgramMetrics.ProtoReflect.Descriptor instead.
func (*ProgramMetrics) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{9}
}

func (x *ProgramMetrics) GetProgramReceiverCount() int64 {
	if x != nil {
		return x.ProgramReceiverCount
	}
	return 0
}

func (x *ProgramMetrics) GetProgramDiscontinuities() int64 {
	if x != nil {
		return x.ProgramDiscontinuities
	}
	return 0
}

func (x *ProgramMetrics) GetProgramReceivedBytesTotal() int64 {
	if x != nil {
		return x.ProgramReceivedBytesTotal
	}
	return 0
}

func (x *ProgramMetrics) GetProgramReceivedTsPacketsTotal() int64 {
	if x != nil {
		return x.ProgramReceivedTsPacketsTotal
	}
	return 0
}

type Track struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           int64                  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	Type          int64                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Metrics       *TrackMetrics          `protobuf:"bytes,4,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Track) Reset() {
	*x = Track{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Track) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Track) ProtoMessage() {}

func (x *Track) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Track.ProtoReflect.Descriptor instead.
func (*Track) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{10}
}

func (x *Track) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *Track) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Track) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Track) GetMetrics() *TrackMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type TrackMetrics struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	TrackDiscontinuities        int64                  `protobuf:"varint,1,opt,name=track_discontinuities,json=trackDiscontinuities,proto3" json:"track_discontinuities,omitempty"`
	TrackReceivedBytesTotal     int64                  `protobuf:"varint,2,opt,name=track_received_bytes_total,json=trackReceivedBytesTotal,proto3" json:"track_received_bytes_total,omitempty"`
	TrackReceivedTsPacketsTotal int64                  `protobuf:"varint,3,opt,name=track_received_ts_packets_total,json=trackReceivedTsPacketsTotal,proto3" json:"track_received_ts_packets_total,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *TrackMetrics) Reset() {
	*x = TrackMetrics{}
	mi := &file_relay_v1_stream_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackMetrics) ProtoMessage() {}

func (x *TrackMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_stream_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackMetrics.ProtoReflect.Descriptor instead.
func (*TrackMetrics) Descriptor() ([]byte, []int) {
	return file_relay_v1_stream_service_proto_rawDescGZIP(), []int{11}
}

func (x *TrackMetrics) GetTrackDiscontinuities() int64 {
	if x != nil {
		return x.TrackDiscontinuities
	}
	return 0
}

func (x *TrackMetrics) GetTrackReceivedBytesTotal() int64 {
	if x != nil {
		return x.TrackReceivedBytesTotal
	}
	return 0
}

func (x *TrackMetrics) GetTrackReceivedTsPacketsTotal() int64 {
	if x != nil {
		return x.TrackReceivedTsPacketsTotal
	}
	return 0
}

var File_relay_v1_stream_service_proto protoreflect.FileDescriptor

const file_relay_v1_stream_service_proto_rawDesc = "" +
	"\n" +
	"\x1drelay/v1/stream_service.proto\x12\brelay.v1\"D\n" +
	"\vScanRequest\x12!\n" +
	"\fmulticast_ip\x18\x01 \x01(\tR\vmulticastIp\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\"\x0e\n" +
	"\fScanResponse\"\x14\n" +
	"\x12StreamsInfoRequest\"E\n" +
	"\x13StreamsInfoResponse\x12.\n" +
	"\astreams\x18\x01 \x03(\v2\x14.relay.v1.StreamInfoR\astreams\"J\n" +
	"\x11StreamInfoRequest\x12!\n" +
	"\fmulticast_ip\x18\x01 \x01(\tR\vmulticastIp\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\"B\n" +
	"\x12StreamInfoResponse\x12,\n" +
	"\x06stream\x18\x01 \x01(\v2\x14.relay.v1.StreamInfoR\x06stream\"\xef\x01\n" +
	"\n" +
	"StreamInfo\x12!\n" +
	"\fmulticast_ip\x18\x01 \x01(\tR\vmulticastIp\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x18\n" +
	"\abitrate\x18\x03 \x01(\x05R\abitrate\x12\x18\n" +
	"\aclients\x18\x04 \x01(\x03R\aclients\x12\x14\n" +
	"\x05codec\x18\x05 \x01(\tR\x05codec\x12-\n" +
	"\bprograms\x18\x06 \x03(\v2\x11.relay.v1.ProgramR\bprograms\x121\n" +
	"\ametrics\x18\a \x01(\v2\x17.relay.v1.StreamMetricsR\ametrics\"\x92\x04\n" +
	"\rStreamMetrics\x126\n" +
	"\x17stream_present_duration\x18\x01 \x01(\x03R\x15streamPresentDuration\x12:\n" +
	"\x19stream_receiving_duration\x18\x02 \x01(\x03R\x17streamReceivingDuration\x12H\n" +
	"!stream_received_udp_packets_total\x18\x03 \x01(\x03R\x1dstreamReceivedUdpPacketsTotal\x12F\n" +
	" stream_received_ts_packets_total\x18\x04 \x01(\x03R\x1cstreamReceivedTsPacketsTotal\x12=\n" +
	"\x1bstream_received_bytes_total\x18\x05 \x01(\x03R\x18streamReceivedBytesTotal\x122\n" +
	"\x15stream_receiver_count\x18\x06 \x01(\x03R\x13streamReceiverCount\x12@\n" +
	"\x1cstream_discontinuities_total\x18\a \x01(\x03R\x1astreamDiscontinuitiesTotal\x12F\n" +
	" stream_missing_udp_packets_total\x18\b \x01(\x03R\x1cstreamMissingUdpPacketsTotal\"\xa8\x01\n" +
	"\aProgram\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x1a\n" +
	"\bprovider\x18\x03 \x01(\tR\bprovider\x12'\n" +
	"\x06tracks\x18\x04 \x03(\v2\x0f.relay.v1.TrackR\x06tracks\x122\n" +
	"\ametrics\x18\x05 \x01(\v2\x18.relay.v1.ProgramMetricsR\ametrics\"\x8a\x02\n" +
	"\x0eProgramMetrics\x124\n" +
	"\x16program_receiver_count\x18\x01 \x01(\x03R\x14programReceiverCount\x127\n" +
	"\x17program_discontinuities\x18\x02 \x01(\x03R\x16programDiscontinuities\x12?\n" +
	"\x1cprogram_received_bytes_total\x18\x03 \x01(\x03R\x19programReceivedBytesTotal\x12H\n" +
	"!program_received_ts_packets_total\x18\x04 \x01(\x03R\x1dprogramReceivedTsPacketsTotal\"\x81\x01\n" +
	"\x05Track\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\x03R\x03pid\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x03R\x04type\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x120\n" +
	"\ametrics\x18\x04 \x01(\v2\x16.relay.v1.TrackMetricsR\ametrics\"\xc6\x01\n" +
	"\fTrackMetrics\x123\n" +
	"\x15track_discontinuities\x18\x01 \x01(\x03R\x14trackDiscontinuities\x12;\n" +
	"\x1atrack_received_bytes_total\x18\x02 \x01(\x03R\x17trackReceivedBytesTotal\x12D\n" +
	"\x1ftrack_received_ts_packets_total\x18\x03 \x01(\x03R\x1btrackReceivedTsPacketsTotal2\xe0\x01\n" +
	"\x12RelayStreamService\x125\n" +
	"\x04Scan\x12\x15.relay.v1.ScanRequest\x1a\x16.relay.v1.ScanResponse\x12G\n" +
	"\n" +
	"StreamInfo\x12\x1b.relay.v1.StreamInfoRequest\x1a\x1c.relay.v1.StreamInfoResponse\x12J\n" +
	"\vStreamsInfo\x12\x1c.relay.v1.StreamsInfoRequest\x1a\x1d.relay.v1.StreamsInfoResponseB3Z1git.moderntv.eu/multicast-probe/proto/relay;relayb\x06proto3"

var (
	file_relay_v1_stream_service_proto_rawDescOnce sync.Once
	file_relay_v1_stream_service_proto_rawDescData []byte
)

func file_relay_v1_stream_service_proto_rawDescGZIP() []byte {
	file_relay_v1_stream_service_proto_rawDescOnce.Do(func() {
		file_relay_v1_stream_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_relay_v1_stream_service_proto_rawDesc), len(file_relay_v1_stream_service_proto_rawDesc)))
	})
	return file_relay_v1_stream_service_proto_rawDescData
}

var file_relay_v1_stream_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_relay_v1_stream_service_proto_goTypes = []any{
	(*ScanRequest)(nil),         // 0: relay.v1.ScanRequest
	(*ScanResponse)(nil),        // 1: relay.v1.ScanResponse
	(*StreamsInfoRequest)(nil),  // 2: relay.v1.StreamsInfoRequest
	(*StreamsInfoResponse)(nil), // 3: relay.v1.StreamsInfoResponse
	(*StreamInfoRequest)(nil),   // 4: relay.v1.StreamInfoRequest
	(*StreamInfoResponse)(nil),  // 5: relay.v1.StreamInfoResponse
	(*StreamInfo)(nil),          // 6: relay.v1.StreamInfo
	(*StreamMetrics)(nil),       // 7: relay.v1.StreamMetrics
	(*Program)(nil),             // 8: relay.v1.Program
	(*ProgramMetrics)(nil),      // 9: relay.v1.ProgramMetrics
	(*Track)(nil),               // 10: relay.v1.Track
	(*TrackMetrics)(nil),        // 11: relay.v1.TrackMetrics
}
var file_relay_v1_stream_service_proto_depIdxs = []int32{
	6,  // 0: relay.v1.StreamsInfoResponse.streams:type_name -> relay.v1.StreamInfo
	6,  // 1: relay.v1.StreamInfoResponse.stream:type_name -> relay.v1.StreamInfo
	8,  // 2: relay.v1.StreamInfo.programs:type_name -> relay.v1.Program
	7,  // 3: relay.v1.StreamInfo.metrics:type_name -> relay.v1.StreamMetrics
	10, // 4: relay.v1.Program.tracks:type_name -> relay.v1.Track
	9,  // 5: relay.v1.Program.metrics:type_name -> relay.v1.ProgramMetrics
	11, // 6: relay.v1.Track.metrics:type_name -> relay.v1.TrackMetrics
	0,  // 7: relay.v1.RelayStreamService.Scan:input_type -> relay.v1.ScanRequest
	4,  // 8: relay.v1.RelayStreamService.StreamInfo:input_type -> relay.v1.StreamInfoRequest
	2,  // 9: relay.v1.RelayStreamService.StreamsInfo:input_type -> relay.v1.StreamsInfoRequest
	1,  // 10: relay.v1.RelayStreamService.Scan:output_type -> relay.v1.ScanResponse
	5,  // 11: relay.v1.RelayStreamService.StreamInfo:output_type -> relay.v1.StreamInfoResponse
	3,  // 12: relay.v1.RelayStreamService.StreamsInfo:output_type -> relay.v1.StreamsInfoResponse
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_relay_v1_stream_service_proto_init() }
func file_relay_v1_stream_service_proto_init() {
	if File_relay_v1_stream_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_relay_v1_stream_service_proto_rawDesc), len(file_relay_v1_stream_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_relay_v1_stream_service_proto_goTypes,
		DependencyIndexes: file_relay_v1_stream_service_proto_depIdxs,
		MessageInfos:      file_relay_v1_stream_service_proto_msgTypes,
	}.Build()
	File_relay_v1_stream_service_proto = out.File
	file_relay_v1_stream_service_proto_goTypes = nil
	file_relay_v1_stream_service_proto_depIdxs = nil
}
