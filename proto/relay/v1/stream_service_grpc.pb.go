// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.26.1
// source: relay/v1/stream_service.proto

package relay

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RelayStreamService_Scan_FullMethodName        = "/relay.v1.RelayStreamService/Scan"
	RelayStreamService_StreamInfo_FullMethodName  = "/relay.v1.RelayStreamService/StreamInfo"
	RelayStreamService_StreamsInfo_FullMethodName = "/relay.v1.RelayStreamService/StreamsInfo"
)

// RelayStreamServiceClient is the client API for RelayStreamService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC service definition for the Relay Stream API.
type RelayStreamServiceClient interface {
	// Adds a temporary receiver to the specified multicast stream
	Scan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*ScanResponse, error)
	// Returns information about a specific stream
	StreamInfo(ctx context.Context, in *StreamInfoRequest, opts ...grpc.CallOption) (*StreamInfoResponse, error)
	// Returns information about all streams
	StreamsInfo(ctx context.Context, in *StreamsInfoRequest, opts ...grpc.CallOption) (*StreamsInfoResponse, error)
}

type relayStreamServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRelayStreamServiceClient(cc grpc.ClientConnInterface) RelayStreamServiceClient {
	return &relayStreamServiceClient{cc}
}

func (c *relayStreamServiceClient) Scan(ctx context.Context, in *ScanRequest, opts ...grpc.CallOption) (*ScanResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScanResponse)
	err := c.cc.Invoke(ctx, RelayStreamService_Scan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relayStreamServiceClient) StreamInfo(ctx context.Context, in *StreamInfoRequest, opts ...grpc.CallOption) (*StreamInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamInfoResponse)
	err := c.cc.Invoke(ctx, RelayStreamService_StreamInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relayStreamServiceClient) StreamsInfo(ctx context.Context, in *StreamsInfoRequest, opts ...grpc.CallOption) (*StreamsInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StreamsInfoResponse)
	err := c.cc.Invoke(ctx, RelayStreamService_StreamsInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RelayStreamServiceServer is the server API for RelayStreamService service.
// All implementations must embed UnimplementedRelayStreamServiceServer
// for forward compatibility.
//
// gRPC service definition for the Relay Stream API.
type RelayStreamServiceServer interface {
	// Adds a temporary receiver to the specified multicast stream
	Scan(context.Context, *ScanRequest) (*ScanResponse, error)
	// Returns information about a specific stream
	StreamInfo(context.Context, *StreamInfoRequest) (*StreamInfoResponse, error)
	// Returns information about all streams
	StreamsInfo(context.Context, *StreamsInfoRequest) (*StreamsInfoResponse, error)
	mustEmbedUnimplementedRelayStreamServiceServer()
}

// UnimplementedRelayStreamServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRelayStreamServiceServer struct{}

func (UnimplementedRelayStreamServiceServer) Scan(context.Context, *ScanRequest) (*ScanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Scan not implemented")
}
func (UnimplementedRelayStreamServiceServer) StreamInfo(context.Context, *StreamInfoRequest) (*StreamInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StreamInfo not implemented")
}
func (UnimplementedRelayStreamServiceServer) StreamsInfo(context.Context, *StreamsInfoRequest) (*StreamsInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StreamsInfo not implemented")
}
func (UnimplementedRelayStreamServiceServer) mustEmbedUnimplementedRelayStreamServiceServer() {}
func (UnimplementedRelayStreamServiceServer) testEmbeddedByValue()                            {}

// UnsafeRelayStreamServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RelayStreamServiceServer will
// result in compilation errors.
type UnsafeRelayStreamServiceServer interface {
	mustEmbedUnimplementedRelayStreamServiceServer()
}

func RegisterRelayStreamServiceServer(s grpc.ServiceRegistrar, srv RelayStreamServiceServer) {
	// If the following call pancis, it indicates UnimplementedRelayStreamServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RelayStreamService_ServiceDesc, srv)
}

func _RelayStreamService_Scan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelayStreamServiceServer).Scan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelayStreamService_Scan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelayStreamServiceServer).Scan(ctx, req.(*ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelayStreamService_StreamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelayStreamServiceServer).StreamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelayStreamService_StreamInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelayStreamServiceServer).StreamInfo(ctx, req.(*StreamInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelayStreamService_StreamsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamsInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelayStreamServiceServer).StreamsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelayStreamService_StreamsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelayStreamServiceServer).StreamsInfo(ctx, req.(*StreamsInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RelayStreamService_ServiceDesc is the grpc.ServiceDesc for RelayStreamService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RelayStreamService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "relay.v1.RelayStreamService",
	HandlerType: (*RelayStreamServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Scan",
			Handler:    _RelayStreamService_Scan_Handler,
		},
		{
			MethodName: "StreamInfo",
			Handler:    _RelayStreamService_StreamInfo_Handler,
		},
		{
			MethodName: "StreamsInfo",
			Handler:    _RelayStreamService_StreamsInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "relay/v1/stream_service.proto",
}
