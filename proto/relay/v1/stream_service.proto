syntax = "proto3";

package relay.v1;

option go_package = "git.moderntv.eu/multicast-probe/proto/relay;relay";

// gRPC service definition for the Relay Stream API.
service RelayStreamService {
  // Adds a temporary receiver to the specified multicast stream
  rpc Scan(ScanRequest) returns (ScanResponse);

  // Returns information about a specific stream
  rpc StreamInfo(StreamInfoRequest) returns (StreamInfoResponse);

  // Returns information about all streams
  rpc StreamsInfo(StreamsInfoRequest) returns (StreamsInfoResponse);
}

// --- SCANS

message ScanRequest {
  string multicast_ip = 1;
  int32 port = 2;
}

message ScanResponse {}

// --- STREAMS

message StreamsInfoRequest {}

message StreamsInfoResponse {
  repeated StreamInfo streams = 1;
}

message StreamInfoRequest {
  string multicast_ip = 1;
  int32 port = 2;
}

message StreamInfoResponse {
  StreamInfo stream = 1;
}

// --- TYPES

message StreamInfo {
  string multicast_ip = 1;
  int32 port = 2;
  int32 bitrate = 3;
  int64 clients = 4;
  string codec = 5;
  repeated Program programs = 6;
  StreamMetrics metrics = 7;
}

message StreamMetrics {
  int64 stream_present_duration = 1;
  int64 stream_receiving_duration = 2;
  int64 stream_received_udp_packets_total = 3;
  int64 stream_received_ts_packets_total = 4;
  int64 stream_received_bytes_total = 5;
  int64 stream_receiver_count = 6;
  int64 stream_discontinuities_total = 7;
  int64 stream_missing_udp_packets_total = 8;
}

message Program {
  int64 id = 1;
  string title = 2;
  string provider = 3;
  repeated Track tracks = 4;
  ProgramMetrics metrics = 5;
}

message ProgramMetrics {
  int64 program_receiver_count = 1;
  int64 program_discontinuities = 2;
  int64 program_received_bytes_total = 3;
  int64 program_received_ts_packets_total = 4;
}

message Track {
  int64 pid = 1;
  int64 type = 2;
  string description = 3;
  TrackMetrics metrics = 4;
}

message TrackMetrics {
  int64 track_discontinuities = 1;
  int64 track_received_bytes_total = 2;
  int64 track_received_ts_packets_total = 3;
}
