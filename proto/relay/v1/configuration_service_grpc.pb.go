// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.26.1
// source: relay/v1/configuration_service.proto

package relay

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RelayConfigurationService_Configuration_FullMethodName    = "/relay.v1.RelayConfigurationService/Configuration"
	RelayConfigurationService_SetConfiguration_FullMethodName = "/relay.v1.RelayConfigurationService/SetConfiguration"
)

// RelayConfigurationServiceClient is the client API for RelayConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC service definition for the Relay Configuration API
type RelayConfigurationServiceClient interface {
	// Returns the current configuration of the Relay
	Configuration(ctx context.Context, in *ConfigurationRequest, opts ...grpc.CallOption) (*ConfigurationResponse, error)
	// Sets the configuration of the Relay
	SetConfiguration(ctx context.Context, in *SetConfigurationRequest, opts ...grpc.CallOption) (*SetConfigurationResponse, error)
}

type relayConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRelayConfigurationServiceClient(cc grpc.ClientConnInterface) RelayConfigurationServiceClient {
	return &relayConfigurationServiceClient{cc}
}

func (c *relayConfigurationServiceClient) Configuration(ctx context.Context, in *ConfigurationRequest, opts ...grpc.CallOption) (*ConfigurationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfigurationResponse)
	err := c.cc.Invoke(ctx, RelayConfigurationService_Configuration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *relayConfigurationServiceClient) SetConfiguration(ctx context.Context, in *SetConfigurationRequest, opts ...grpc.CallOption) (*SetConfigurationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetConfigurationResponse)
	err := c.cc.Invoke(ctx, RelayConfigurationService_SetConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RelayConfigurationServiceServer is the server API for RelayConfigurationService service.
// All implementations must embed UnimplementedRelayConfigurationServiceServer
// for forward compatibility.
//
// gRPC service definition for the Relay Configuration API
type RelayConfigurationServiceServer interface {
	// Returns the current configuration of the Relay
	Configuration(context.Context, *ConfigurationRequest) (*ConfigurationResponse, error)
	// Sets the configuration of the Relay
	SetConfiguration(context.Context, *SetConfigurationRequest) (*SetConfigurationResponse, error)
	mustEmbedUnimplementedRelayConfigurationServiceServer()
}

// UnimplementedRelayConfigurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRelayConfigurationServiceServer struct{}

func (UnimplementedRelayConfigurationServiceServer) Configuration(context.Context, *ConfigurationRequest) (*ConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Configuration not implemented")
}
func (UnimplementedRelayConfigurationServiceServer) SetConfiguration(context.Context, *SetConfigurationRequest) (*SetConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetConfiguration not implemented")
}
func (UnimplementedRelayConfigurationServiceServer) mustEmbedUnimplementedRelayConfigurationServiceServer() {
}
func (UnimplementedRelayConfigurationServiceServer) testEmbeddedByValue() {}

// UnsafeRelayConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RelayConfigurationServiceServer will
// result in compilation errors.
type UnsafeRelayConfigurationServiceServer interface {
	mustEmbedUnimplementedRelayConfigurationServiceServer()
}

func RegisterRelayConfigurationServiceServer(s grpc.ServiceRegistrar, srv RelayConfigurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedRelayConfigurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RelayConfigurationService_ServiceDesc, srv)
}

func _RelayConfigurationService_Configuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelayConfigurationServiceServer).Configuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelayConfigurationService_Configuration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelayConfigurationServiceServer).Configuration(ctx, req.(*ConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RelayConfigurationService_SetConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelayConfigurationServiceServer).SetConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelayConfigurationService_SetConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelayConfigurationServiceServer).SetConfiguration(ctx, req.(*SetConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RelayConfigurationService_ServiceDesc is the grpc.ServiceDesc for RelayConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RelayConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "relay.v1.RelayConfigurationService",
	HandlerType: (*RelayConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Configuration",
			Handler:    _RelayConfigurationService_Configuration_Handler,
		},
		{
			MethodName: "SetConfiguration",
			Handler:    _RelayConfigurationService_SetConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "relay/v1/configuration_service.proto",
}
