// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.26.1
// source: relay/v1/configuration_service.proto

package relay

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigurationRequest) Reset() {
	*x = ConfigurationRequest{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationRequest) ProtoMessage() {}

func (x *ConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationRequest.ProtoReflect.Descriptor instead.
func (*ConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{0}
}

type ConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configuration *Configuration         `protobuf:"bytes,1,opt,name=configuration,proto3" json:"configuration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigurationResponse) Reset() {
	*x = ConfigurationResponse{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationResponse) ProtoMessage() {}

func (x *ConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationResponse.ProtoReflect.Descriptor instead.
func (*ConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{1}
}

func (x *ConfigurationResponse) GetConfiguration() *Configuration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

type SetConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configuration *Configuration         `protobuf:"bytes,1,opt,name=configuration,proto3" json:"configuration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetConfigurationRequest) Reset() {
	*x = SetConfigurationRequest{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConfigurationRequest) ProtoMessage() {}

func (x *SetConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConfigurationRequest.ProtoReflect.Descriptor instead.
func (*SetConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{2}
}

func (x *SetConfigurationRequest) GetConfiguration() *Configuration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

type SetConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetConfigurationResponse) Reset() {
	*x = SetConfigurationResponse{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConfigurationResponse) ProtoMessage() {}

func (x *SetConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConfigurationResponse.ProtoReflect.Descriptor instead.
func (*SetConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{3}
}

type Configuration struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	ScanTimeout             int64                  `protobuf:"varint,1,opt,name=scan_timeout,json=scanTimeout,proto3" json:"scan_timeout,omitempty"`
	CleanupDelay            int64                  `protobuf:"varint,2,opt,name=cleanup_delay,json=cleanupDelay,proto3" json:"cleanup_delay,omitempty"`
	DispatcherBufferSize    int64                  `protobuf:"varint,3,opt,name=dispatcher_buffer_size,json=dispatcherBufferSize,proto3" json:"dispatcher_buffer_size,omitempty"`
	StreamChannelBufferSize int64                  `protobuf:"varint,4,opt,name=stream_channel_buffer_size,json=streamChannelBufferSize,proto3" json:"stream_channel_buffer_size,omitempty"`
	JoinBufferSize          int64                  `protobuf:"varint,5,opt,name=join_buffer_size,json=joinBufferSize,proto3" json:"join_buffer_size,omitempty"`
	JoinsPerSecond          int64                  `protobuf:"varint,6,opt,name=joins_per_second,json=joinsPerSecond,proto3" json:"joins_per_second,omitempty"`
	ConnectionTimeout       int64                  `protobuf:"varint,7,opt,name=connection_timeout,json=connectionTimeout,proto3" json:"connection_timeout,omitempty"`
	UdpPacketSize           int64                  `protobuf:"varint,8,opt,name=udp_packet_size,json=udpPacketSize,proto3" json:"udp_packet_size,omitempty"`
	NetworkInterfaces       []*NetworkInterface    `protobuf:"bytes,9,rep,name=network_interfaces,json=networkInterfaces,proto3" json:"network_interfaces,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *Configuration) Reset() {
	*x = Configuration{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Configuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Configuration) ProtoMessage() {}

func (x *Configuration) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Configuration.ProtoReflect.Descriptor instead.
func (*Configuration) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{4}
}

func (x *Configuration) GetScanTimeout() int64 {
	if x != nil {
		return x.ScanTimeout
	}
	return 0
}

func (x *Configuration) GetCleanupDelay() int64 {
	if x != nil {
		return x.CleanupDelay
	}
	return 0
}

func (x *Configuration) GetDispatcherBufferSize() int64 {
	if x != nil {
		return x.DispatcherBufferSize
	}
	return 0
}

func (x *Configuration) GetStreamChannelBufferSize() int64 {
	if x != nil {
		return x.StreamChannelBufferSize
	}
	return 0
}

func (x *Configuration) GetJoinBufferSize() int64 {
	if x != nil {
		return x.JoinBufferSize
	}
	return 0
}

func (x *Configuration) GetJoinsPerSecond() int64 {
	if x != nil {
		return x.JoinsPerSecond
	}
	return 0
}

func (x *Configuration) GetConnectionTimeout() int64 {
	if x != nil {
		return x.ConnectionTimeout
	}
	return 0
}

func (x *Configuration) GetUdpPacketSize() int64 {
	if x != nil {
		return x.UdpPacketSize
	}
	return 0
}

func (x *Configuration) GetNetworkInterfaces() []*NetworkInterface {
	if x != nil {
		return x.NetworkInterfaces
	}
	return nil
}

type NetworkInterface struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Prefix        string                 `protobuf:"bytes,2,opt,name=prefix,proto3" json:"prefix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkInterface) Reset() {
	*x = NetworkInterface{}
	mi := &file_relay_v1_configuration_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkInterface) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkInterface) ProtoMessage() {}

func (x *NetworkInterface) ProtoReflect() protoreflect.Message {
	mi := &file_relay_v1_configuration_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkInterface.ProtoReflect.Descriptor instead.
func (*NetworkInterface) Descriptor() ([]byte, []int) {
	return file_relay_v1_configuration_service_proto_rawDescGZIP(), []int{5}
}

func (x *NetworkInterface) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NetworkInterface) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

var File_relay_v1_configuration_service_proto protoreflect.FileDescriptor

const file_relay_v1_configuration_service_proto_rawDesc = "" +
	"\n" +
	"$relay/v1/configuration_service.proto\x12\brelay.v1\"\x16\n" +
	"\x14ConfigurationRequest\"V\n" +
	"\x15ConfigurationResponse\x12=\n" +
	"\rconfiguration\x18\x01 \x01(\v2\x17.relay.v1.ConfigurationR\rconfiguration\"X\n" +
	"\x17SetConfigurationRequest\x12=\n" +
	"\rconfiguration\x18\x01 \x01(\v2\x17.relay.v1.ConfigurationR\rconfiguration\"\x1a\n" +
	"\x18SetConfigurationResponse\"\xc0\x03\n" +
	"\rConfiguration\x12!\n" +
	"\fscan_timeout\x18\x01 \x01(\x03R\vscanTimeout\x12#\n" +
	"\rcleanup_delay\x18\x02 \x01(\x03R\fcleanupDelay\x124\n" +
	"\x16dispatcher_buffer_size\x18\x03 \x01(\x03R\x14dispatcherBufferSize\x12;\n" +
	"\x1astream_channel_buffer_size\x18\x04 \x01(\x03R\x17streamChannelBufferSize\x12(\n" +
	"\x10join_buffer_size\x18\x05 \x01(\x03R\x0ejoinBufferSize\x12(\n" +
	"\x10joins_per_second\x18\x06 \x01(\x03R\x0ejoinsPerSecond\x12-\n" +
	"\x12connection_timeout\x18\a \x01(\x03R\x11connectionTimeout\x12&\n" +
	"\x0fudp_packet_size\x18\b \x01(\x03R\rudpPacketSize\x12I\n" +
	"\x12network_interfaces\x18\t \x03(\v2\x1a.relay.v1.NetworkInterfaceR\x11networkInterfaces\">\n" +
	"\x10NetworkInterface\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06prefix\x18\x02 \x01(\tR\x06prefix2\xc8\x01\n" +
	"\x19RelayConfigurationService\x12P\n" +
	"\rConfiguration\x12\x1e.relay.v1.ConfigurationRequest\x1a\x1f.relay.v1.ConfigurationResponse\x12Y\n" +
	"\x10SetConfiguration\x12!.relay.v1.SetConfigurationRequest\x1a\".relay.v1.SetConfigurationResponseB3Z1git.moderntv.eu/multicast-probe/proto/relay;relayb\x06proto3"

var (
	file_relay_v1_configuration_service_proto_rawDescOnce sync.Once
	file_relay_v1_configuration_service_proto_rawDescData []byte
)

func file_relay_v1_configuration_service_proto_rawDescGZIP() []byte {
	file_relay_v1_configuration_service_proto_rawDescOnce.Do(func() {
		file_relay_v1_configuration_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_relay_v1_configuration_service_proto_rawDesc), len(file_relay_v1_configuration_service_proto_rawDesc)))
	})
	return file_relay_v1_configuration_service_proto_rawDescData
}

var file_relay_v1_configuration_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_relay_v1_configuration_service_proto_goTypes = []any{
	(*ConfigurationRequest)(nil),     // 0: relay.v1.ConfigurationRequest
	(*ConfigurationResponse)(nil),    // 1: relay.v1.ConfigurationResponse
	(*SetConfigurationRequest)(nil),  // 2: relay.v1.SetConfigurationRequest
	(*SetConfigurationResponse)(nil), // 3: relay.v1.SetConfigurationResponse
	(*Configuration)(nil),            // 4: relay.v1.Configuration
	(*NetworkInterface)(nil),         // 5: relay.v1.NetworkInterface
}
var file_relay_v1_configuration_service_proto_depIdxs = []int32{
	4, // 0: relay.v1.ConfigurationResponse.configuration:type_name -> relay.v1.Configuration
	4, // 1: relay.v1.SetConfigurationRequest.configuration:type_name -> relay.v1.Configuration
	5, // 2: relay.v1.Configuration.network_interfaces:type_name -> relay.v1.NetworkInterface
	0, // 3: relay.v1.RelayConfigurationService.Configuration:input_type -> relay.v1.ConfigurationRequest
	2, // 4: relay.v1.RelayConfigurationService.SetConfiguration:input_type -> relay.v1.SetConfigurationRequest
	1, // 5: relay.v1.RelayConfigurationService.Configuration:output_type -> relay.v1.ConfigurationResponse
	3, // 6: relay.v1.RelayConfigurationService.SetConfiguration:output_type -> relay.v1.SetConfigurationResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_relay_v1_configuration_service_proto_init() }
func file_relay_v1_configuration_service_proto_init() {
	if File_relay_v1_configuration_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_relay_v1_configuration_service_proto_rawDesc), len(file_relay_v1_configuration_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_relay_v1_configuration_service_proto_goTypes,
		DependencyIndexes: file_relay_v1_configuration_service_proto_depIdxs,
		MessageInfos:      file_relay_v1_configuration_service_proto_msgTypes,
	}.Build()
	File_relay_v1_configuration_service_proto = out.File
	file_relay_v1_configuration_service_proto_goTypes = nil
	file_relay_v1_configuration_service_proto_depIdxs = nil
}
