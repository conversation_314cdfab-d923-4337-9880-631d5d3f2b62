.PHONY: all clean test

DOCS_DIR ?= docs

define DOCS_template =
ifeq ($$($(1)_EXTRA_DOCS_DIRS),)
	$(1)_DOCS_DIRS = cmd/$(1)
else
	$(1)_DOCS_DIRS = cmd/$(1),$$($(1)_EXTRA_DOCS_DIRS)
endif \

.PHONY: docs-$(1)
docs-$(1): docs-dir
	go run github.com/swaggo/swag/cmd/swag \
		init \
		-d "$$($(1)_DOCS_DIRS)" \
		-o "$$(DOCS_DIR)/rest-api/$(1)/" \
		--parseDependency \
		--parseDepth 2 \
		--parseInternal
endef

$(foreach cmd,$(CMDS),$(eval $(call DOCS_template,$(cmd))))

define DOCS_COPY_template =
.PHONY: docs-copy-$(1)
docs-copy-$(1): docs
	cp -r $(DOCS_DIR)/rest-api/$(1)* assets/$(1)/docs
endef

$(foreach cmd,$(CMDS),$(eval $(call DOCS_COPY_template,$(cmd))))

define DOCS_CLEAN_template =
.PHONY: docs-clean-$(1)
docs-clean-$(1):
	rm -rf $(DOCS_DIR)/rest-api/$(1)/*
	rm -rf assets/$(1)/docs/$(1)
endef

$(foreach cmd,$(CMDS),$(eval $(call DOCS_CLEAN_template,$(cmd))))

.PHONY: docs
docs: $(DOCS_PREREQ) docs-dir $(foreach cmd,$(CMDS),docs-$(cmd))

.PHONY: docs-dir
docs-dir:
	mkdir -p "$(DOCS_DIR)"

.PHONY: docs-copy
docs-copy: $(foreach cmd,$(CMDS),docs-copy-$(cmd))

.PHONY: docs-clean
docs-clean: $(foreach cmd,$(CMDS),docs-clean-$(cmd))

.PHONY: run-docs
run-docs: $(RUN_DOCS_PREREQ)
	go run mjpclab.dev/ghfs -I "index.html" -r docs -l 8000
