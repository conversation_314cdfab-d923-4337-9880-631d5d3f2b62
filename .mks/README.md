# mks

A repository of Makefiles to service Go projects.
Each _\*.mk_ file defines _phony_ targets to perform some action.

## Usage

1. Add mks repository as a submodule in your project or
   clone mks into `.mks/` folder in your project.
2. Include .mk file(s) in your project's root Makefile.
3. Mks includes `update.mk` which solves update of all makefiles.

### Example

```Makefile
PROJECT_NAME = "example-project"
CMDS = $(shell ls -1 cmd/)

include mks/build.mk

RUN_PREREQ = before-run
include mks/run.mk

.PHONY: before-run
before-run:
 @echo "Run before target `run`"
```

## Modules

### build.mk

Builds packages found in directory `./cmd/${name}` into an
executable file stored in the build directory `BUILD_DIR`.

#### Variables

| Name            | Availability    | Description                                                                           |
| --------------- | --------------- | ------------------------------------------------------------------------------------- |
| `CMDS`          | required        | Space delimited list of targets found in `./cmd` (e.g. "stream content geolocation"). |
| `BUILD_DIR`     | default="bin"   | Directory to place compiled executable file.                                          |
| `OS`            | default="linux" | Cross-compilation target operating system.                                            |
| `ARCH`          | default="amd64" | Cross-compilation target architecture.                                                |
| `GO_CC_FLAGS`   | optional        | Flags for the Go compiler.                                                            |
| `GO_BUILD_TAGS` | optional        | Tags for the Go compiler.                                                             |
| `BUILD_PREREQ`  | optional        | Run Makefile target before module's `build` target.                                   |

#### Targets

| Name                    | Description                                                                                    |
| ----------------------- | ---------------------------------------------------------------------------------------------- |
| `build`                 | Builds all executables.                                                                        |
| `build-${name}`         | Builds a specific executable file.                                                             |
| `build-cgo-${name}`     | Builds program inside docker debian image to preserve correct `libc` version in target binary. |
| `build-dir`             | Creates a build directory specified in `BUILD_DIR`.                                            |
| `build-clean`           | Removes a build directory and its content.                                                     |
| `build-clean-cgo-cache` | Removes docker cache when using `cgo` build makefile target.                                   |
| `bin/k6`                | Builds k6 using `xk6` for building custom k6 with plugins.                                     |

#### Example usage

```shell
make build-stream # builds "stream" service
```

```shell
make build-cgo-stream # builds "stream" service inside Docker container
```

### deploy.mk

Deploys services into nomad using images that were build by `docker.mk`.
The images that are used are stored in `REGISTRY`.

#### Variables

| Name                  | Availability | Description                                                                                                                 |
| --------------------- | ------------ | --------------------------------------------------------------------------------------------------------------------------- |
| `CI_ENVIRONMENT_SLUG` | required     | CI unique salt that specifies unique job per each branch.                                                                   |
| `REGISTRY`            | required     | Location of docker registry where images are stored from executing `make docker-build-${name}` and `docker push` commands.  |
| `VERSION`             | required     | Version of build image. Latest usually points to newest image that was build by master branch.                              |
| `DEPLOY_HOST`         | required     | Name of job that is created in nomad. Usually contains also hostname of cluster. e.g moderntv.dev or moderntv.app for prod. |
| `CI_PROJECT_NAME`     | required     | Name of project in your CI.                                                                                                 |

#### Targets

| Name          | Description                               |
| ------------- | ----------------------------------------- |
| `deploy`      | Deploys services with all prerequisities. |
| `stop-deploy` | Purges deployed jobs.                     |

#### Example usage

```shell
make deploy # deploys app.nomad deployment based on given definition and set up variables.
```

### dev.mk

Starts/stops the development environment.
Expects a non-empty `docker-compose.(yml|yaml)` in the current directory.

Project services MUST be assigned to `services` profile.
Dependencies (databases, message brokers,...) MUST be assigned
to `dependencies` Compose profile.

#### Variables

| Name               | Availability                    | Description                                             |
| ------------------ | ------------------------------- | ------------------------------------------------------- |
| `COMPOSE_PROFILES` | default="services,dependencies" | Start services belonging to these profiles.             |
| `DEV_PREREQ`       | optional                        | Run Makefile target before module's `dev` target.       |
| `DEV_CLEAN_PREREQ` | optional                        | Run Makefile target before module's `dev-clean` target. |

#### Targets

| Name        | Description                                   |
| ----------- | --------------------------------------------- |
| `dev`       | Starts the development environment.           |
| `dev-clean` | Stops and cleans the development environment. |

#### Example usage

```shell
make dev # starts development environment
make dev COMPOSE_PROFILES="dependencies" # starts only the dependencies
make dev-clean # stops and cleans the development environment
```

### docker.mk

Builds, tags and pushes Docker images for packages found in `cmd/${name}`.
Expects a non-empty `docker-compose.build.yml` in the current directory.

#### Variables

| Name           | Availability | Description                                                                           |
| -------------- | ------------ | ------------------------------------------------------------------------------------- |
| `PROJECT_NAME` | required     | Name of the project.                                                                  |
| `CMDS`         | required     | Space delimited list of targets found in `./cmd` (i.e. "stream content geolocation"). |
| `VERSION`      | required     | Version used as a tag name.                                                           |
| `REGISTRY`     | required     | Docker registry used as a prefix of the image name.                                   |

#### Targets

| Name                   | Description                                                                                    |
| ---------------------- | ---------------------------------------------------------------------------------------------- |
| `docker-build`         | Builds Docker images for all packages.                                                         |
| `docker-build-${name}` | Builds Docker image for a specific package.                                                    |
| `docker-tag`           | Tags Docker images for all packages to include `REGISTRY` in the name.                         |
| `docker-tag-${name}`   | Tags a Docker image for a specific package to include `REGISTRY` in the name.                  |
| `docker-push`          | Push all built and tagged Docker images into `REGISTRY`.                                       |
| `docker-push-${name}`  | Pushes single Docker image into `Registry`. It is expected to use `docker-tag-${name}` before. |

#### Example usage

```shell
make docker-build # builds Docker images for all services
make docker-build-content # builds Docker image for "content" services
make docker-tag-content # tags an image so it can be published to a repository
make docker-push-content # pushes build image with tag into set `REGISTRY`.
```

### docs.mk

Builds documentation for packages found in
directory `./cmd/${name}` with [swag](https://github.com/swaggo/swag), and
optionally starts [ghfs](https://github.com/mjpclab/go-http-file-server)
a web server for live preview. Swag and ghfs
executable packages SHOULD be versioned as direct dependencies in project's `go.mod`.

#### Variables

| Name              | Availability     | Description                                                                           |
| ----------------- | ---------------- | ------------------------------------------------------------------------------------- |
| `CMDS`            | required         | Space delimited list of targets found in `./cmd` (i.e. "stream content geolocation"). |
| `DOCS_DIR`        | default="./docs" | Directory to place the built documentation.                                           |
| `DOCS_PREREQ`     | optional         | Run Makefile target before module's `docs` target.                                    |
| `RUN_DOCS_PREREQ` | optional         | Run Makefile target before module's `run-docs` target.                                |

#### Targets

| Name                 | Description                                                                          |
| -------------------- | ------------------------------------------------------------------------------------ |
| `docs`               | Builds documentation for all packages.                                               |
| `docs-${name}`       | Builds documentation for a specific package.                                         |
| `docs-dir`           | Creates a build directory specified in `DOCS_DIR`.                                   |
| `docs-clean`         | Removes content of `DOCS_DIR` and `assets` docs for apidoc for each `CMDS`.          |
| `docs-clean-${name}` | Removes content of `DOCS_DIR` and `assets` docs for apidoc for single cmd in `CDMS`. |
| `run-docs`           | Starts a webserver on port `8000` with live preview.                                 |

### lint.mk

Lints the current directory using [golangci-lint](https://github.com/golangci/golangci-lint).
The golangci-lint executable package SHOULD be versioned as a direct
dependency in project's `go.mod`.

#### Variables

| Name          | Availability | Description                                        |
| ------------- | ------------ | -------------------------------------------------- |
| `LINT_PREREQ` | optional     | Run Makefile target before module's `lint` target. |

#### Targets

| Name   | Description |
| ------ | ----------- |
| `lint` | Run linter. |

### package.mk

Builds a Debian packages out of the content found
in `${DEPLOYMENT_DIR}/${name}_pkg` and executables in `PACKAGE_DIR`.

#### Variables

| Name                  | Availability                       | Description                                                                           |
| --------------------- | ---------------------------------- | ------------------------------------------------------------------------------------- |
| `PROJECT_NAME`        | required                           | Name of the project.                                                                  |
| `PACKAGES`            | required                           | Space delimited list of targets found in `./cmd` (e.g. "stream content geolocation"). |
| `VERSION`             | default="latest"                   | Package version.                                                                      |
| `BUILD_DIR`           | default="bin"                      | Directory with executable files.                                                      |
| `PACKAGE_DIR`         | default="build"                    | Directory used for building packages.                                                 |
| `DEPLOYMENT_DIR`      | default="deployment"               | Directory with package templates.                                                     |
| `BUILD_DISTRIBUTIONS` | default="buster bullseye bookworm" | Default debian distributions that will be program build for.                          |
| `NEXUS_URL`           | default="nexus.moderntv.dev"       | Nexus package registry URL.                                                           |
| `OPT_DIR_SEPARATOR`   | default="/"                        | Separator used in package opt/etc path as conventions vary between projects.          |
| `PACKAGE_PREREQ`      | optional                           | Run Makefile target before module's `package` target.                                 |

#### Targets

| Name                             | Description                                                                    |
| -------------------------------- | ------------------------------------------------------------------------------ |
| `package`                        | Builds Debian packages for all packages.                                       |
| `package-${name}`                | Builds Debian package for a specific package.                                  |
| `package-dir`                    | Creates a build directory specified in `BUILD_DIR`.                            |
| `package-clean`                  | Removes a build directory and its content.                                     |
| `package-upload`                 | Upload all packages to nexus repository.                                       |
| `package-upload-${name}-${dist}` | Upload specific package with specific Debian distribution to nexus repository. |
| `package-upload-check`           | Check that variables push to nexus are set.                                    |

### proto.mk

Runs proto generator for `PROTOS` target/s.

#### Variables

| Name                 | Availability                                                   | Description                                                         |
| -------------------- | -------------------------------------------------------------- | ------------------------------------------------------------------- |
| `GO_PROJECT_PACKAGE` | default="cat go.mod \| grep -E '^module' \| awk '{print $$2}'" | Full go mod name of project. Used for `go_opt` module specification |
| `PROTOS`             | required                                                       | Same as `CMDS`, list of proto targets to be generated.              |

#### Targets

| Name            | Description                                                         |
| --------------- | ------------------------------------------------------------------- |
| `proto`         | Iterates over all `PROTOS` targets and generates protobuf.          |
| `proto-${name}` | Generates single protobuf target with specified `${name}` variable. |

### run.mk

Builds and runs Go files found in directory `./cmd/${name}`.

#### Variables

| Name           | Availability    | Description                                                                           |
| -------------- | --------------- | ------------------------------------------------------------------------------------- |
| `PROJECT_NAME` | required        | Name of the project to include in the executable filename.                            |
| `CMDS`         | required        | Space delimited list of targets found in `./cmd` (i.e. "stream content geolocation"). |
| `BUILD_DIR`    | default="./bin" | Directory to place compiled executable file.                                          |
| `GO_CC_FLAGS`  | optional        | Flags for the Go compiler.                                                            |
| `RUN_PREREQ`   | optional        | Run Makefile target before module's `run` target.                                     |

#### Targets

| Name          | Description                                 |
| ------------- | ------------------------------------------- |
| `run-${name}` | Builds and runs a specific executable file. |

### test.mk

Runs local unit tests recursively from the current directory.

#### Variables

| Name                      | Availability            | Description                                                  |
| ------------------------- | ----------------------- | ------------------------------------------------------------ |
| `TEST_ENV`                | default="CGO_ENABLED=1" | Environment variables for test.                              |
| `TEST_RUN`                | default="^.\*$$"        | Test functions to run, specified as a pattern.               |
| `TEST_TIMEOUT`            | default="1h"            | Test timeout.                                                |
| `TEST_DIR`                | default="./..."         | Directory with test files.                                   |
| `TEST_OPTIONS`            | default=""              | Used to enable other options for test such as verbosity etc. |
| `COVERAGE_PROFILE_OUTPUT` | default="cp.out"        | Output file for coverage profile.                            |

#### Targets

| Name            | Description            |
| --------------- | ---------------------- |
| `test`          | Runs local unit tests. |
| `test-coverage` | Print test coverage.   |

### update.mk

Runs update from specified branch of `git.moderntv.eu:misc/mks.git` to `./.mks/` folder where the makefiles are stored within Go project.

#### Variables

| Name          | Availability     | Description                                        |
| ------------- | ---------------- | -------------------------------------------------- |
| `MKS_VERSION` | default="master" | Branch that should be cloned and mks updated from. |

#### Targets

| Name         | Description        |
| ------------ | ------------------ |
| `update-mks` | Updates mks files. |

## Versioning tools as project's direct dependency

Some modules expect certain tools to be versioned as
project's direct dependency. The benefit of doing so is version
consistency across the environments and easy upgrades.
This is, of course, only applicable to tools written in Go.

At the moment the following tools are invoked from the local `go.mod`.
You can paste the content into a file in your
project's root directory. Run `go mod tidy` afterward.

```go
//go:build tools

package foo

import (
	_ "github.com/golangci/golangci-lint/cmd/golangci-lint"
	_ "github.com/swaggo/swag/cmd/swag"
	_ "mjpclab.dev/ghfs/src"
)
```

## Updating of Mks

Current state of Mks is that updates are done using
update.mk `update-mks` rule. The rule itself clones master branch,
copies everything inside `./mks/` folder which is expected to exists.
The other approach is using submodules. However the CI in
GitLab has issues with cloning other namespace project with
given CI token permissions. Once solved could be imported as submodule
and updates will look like this `git submodule update mks`.

## Troubleshooting

Running `make` fails with the following error:

> Makefile:175: mks/build.mk: No such file or directory

You have forgotten to initialize a submodule:

- [Local environment](https://git-scm.com/book/en/v2/Git-Tools-Submodules) (**tl;dr:**
  `git submodule init && git submodule update`)
- [GitLab CI/CD pipeline](https://docs.gitlab.com/ee/ci/git_submodules.html#use-git-submodules-in-cicd-jobs)
