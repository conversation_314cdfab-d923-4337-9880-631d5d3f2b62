.PHONY: all clean test

COVERAGE_PROFILE_OUTPUT ?= cp.out

.PHONY: test
test: TEST_ENVS ?= CGO_ENABLED=1
test: TEST_RUN ?= ^.*$$
test: TEST_TIMEOUT ?= 1h
test: TEST_OPTIONS ?=
test: TEST_DIR ?= ./...
test:
	$(TEST_ENVS) go test \
		-race \
		-run '$(TEST_RUN)' \
		-timeout $(TEST_TIMEOUT) \
		-coverprofile $(COVERAGE_PROFILE_OUTPUT) \
		$(TEST_OPTIONS) \
		$(TEST_DIR)

.PHONY: test-coverage
test-coverage: test
	@go tool cover -func $(COVERAGE_PROFILE_OUTPUT)
