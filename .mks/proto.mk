.PHONY: all clean test

GO_PROJECT_PACKAGE ?= $(shell cat go.mod | grep -E '^module' | awk '{print $$2}')

define PROTO_template =
.PHONY: proto-$(1)
proto-$(1):
	protoc --proto_path=. -I ./proto -I $$(GOPATH)/pkg/mod \
		--go_out=. \
		--go_opt=module=$$(GO_PROJECT_PACKAGE) \
		--go-grpc_out=. \
		--go-grpc_opt=module=$$(GO_PROJECT_PACKAGE) \
		proto/$(1)/*.proto
endef

$(foreach proto-cmd, $(PROTOS), $(eval $(call PROTO_template,$(proto-cmd))))

.PHONY: proto
proto: $(foreach proto-cmd,$(PROTOS),proto-$(proto-cmd))
