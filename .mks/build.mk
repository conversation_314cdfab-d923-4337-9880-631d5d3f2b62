.PHONY: all clean test

BUILD_DIR ?= ./bin

GO_BUILD_ENVS ?= CGO_ENABLED=0
GO_BUILD_TAGS ?= ""
GO_CC_FLAGS ?= ""

GOOS ?= linux
GOARCH ?= amd64

define BUILD_template =
.PHONY: build-$(1)
build-$(1): $$(BUILD_PREREQ) build-dir
	$$(GO_BUILD_ENVS) GOOS=$$(GOOS) GOARCH=$$(GOARCH) go build -tags $$(GO_BUILD_TAGS) $$(GO_CC_FLAGS) -o "$$(BUILD_DIR)/$(1)" "./cmd/$(1)"
endef

$(foreach cmd,$(CMDS),$(eval $(call BUILD_template,$(cmd))))

.PHONY: build
build: $(BUILD_PREREQ) build-dir $(foreach cmd,$(CMDS),build-$(cmd))

.PHONY: build-cgo
build-cgo: $(BUILD_PREREQ)
	docker run --name build-cgo --rm \
		-v ./:/build \
		-v ./docker/cache/go:/go \
		-v ./.docker/cache/go-build:/root/.cache/go-build \
		--workdir /build golang:buster \
		sh -c 'git config --global url."https://gitlab-ci-token:$(GITLAB_ACCESS_TOKEN)@git.moderntv.eu/".insteadOf "https://git.moderntv.eu/"; BUILD_PREREQ= make build'

.PHONY: build-dir
build-dir:
	mkdir -p "$(BUILD_DIR)"

bin/k6:
	go run go.k6.io/xk6/cmd/xk6 build v0.41.0 \
		--output bin/k6

.PHONY: build-clean
build-clean: build-clean-cgo-cache
	rm -rf "$(BUILD_DIR)"

.PHONY: build-clean-cgo-cache
build-clean-cgo-cache:
	rm -rf ./docker/cache
