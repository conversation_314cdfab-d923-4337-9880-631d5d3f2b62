.PHONY: all clean test

SHELL := /bin/bash
NEXUS_URL ?= https://nexus.moderntv.dev
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo 'latest')
DEBIAN_VERSION = $(shell echo $(VERSION) | sed 's/-/~/')
BUILD_DISTRIBUTIONS ?= buster bullseye bookworm
PACKAGES ?= $(CMDS)

BUILD_DIR ?= bin
DEPLOYMENT_DIR ?= deployment
PACKAGE_DIR ?= build
DATE ?= $(shell date +'%a, %d %b %Y %T %z')
OPT_DIR_SEPARATOR ?= /

define PACKAGE_template =
ifeq ($$(PACKAGE_DIR),)
	$(1)_PACKAGE_DIR = $(1)_pkg_build
else
	$(1)_PACKAGE_DIR = $$(PACKAGE_DIR)/$(1)_pkg
endif \

ifeq ($$(NAMESPACE),)
	$(1)_OPT_BIN_PATH = $$($(1)_PACKAGE_DIR)/opt/$$(PROJECT_NAME)/bin
else ifeq ($$(NAMESPACE), websystem)
	$(1)_OPT_BIN_PATH = $$($(1)_PACKAGE_DIR)/opt/moderntv$$(OPT_DIR_SEPARATOR)$$(PROJECT_NAME)/bin
else ifeq ($$(NAMESPACE), mcloud)
	$(1)_OPT_BIN_PATH = $$($(1)_PACKAGE_DIR)/opt/mcloud/bin
else
	$(1)_OPT_BIN_PATH = $$($(1)_PACKAGE_DIR)/opt/$$(NAMESPACE)/$(1)/bin
endif \

.PHONY: package-$(1)
package-$(1): $$(PACKAGE_PREREQ) package-dir
	@echo ">>> Preparing environment"
	rm -rf $$($(1)_PACKAGE_DIR)
	cp -rp $$(DEPLOYMENT_DIR)/$(1)_pkg $$($(1)_PACKAGE_DIR)
	@echo "================================="
	@echo ">>> Preparing package content"
	mkdir -p $$($(1)_OPT_BIN_PATH)
ifneq ($($(1)_BIN_FILES),)
	cp -r $$($(1)_BIN_FILES) $$($(1)_OPT_BIN_PATH)
else
	cp $$(BUILD_DIR)/$(1) $$($(1)_OPT_BIN_PATH)/$(1)
endif
	sed -i "s/VERSION/$$(DEBIAN_VERSION)/g" "$$($(1)_PACKAGE_DIR)/debian/changelog"
	sed -i "s/DATE/$$(DATE)/g" "$$($(1)_PACKAGE_DIR)/debian/changelog"
	@echo "================================="
	@echo ">>> Building package"
	cd $$($(1)_PACKAGE_DIR) && dpkg-buildpackage -d -rfakeroot
	@echo "================================="
endef

$(foreach pkg, $(PACKAGES), $(eval $(call PACKAGE_template,$(pkg))))

define PACKAGE_UPLOAD_DIST_template =
ifeq ($$(NAMESPACE),)
	$(1)_PACKAGE_FILE = $$(PROJECT_NAME)-$(1)_$(DEBIAN_VERSION)_amd64.deb
else ifeq ($$(NAMESPACE), websystem)
	ifeq ($(PROJECT_NAME), $(1))
		$(1)_PACKAGE_FILE = moderntv-$(1)_$(DEBIAN_VERSION)_amd64.deb
	else
		$(1)_PACKAGE_FILE = moderntv-$(PROJECT_NAME)-$(1)_$(DEBIAN_VERSION)_amd64.deb
	endif
else ifeq ($$(NAMESPACE), mcloud)
	$(1)_PACKAGE_FILE = mcloud-$(1)_$(DEBIAN_VERSION)_amd64.deb
else 
	$(1)_PACKAGE_FILE = $(NAMESPACE)-$(1)_$(DEBIAN_VERSION)_amd64.deb
endif \

.PHONY: package-upload-$(1)-$(2)
package-upload-$(1)-$(2):
	@if [[ "$(DEBIAN_VERSION)" =~ "~" ]]; then \
		echo "Uploading $(1) service package to repository moderntv-debian-$(2)-testing"; \
		curl \
			-u "$(NEXUS_GITLAB_USER):$(NEXUS_GITLAB_PASSWORD)" \
			-H "Content-Type: multipart/form-data" \
			--data-binary "@$$($(1)_PACKAGE_DIR)/../$$($(1)_PACKAGE_FILE)" \
			$(NEXUS_URL)/repository/moderntv-debian-$(2)-testing/; \
	else \
		echo "Uploading $(1) service package to repository moderntv-debian-$(2)"; \
		curl \
			-u "$(NEXUS_GITLAB_USER):$(NEXUS_GITLAB_PASSWORD)" \
			-H "Content-Type: multipart/form-data" \
			--data-binary "@$$($(1)_PACKAGE_DIR)/../$$($(1)_PACKAGE_FILE)" \
			$(NEXUS_URL)/repository/moderntv-debian-$(2)/; \
	fi;
endef

$(foreach pkg, $(PACKAGES), $(foreach dist, $(BUILD_DISTRIBUTIONS), $(eval $(call PACKAGE_UPLOAD_DIST_template,$(pkg),$(dist)))))

.PHONY: package
package: $(PACKAGE_PREREQ) package-dir $(foreach pkg,$(PACKAGES),package-$(pkg))

.PHONY: package-dir
package-dir:
ifneq ($(PACKAGE_DIR),)
	mkdir -p "$(PACKAGE_DIR)"
endif

.PHONY: package-upload-check
package-upload-check:
	@if [ -z "${NEXUS_GITLAB_USER}" ]; then echo "Missing NEXUS_GITLAB_USER variable" && exit 255; fi;
	@if [ -z "${NEXUS_GITLAB_PASSWORD}" ]; then echo "Missing NEXUS_GITLAB_PASSWORD variable" && exit 255; fi;

.PHONY: package-upload
package-upload: package-upload-check package $(foreach pkg,$(PACKAGES), $(foreach dist, $(BUILD_DISTRIBUTIONS), package-upload-$(pkg)-$(dist)))

.PHONY: package-clean
package-clean:
ifeq ($(PACKAGE_DIR),)
	rm -rf $(foreach pkg,$(PACKAGES), $(pkg)_pkg_build)
else
	rm -rf $(foreach pkg,$(PACKAGES), $(PACKAGE_DIR)/$(pkg)_pkg)
endif
