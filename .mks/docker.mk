.PHONY: all clean test

CONTAINERS ?= $(CMDS)
REGISTRY ?= "registry.moderntv.eu/"
DATE_VERSION = $(shell date -u +"%Y%m%d%H%M%S")

define DOCKER_RELEASE_CMD_template
.PHONY: docker-release-$(1)
docker-release-$(1):
	docker tag $$(PROJECT_NAME)-$(1):latest $$(REGISTRY)/$(1):${DOCKER_APP_VERSION}
	docker push $$(REGISTRY)/$(1):${DOCKER_APP_VERSION}
	docker tag $$(PROJECT_NAME)-$(1):latest $$(REGISTRY)/$(1):${DOCKER_APP_VERSION}-${DATE_VERSION}
	docker push $$(REGISTRY)/$(1):${DOCKER_APP_VERSION}-${DATE_VERSION}
endef

$(foreach ctr,$(CONTAINERS),$(eval $(call DOCKER_RELEASE_CMD_template,$(ctr),$(env))))

define DOCKER_BUILD_CMD_template =
.PHONY: docker-build-$(1)
docker-build-$(1):
	docker compose -f docker-compose.build.yml build $(1)
endef

$(foreach ctr,$(CONTAINERS),$(eval $(call DOCKER_BUILD_CMD_template,$(ctr),$(env))))

.PHONY: docker-build
docker-build:
	docker compose -f docker-compose.build.yml build

.PHONY: docker-release
docker-release: $(foreach ctr,$(CONTAINERS),docker-release-$(ctr))
