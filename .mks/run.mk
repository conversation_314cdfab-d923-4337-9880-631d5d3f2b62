.PHONY: all clean test

define RUN_template =
.PHONY: run-$(1)
run-$(1): $$(RUN_PREREQ)
	# CGO_ENABLED must always be enabled as it is a requirement for -race.
	#
	# For more information why `-buildvcs=false` is used see this bug:
	#	https://moderntv.atlassian.net/browse/BACK-626
	#
	CGO_ENABLED=1 go build -tags $$(GO_BUILD_TAGS) -buildvcs=false -race $$(GO_CC_FLAGS) -o "$$(BUILD_DIR)/$$(CMD_BIN_FILE)" "./cmd/$(1)"
	$$(BUILD_DIR)/$(1) $$(ARGS)
endef

$(foreach cmd,$(CMDS),$(eval $(call RUN_template,$(cmd))))
