SHELL := /bin/bash

PROJECT_NAME = multicast-probe
NAMESPACE = mcloud
CMDS ?= management relay sender

REGISTRY ?= registry.moderntv.eu/mcloud/multicast-probe
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo '0.0.0-dev')

all: build

GO_LD_FLAGS = -X main.Version=${VERSION}
GO_CC_FLAGS = -buildvcs=false -ldflags "$(GO_LD_FLAGS)"
BUILD_PREREQ ?= generate-docs
include .mks/build.mk

PACKAGES ?= management relay
include .mks/package.mk

include .mks/run.mk

include .mks/dev.mk

CONTAINERS ?= management relay
include .mks/docker.mk

include .mks/test.mk

include .mks/lint.mk

include .mks/generate.mk

include .mks/update.mk

clean: build-clean package-clean docs-clean dev-clean
	rm -rf $(shell find proto -type f -name '*.pb.go') \
		$(shell find proto -type f -name '*.pb.gw.go') \
		$(shell find proto -type f -name '*.swagger.json') \
	rm -rf sonar-project.properties cp.out
