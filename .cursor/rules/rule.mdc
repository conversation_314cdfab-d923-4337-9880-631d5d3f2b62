---
alwaysApply: true
---

# Multicast Probe Project Rules

## Overview
This document defines the architectural and coding standards for the Multicast Probe project, which follows Domain-Driven Design (DDD) and Clean Architecture principles.

## Architecture Rules

### 1. Bounded Contexts
- **Management Context**: Handles user management, configuration, scanning, and stream aggregation
- **Relay Context**: Handles low-level stream processing and multicast listening
- Each bounded context MUST be completely separated in the `internal/` directory
- NO direct dependencies between bounded contexts - communication only through defined interfaces (gRPC, REST)

### 2. Layer Structure (Clean Architecture)
Each bounded context MUST follow this exact layer structure:

```
internal/{context}/
├── api/                    # Presentation Layer
│   ├── grpc/              # gRPC servers and mappers
│   └── rest/              # REST controllers, DTOs, middleware
├── application/           # Application Layer
│   ├── command/           # Command handlers (write operations)
│   ├── query/             # Query handlers (read operations)
│   ├── interfaces/        # Service interfaces
│   └── services/          # Application services
├── domain/                # Domain Layer
│   ├── entity/            # Domain entities
│   ├── value/             # Value objects
│   ├── repository/        # Repository interfaces
│   └── services/          # Domain services
└── infrastructure/        # Infrastructure Layer
    ├── persistence/       # Database implementations
    ├── clients/           # External service clients
    └── network/           # Network-specific implementations
```

### 3. Dependency Direction Rules
- **Domain Layer**: MUST NOT depend on any other layers
- **Application Layer**: Can depend ONLY on Domain Layer
- **Infrastructure Layer**: Can depend on Domain and Application layers
- **API Layer**: Can depend on Application layer (not directly on Domain)

### 4. Interface Segregation
- All dependencies MUST be injected via interfaces
- Interfaces MUST be defined in the layer that uses them
- Repository interfaces MUST be defined in `domain/repository/`
- Service interfaces MUST be defined in `application/interfaces/`

## File Organization Rules

### 1. Naming Conventions
- **Files**: snake_case (e.g., `user_service.go`, `scan_repository.go`)
- **Directories**: snake_case
- **Packages**: lowercase, single word when possible
- **Structs**: PascalCase
- **Interfaces**: PascalCase ending with interface purpose (e.g., `UserService`, `ScanRepository`)

### 2. Entity Rules
- **Entities**: Must be in `domain/entity/` directory
- Must have business logic and behavior
- Must be anemic data containers with methods
- Example: `User`, `Scan`, `Stream`, `Configuration`

### 3. Value Object Rules
- **Value Objects**: Must be in `domain/value/` directory
- Must be immutable
- Must have equality comparison
- Examples: `Address`, `UNIXTime`, `ScanParameters`, `StreamMetrics`

### 4. Repository Rules
- **Interfaces**: Must be in `domain/repository/`
- **Implementations**: Must be in `infrastructure/persistence/{db_type}/`
- Must use dependency injection
- Must return domain entities and value objects

### 5. Service Rules
- **Application Services**: Must be in `application/services/`
- **Domain Services**: Must be in `domain/services/`
- Must use dependency injection
- Must handle business logic coordination

## CQRS Rules

### 1. Command Structure
- **Commands**: Must be in `application/command/`
- Must represent write operations
- Must have corresponding CommandResult
- Must be handled by application services

### 2. Query Structure
- **Queries**: Must be in `application/query/`
- Must represent read operations
- Must have corresponding QueryResult
- Must be handled by application services

### 3. Command/Query Separation
- Commands and Queries MUST be separate structs
- NO mixing of read and write operations
- Each command/query must have a single responsibility

## API Layer Rules

### 1. REST API Rules
- **Controllers**: Must be in `api/rest/controllers/`
- **DTOs**: Must be in `api/rest/dto/request/` and `api/rest/dto/response/`
- **Middleware**: Must be in `api/rest/middleware/`
- Controllers MUST NOT contain business logic
- Controllers MUST use application services

### 2. gRPC API Rules
- **Servers**: Must be in `api/grpc/servers/`
- **Mappers**: Must be in `api/grpc/mappers/`
- Servers MUST NOT contain business logic
- Servers MUST use application services

### 3. DTO Rules
- DTOs MUST be separate from domain entities
- DTOs MUST be in API layer
- NO domain entities in API responses

## Testing Rules

### 1. Test Structure
- **Unit Tests**: Must be in same directory as tested code
- **Integration Tests**: Must be in `internal/integration/`
- **Mocks**: Must be in `{package}/mocks/` directory
- Test files MUST end with `_test.go`

### 2. Mock Rules
- ALL external dependencies MUST be mocked
- Use `go tool mockgen` for generating mocks
- Mock generation MUST be defined in interfaces with `//go:generate` comments
- Mocks MUST be in `mocks/` subdirectory

### 3. Test Patterns
- Use table-driven tests for multiple scenarios
- Use `t.Parallel()` for parallel test execution
- Use `testify/assert` and `testify/require` for assertions
- Use `testify/suite` for complex test scenarios

## Configuration Rules

### 1. Config Structure
- **Base Config**: Must be in `internal/config/base.go`
- **Context-Specific Config**: Must be in `internal/config/{context}.go`
- **Config Interface**: Must implement `Config` interface
- Must support environment variables via `mapstructure` tags

### 2. Config Patterns
- Use `viper` for configuration management
- Support both file and environment variable configuration
- Use `PostLoad()` for config validation
- Use structured config with nested structs

## Database Rules

### 1. Repository Pattern
- ALL database access MUST go through repository interfaces
- Repository implementations MUST be in `infrastructure/persistence/{db_type}/`
- Use proper transaction management
- Handle errors appropriately (e.g., `ErrKeyNotFound` → `ErrUserNotFound`)

### 2. BadgerDB Rules
- Use proper key prefixes for different entity types
- Implement proper error handling for `badger.ErrKeyNotFound`
- Use proper transaction management (`View`/`Update`)
- Implement proper cleanup via garbage collection

## Build and Development Rules

### 1. Makefile Rules
- Use included makefile modules from `.mks/`
- MUST run `make lint` before commit
- MUST run `make test` before commit
- MUST pass all checks before push

### 2. Version Management
- Use semantic versioning
- Version information MUST be injected via build flags
- Use `git describe --tags --always --dirty` for version generation

### 3. Docker Rules
- Maintain separate Dockerfiles for different environments
- Use multi-stage builds for optimization
- Follow security best practices

## Protocol Buffer Rules

### 1. Proto Files
- **Proto Files**: Must be in `proto/{service}/v{version}/`
- Use proper versioning (v1, v2, etc.)
- Generate Go code using `protoc`
- Generated files MUST be committed

### 2. Service Definition
- Define separate services for different contexts
- Use proper gRPC service patterns
- Include proper documentation

## Documentation Rules

### 1. API Documentation
- **REST API**: Must use Swagger/OpenAPI annotations
- **gRPC API**: Must use proto file documentation
- API docs MUST be generated automatically

### 2. Code Documentation
- **Comments**: Must be in English
- ALL exported functions MUST have documentation
- Use proper Go documentation format

## Error Handling Rules

### 1. Error Patterns
- Use proper error wrapping with `fmt.Errorf`
- Define domain-specific errors in `domain/repository/errors.go`
- Use proper error handling patterns

### 2. Logging
- Use structured logging with `zerolog`
- Include proper context in logs
- Use appropriate log levels

## Dependency Management Rules

### 1. Go Modules
- Use `go.mod` and `go.sum` for dependency management
- Pin major versions
- Regular dependency updates

### 2. External Dependencies
- Minimize external dependencies
- Use well-maintained libraries
- Follow security best practices

## Commit and Git Rules

### 1. Commit Messages
- Use Conventional Commits format
- Examples:
  - `feat(management): add user management endpoint`
  - `fix(relay): resolve stream processing memory leak`
  - `docs(api): update REST API documentation`

### 2. Branch Strategy
- Feature branches for new features
- Use descriptive branch names
- Merge via pull requests

### 3. Pre-commit Checks
- Run `make lint` (MUST pass)
- Run `make test` (MUST pass)  
- Commit and push only after all checks pass

## Security Rules

### 1. Authentication
- Use JWT tokens for authentication
- Implement proper token validation
- Use secure token generation

### 2. Data Protection
- NO sensitive data in logs
- Use proper encryption for sensitive data
- Follow security best practices

## Performance Rules

### 1. Database Performance
- Implement proper BadgerDB garbage collection
- Use appropriate key design
- Monitor database performance

### 2. Stream Processing
- Use proper buffer sizes for channels
- Implement proper cleanup for goroutines
- Monitor memory usage

## Monitoring Rules

### 1. Metrics
- Use Prometheus for metrics collection
- Implement proper metric naming
- Monitor key performance indicators

### 2. Health Checks
- Implement proper health check endpoints
- Include dependency health checks
- Use proper status codes

## Deployment Rules

### 1. Packaging
- Use proper Debian packaging
- Include systemd service files
- Follow packaging best practices

### 2. Configuration Management
- Use proper configuration management
- Support different environments
- Use secure configuration practices

---

## Enforcement

These rules MUST be followed in all new code and existing code SHOULD be refactored to follow these rules over time. Code reviews MUST enforce these rules.

## Examples

See existing codebase for examples of proper implementation of these rules. Key examples:
- User management in `internal/management/`
- Stream processing in `internal/relay/`
- Configuration management in `internal/config/`
- Repository pattern in `internal/*/infrastructure/persistence/`
