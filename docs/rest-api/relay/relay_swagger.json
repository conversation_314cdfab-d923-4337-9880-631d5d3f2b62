{"swagger": "2.0", "info": {"description": "Low level stream processing service.", "title": "Multicase Probe Relay Service", "contact": {}, "version": "1.0"}, "paths": {"/_/apidoc": {"get": {"description": "Returns OpenAPI specification", "tags": ["common"], "summary": "OpenAPI specification", "operationId": "apidoc", "responses": {"200": {"description": "OK", "schema": {}}}}}, "/udp/{address}": {"get": {"description": "Adds a receiver for the stream identified by multicast address\nand writes source stream data as a progressive stream.", "consumes": ["application/json"], "produces": ["video/mp2t"], "tags": ["streams"], "summary": "Progressively stream multicast data", "operationId": "getStreamUDP", "parameters": [{"type": "string", "description": "Stream address in format multicast_ip:port (e.g., *********:5000)", "name": "address", "in": "path", "required": true}], "responses": {"200": {"description": "Progressive stream data in MPEG-TS format", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/response.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.ErrorResponse"}}}}}, "/udp/{address}/pid/{programId}": {"get": {"description": "Adds a receiver for the stream identified by multicast address, filters packets by program ID,\nand writes source stream data as a progressive stream.", "produces": ["video/mp2t"], "tags": ["streams"], "summary": "Progressively stream program multicast data", "operationId": "getStreamUDPProgram", "parameters": [{"type": "string", "description": "Stream address in format multicast_ip:port (e.g., *********:5000)", "name": "address", "in": "path", "required": true}, {"type": "integer", "description": "Program ID to filter the stream (e.g., 423)", "name": "programId", "in": "path", "required": true}], "responses": {"200": {"description": "Progressive stream data in MPEG-TS format filtered by program ID", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/response.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/response.ErrorResponse"}}}}}}, "definitions": {"response.ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}}}}}