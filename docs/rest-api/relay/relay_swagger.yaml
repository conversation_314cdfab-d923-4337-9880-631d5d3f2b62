definitions:
  response.ErrorResponse:
    properties:
      error:
        type: string
    type: object
info:
  contact: {}
  description: Low level stream processing service.
  title: Multicase Probe Relay Service
  version: "1.0"
paths:
  /_/apidoc:
    get:
      description: Returns OpenAPI specification
      operationId: apidoc
      responses:
        "200":
          description: OK
          schema: {}
      summary: OpenAPI specification
      tags:
      - common
  /udp/{address}:
    get:
      consumes:
      - application/json
      description: |-
        Adds a receiver for the stream identified by multicast address
        and writes source stream data as a progressive stream.
      operationId: getStreamUDP
      parameters:
      - description: Stream address in format multicast_ip:port (e.g., *********:5000)
        in: path
        name: address
        required: true
        type: string
      produces:
      - video/mp2t
      responses:
        "200":
          description: Progressive stream data in MPEG-TS format
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.ErrorResponse'
      summary: Progressively stream multicast data
      tags:
      - streams
  /udp/{address}/pid/{programId}:
    get:
      description: |-
        Adds a receiver for the stream identified by multicast address, filters packets by program ID,
        and writes source stream data as a progressive stream.
      operationId: getStreamUDPProgram
      parameters:
      - description: Stream address in format multicast_ip:port (e.g., *********:5000)
        in: path
        name: address
        required: true
        type: string
      - description: Program ID to filter the stream (e.g., 423)
        in: path
        name: programId
        required: true
        type: integer
      produces:
      - video/mp2t
      responses:
        "200":
          description: Progressive stream data in MPEG-TS format filtered by program
            ID
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.ErrorResponse'
      summary: Progressively stream program multicast data
      tags:
      - streams
swagger: "2.0"
