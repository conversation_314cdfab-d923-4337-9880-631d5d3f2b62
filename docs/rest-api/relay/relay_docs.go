// Package relay Code generated by swaggo/swag. DO NOT EDIT
package relay

import "github.com/swaggo/swag"

const docTemplaterelay = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/_/apidoc": {
            "get": {
                "description": "Returns OpenAPI specification",
                "tags": [
                    "common"
                ],
                "summary": "OpenAPI specification",
                "operationId": "apidoc",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {}
                    }
                }
            }
        },
        "/udp/{address}": {
            "get": {
                "description": "Adds a receiver for the stream identified by multicast address\nand writes source stream data as a progressive stream.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "video/mp2t"
                ],
                "tags": [
                    "streams"
                ],
                "summary": "Progressively stream multicast data",
                "operationId": "getStreamUDP",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Stream address in format multicast_ip:port (e.g., *********:5000)",
                        "name": "address",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Progressive stream data in MPEG-TS format",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/udp/{address}/pid/{programId}": {
            "get": {
                "description": "Adds a receiver for the stream identified by multicast address, filters packets by program ID,\nand writes source stream data as a progressive stream.",
                "produces": [
                    "video/mp2t"
                ],
                "tags": [
                    "streams"
                ],
                "summary": "Progressively stream program multicast data",
                "operationId": "getStreamUDPProgram",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Stream address in format multicast_ip:port (e.g., *********:5000)",
                        "name": "address",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Program ID to filter the stream (e.g., 423)",
                        "name": "programId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Progressive stream data in MPEG-TS format filtered by program ID",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/response.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "response.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInforelay holds exported Swagger Info so clients can modify it
var SwaggerInforelay = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Multicase Probe Relay Service",
	Description:      "Low level stream processing service.",
	InfoInstanceName: "relay",
	SwaggerTemplate:  docTemplaterelay,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInforelay.InstanceName(), SwaggerInforelay)
}
