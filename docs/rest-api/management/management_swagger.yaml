definitions:
  dto.Configuration:
    properties:
      scanChannelSize:
        type: integer
      scanCleanupInterval:
        description: milliseconds
        type: integer
      scanDelay:
        description: milliseconds
        type: integer
      scanInterval:
        description: milliseconds
        type: integer
      scanMaxTime:
        description: milliseconds
        type: integer
      scanRetention:
        description: milliseconds
        type: integer
      scansPerSecond:
        type: integer
    type: object
  dto.RelayConfiguration:
    properties:
      cleanupDelay:
        description: milliseconds
        type: integer
      connectionTimeout:
        description: milliseconds
        type: integer
      dispatcherBufferSize:
        type: integer
      joinBufferSize:
        type: integer
      joinsPerSecond:
        type: integer
      networkInterfaces:
        items:
          $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.NetworkInterface'
        type: array
      scanTimeout:
        description: milliseconds
        type: integer
      streamChannelBufferSize:
        type: integer
      udpPacketSize:
        type: integer
    type: object
  entity.AnonymousUser:
    properties:
      password:
        type: string
      username:
        type: string
    type: object
  entity.Scan:
    properties:
      createdAt:
        $ref: '#/definitions/value.UNIXTime'
      id:
        type: string
      parameters:
        $ref: '#/definitions/value.ScanParameters'
      status:
        $ref: '#/definitions/entity.ScanStatus'
      streams:
        items:
          $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream'
        type: array
    type: object
  entity.ScanStatus:
    enum:
    - queued
    - processing
    - completed
    - failed
    - cancelled
    type: string
    x-enum-varnames:
    - ScanStatusQueued
    - ScanStatusProcessing
    - ScanStatusCompleted
    - ScanStatusFailed
    - ScanStatusCancelled
  entity.User:
    properties:
      password:
        type: string
      username:
        type: string
      uuid:
        type: string
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_CancelScanResponse:
    properties:
      data:
        $ref: '#/definitions/response.CancelScanResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_DeleteUserResponse:
    properties:
      data:
        $ref: '#/definitions/response.DeleteUserResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetAuthStatusResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetAuthStatusResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetConfigurationsResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetConfigurationsResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScanResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetScanResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScansResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetScansResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetStreamResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamsResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetStreamsResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUserResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetUserResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUsersResponse:
    properties:
      data:
        $ref: '#/definitions/response.GetUsersResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchConfigurationsResponse:
    properties:
      data:
        $ref: '#/definitions/response.PatchConfigurationsResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchUserResponse:
    properties:
      data:
        $ref: '#/definitions/response.PatchUserResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthLoginResponse:
    properties:
      data:
        $ref: '#/definitions/response.PostAuthLoginResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthRefreshResponse:
    properties:
      data:
        $ref: '#/definitions/response.PostAuthRefreshResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostScansResponse:
    properties:
      data:
        $ref: '#/definitions/response.PostScansResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostUsersResponse:
    properties:
      data:
        $ref: '#/definitions/response.PostUsersResponse'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse:
    properties:
      error:
        type: string
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_entity.Program:
    properties:
      metrics:
        $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.ProgramMetrics'
      programId:
        type: integer
      provider:
        type: string
      title:
        type: string
      tracks:
        items:
          $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Track'
        type: array
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream:
    properties:
      address:
        $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.Address'
      clients:
        type: integer
      metrics:
        $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.StreamMetrics'
      programs:
        items:
          $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Program'
        type: array
      scanError:
        type: string
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_entity.Track:
    properties:
      description:
        type: string
      metrics:
        $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.TrackMetrics'
      pid:
        type: integer
      type:
        type: integer
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_value.Address:
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_value.NetworkInterface:
    properties:
      name:
        type: string
      prefix:
        $ref: '#/definitions/netip.Prefix'
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_value.ProgramMetrics:
    properties:
      discontinuities:
        type: integer
      receivedBytes:
        type: integer
      receivedTsPackets:
        type: integer
      receivers:
        type: integer
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_value.StreamMetrics:
    properties:
      discontinuities:
        type: integer
      missingUdpPackets:
        type: integer
      presentDuration:
        type: integer
      receivedBytes:
        type: integer
      receivedTsPackets:
        type: integer
      receivedUdpPackets:
        type: integer
      receivers:
        type: integer
      receivingDuration:
        type: integer
    type: object
  git_moderntv_eu_multicast-probe_internal_management_domain_value.TrackMetrics:
    properties:
      discontinuities:
        type: integer
      receivedBytes:
        type: integer
      receivedTsPackets:
        type: integer
    type: object
  netip.Prefix:
    type: object
  request.PatchConfigurationsRequest:
    properties:
      configurationManagement:
        $ref: '#/definitions/dto.Configuration'
      configurationRelay:
        $ref: '#/definitions/dto.RelayConfiguration'
    required:
    - configurationManagement
    - configurationRelay
    type: object
  request.PatchUserRequest:
    properties:
      userID:
        type: string
    required:
    - userID
    type: object
  request.PostAuthLoginRequest:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  request.PostScansRequest:
    properties:
      multicastCidr:
        type: string
      port:
        type: integer
    type: object
  request.PostUsersRequest:
    properties:
      user:
        $ref: '#/definitions/entity.AnonymousUser'
    required:
    - user
    type: object
  response.CancelScanResponse:
    properties:
      id:
        type: string
    type: object
  response.DeleteUserResponse:
    type: object
  response.GetAuthStatusResponse:
    type: object
  response.GetConfigurationsResponse:
    properties:
      configurationManagement:
        $ref: '#/definitions/dto.Configuration'
      configurationRelay:
        $ref: '#/definitions/dto.RelayConfiguration'
    type: object
  response.GetScanResponse:
    properties:
      scan:
        $ref: '#/definitions/entity.Scan'
    type: object
  response.GetScansResponse:
    properties:
      scans:
        items:
          $ref: '#/definitions/entity.Scan'
        type: array
    type: object
  response.GetStreamResponse:
    properties:
      stream:
        $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream'
    type: object
  response.GetStreamsResponse:
    properties:
      streams:
        items:
          $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream'
        type: array
    type: object
  response.GetUserResponse:
    properties:
      user:
        $ref: '#/definitions/entity.User'
    type: object
  response.GetUsersResponse:
    properties:
      users:
        items:
          $ref: '#/definitions/entity.User'
        type: array
    type: object
  response.PatchConfigurationsResponse:
    type: object
  response.PatchUserResponse:
    properties:
      user:
        $ref: '#/definitions/entity.User'
    type: object
  response.PostAuthLoginResponse:
    properties:
      token:
        type: string
    type: object
  response.PostAuthRefreshResponse:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  response.PostScansResponse:
    properties:
      scanId:
        type: string
    type: object
  response.PostUsersResponse:
    properties:
      user:
        $ref: '#/definitions/entity.User'
    type: object
  value.ScanParameters:
    properties:
      multicastCidr:
        type: string
      port:
        type: integer
    type: object
  value.UNIXTime:
    properties:
      time.Time:
        type: string
    type: object
info:
  contact: {}
  description: Management service exposing REST API.
  title: Multicase Probe Management Service
  version: "1.0"
paths:
  /_/apidoc:
    get:
      description: Returns OpenAPI specification
      operationId: apidoc
      responses:
        "200":
          description: OK
          schema: {}
      summary: OpenAPI specification
      tags:
      - common
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      operationId: postAuthLogin
      parameters:
      - description: User credentials
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/request.PostAuthLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthLoginResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      summary: Logs in user
      tags:
      - auth
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      operationId: postAuthRefresh
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthRefreshResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Refreshes token
      tags:
      - auth
  /api/v1/auth/status:
    get:
      consumes:
      - application/json
      operationId: getAuthStatus
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetAuthStatusResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Validates correct authentication
      tags:
      - auth
  /api/v1/configurations:
    get:
      consumes:
      - application/json
      operationId: getConfigurations
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetConfigurationsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets list of configurations
      tags:
      - configurations
    patch:
      consumes:
      - application/json
      operationId: patchConfigurations
      parameters:
      - description: Patched configuration
        in: body
        name: configuration
        required: true
        schema:
          $ref: '#/definitions/request.PatchConfigurationsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchConfigurationsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Updates configurations
      tags:
      - configurations
  /api/v1/scans:
    get:
      consumes:
      - application/json
      operationId: getScans
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScansResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets list of scans
      tags:
      - scans
    post:
      consumes:
      - application/json
      operationId: postScans
      parameters:
      - description: Scan
        in: body
        name: scan
        required: true
        schema:
          $ref: '#/definitions/request.PostScansRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostScansResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Creates scans
      tags:
      - scans
  /api/v1/scans/{scanId}:
    delete:
      consumes:
      - application/json
      operationId: cancelScan
      parameters:
      - description: Scan ID
        in: path
        name: scanId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_CancelScanResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      tags:
      - scans
    get:
      consumes:
      - application/json
      operationId: getScan
      parameters:
      - description: Scan ID
        in: path
        name: scanId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScanResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      tags:
      - scans
  /api/v1/streams:
    get:
      consumes:
      - application/json
      operationId: getStreams
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets list of streams
      tags:
      - streams
  /api/v1/streams/{streamId}:
    get:
      consumes:
      - application/json
      operationId: getStream
      parameters:
      - description: Stream ID
        in: path
        name: streamId
        required: true
        type: string
      - description: Export stream metrics
        in: query
        name: metrics
        type: boolean
      - description: Export stream programs
        in: query
        name: programs
        type: boolean
      - description: Export stream program tracks. It is ignored when programs are
          not exported.
        in: query
        name: tracks
        type: boolean
      - description: ID of stream represented by IP address and port combination
        in: path
        name: streamId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets stream
      tags:
      - streams
  /api/v1/users:
    get:
      consumes:
      - application/json
      operationId: getUsers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUsersResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets list of users
      tags:
      - users
    post:
      consumes:
      - application/json
      operationId: postUsers
      parameters:
      - description: User
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/request.PostUsersRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostUsersResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Creates users
      tags:
      - users
  /api/v1/users/{userId}:
    get:
      consumes:
      - application/json
      operationId: getUser
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Gets user
      tags:
      - users
    patch:
      consumes:
      - application/json
      operationId: deleteUser
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_DeleteUserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Deletes user
      tags:
      - users
securityDefinitions:
  BearerAuth:
    description: |-
      Bearer token authentication.
      The token is JWT and is expected to be in the "Bearer [TOKEN]" format.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
