// Package management Code generated by swaggo/swag. DO NOT EDIT
package management

import "github.com/swaggo/swag"

const docTemplatemanagement = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/_/apidoc": {
            "get": {
                "description": "Returns OpenAPI specification",
                "tags": [
                    "common"
                ],
                "summary": "OpenAPI specification",
                "operationId": "apidoc",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {}
                    }
                }
            }
        },
        "/api/v1/auth/login": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Logs in user",
                "operationId": "postAuthLogin",
                "parameters": [
                    {
                        "description": "User credentials",
                        "name": "credentials",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PostAuthLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthLoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/refresh": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Refreshes token",
                "operationId": "postAuthRefresh",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthRefreshResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/status": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Validates correct authentication",
                "operationId": "getAuthStatus",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetAuthStatusResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/configurations": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "configurations"
                ],
                "summary": "Gets list of configurations",
                "operationId": "getConfigurations",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetConfigurationsResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "configurations"
                ],
                "summary": "Updates configurations",
                "operationId": "patchConfigurations",
                "parameters": [
                    {
                        "description": "Patched configuration",
                        "name": "configuration",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PatchConfigurationsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchConfigurationsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/scans": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scans"
                ],
                "summary": "Gets list of scans",
                "operationId": "getScans",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScansResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scans"
                ],
                "summary": "Creates scans",
                "operationId": "postScans",
                "parameters": [
                    {
                        "description": "Scan",
                        "name": "scan",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PostScansRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostScansResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/scans/{scanId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scans"
                ],
                "operationId": "getScan",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Scan ID",
                        "name": "scanId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScanResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "scans"
                ],
                "operationId": "cancelScan",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Scan ID",
                        "name": "scanId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_CancelScanResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/streams": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "streams"
                ],
                "summary": "Gets list of streams",
                "operationId": "getStreams",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamsResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/streams/{streamId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "streams"
                ],
                "summary": "Gets stream",
                "operationId": "getStream",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Stream ID",
                        "name": "streamId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "description": "Export stream metrics",
                        "name": "metrics",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Export stream programs",
                        "name": "programs",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Export stream program tracks. It is ignored when programs are not exported.",
                        "name": "tracks",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "ID of stream represented by IP address and port combination",
                        "name": "streamId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "Gets list of users",
                "operationId": "getUsers",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUsersResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "Creates users",
                "operationId": "postUsers",
                "parameters": [
                    {
                        "description": "User",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PostUsersRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostUsersResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/users/{userId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "Gets user",
                "operationId": "getUser",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "userId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "Deletes user",
                "operationId": "deleteUser",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "userId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_DeleteUserResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.Configuration": {
            "type": "object",
            "properties": {
                "scanChannelSize": {
                    "type": "integer"
                },
                "scanCleanupInterval": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "scanDelay": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "scanInterval": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "scanMaxTime": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "scanRetention": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "scansPerSecond": {
                    "type": "integer"
                }
            }
        },
        "dto.RelayConfiguration": {
            "type": "object",
            "properties": {
                "cleanupDelay": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "connectionTimeout": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "dispatcherBufferSize": {
                    "type": "integer"
                },
                "joinBufferSize": {
                    "type": "integer"
                },
                "joinsPerSecond": {
                    "type": "integer"
                },
                "networkInterfaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.NetworkInterface"
                    }
                },
                "scanTimeout": {
                    "description": "milliseconds",
                    "type": "integer"
                },
                "streamChannelBufferSize": {
                    "type": "integer"
                },
                "udpPacketSize": {
                    "type": "integer"
                }
            }
        },
        "entity.AnonymousUser": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "entity.Scan": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "$ref": "#/definitions/value.UNIXTime"
                },
                "id": {
                    "type": "string"
                },
                "parameters": {
                    "$ref": "#/definitions/value.ScanParameters"
                },
                "status": {
                    "$ref": "#/definitions/entity.ScanStatus"
                },
                "streams": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream"
                    }
                }
            }
        },
        "entity.ScanStatus": {
            "type": "string",
            "enum": [
                "queued",
                "processing",
                "completed",
                "failed",
                "cancelled"
            ],
            "x-enum-varnames": [
                "ScanStatusQueued",
                "ScanStatusProcessing",
                "ScanStatusCompleted",
                "ScanStatusFailed",
                "ScanStatusCancelled"
            ]
        },
        "entity.User": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_CancelScanResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.CancelScanResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_DeleteUserResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.DeleteUserResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetAuthStatusResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetAuthStatusResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetConfigurationsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetConfigurationsResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScanResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetScanResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetScansResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetScansResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetStreamResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetStreamsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetStreamsResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUserResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetUserResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_GetUsersResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.GetUsersResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchConfigurationsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PatchConfigurationsResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PatchUserResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PatchUserResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthLoginResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PostAuthLoginResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostAuthRefreshResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PostAuthRefreshResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostScansResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PostScansResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.DataResponse-response_PostUsersResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/response.PostUsersResponse"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_api_rest_dto_response.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_entity.Program": {
            "type": "object",
            "properties": {
                "metrics": {
                    "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.ProgramMetrics"
                },
                "programId": {
                    "type": "integer"
                },
                "provider": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "tracks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Track"
                    }
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream": {
            "type": "object",
            "properties": {
                "address": {
                    "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.Address"
                },
                "clients": {
                    "type": "integer"
                },
                "metrics": {
                    "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.StreamMetrics"
                },
                "programs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Program"
                    }
                },
                "scanError": {
                    "type": "string"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_entity.Track": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "metrics": {
                    "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_value.TrackMetrics"
                },
                "pid": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_value.Address": {
            "type": "object"
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_value.NetworkInterface": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "prefix": {
                    "$ref": "#/definitions/netip.Prefix"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_value.ProgramMetrics": {
            "type": "object",
            "properties": {
                "discontinuities": {
                    "type": "integer"
                },
                "receivedBytes": {
                    "type": "integer"
                },
                "receivedTsPackets": {
                    "type": "integer"
                },
                "receivers": {
                    "type": "integer"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_value.StreamMetrics": {
            "type": "object",
            "properties": {
                "discontinuities": {
                    "type": "integer"
                },
                "missingUdpPackets": {
                    "type": "integer"
                },
                "presentDuration": {
                    "type": "integer"
                },
                "receivedBytes": {
                    "type": "integer"
                },
                "receivedTsPackets": {
                    "type": "integer"
                },
                "receivedUdpPackets": {
                    "type": "integer"
                },
                "receivers": {
                    "type": "integer"
                },
                "receivingDuration": {
                    "type": "integer"
                }
            }
        },
        "git_moderntv_eu_multicast-probe_internal_management_domain_value.TrackMetrics": {
            "type": "object",
            "properties": {
                "discontinuities": {
                    "type": "integer"
                },
                "receivedBytes": {
                    "type": "integer"
                },
                "receivedTsPackets": {
                    "type": "integer"
                }
            }
        },
        "netip.Prefix": {
            "type": "object"
        },
        "request.PatchConfigurationsRequest": {
            "type": "object",
            "required": [
                "configurationManagement",
                "configurationRelay"
            ],
            "properties": {
                "configurationManagement": {
                    "$ref": "#/definitions/dto.Configuration"
                },
                "configurationRelay": {
                    "$ref": "#/definitions/dto.RelayConfiguration"
                }
            }
        },
        "request.PatchUserRequest": {
            "type": "object",
            "required": [
                "userID"
            ],
            "properties": {
                "userID": {
                    "type": "string"
                }
            }
        },
        "request.PostAuthLoginRequest": {
            "type": "object",
            "required": [
                "password",
                "username"
            ],
            "properties": {
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "request.PostScansRequest": {
            "type": "object",
            "properties": {
                "multicastCidr": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                }
            }
        },
        "request.PostUsersRequest": {
            "type": "object",
            "required": [
                "user"
            ],
            "properties": {
                "user": {
                    "$ref": "#/definitions/entity.AnonymousUser"
                }
            }
        },
        "response.CancelScanResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                }
            }
        },
        "response.DeleteUserResponse": {
            "type": "object"
        },
        "response.GetAuthStatusResponse": {
            "type": "object"
        },
        "response.GetConfigurationsResponse": {
            "type": "object",
            "properties": {
                "configurationManagement": {
                    "$ref": "#/definitions/dto.Configuration"
                },
                "configurationRelay": {
                    "$ref": "#/definitions/dto.RelayConfiguration"
                }
            }
        },
        "response.GetScanResponse": {
            "type": "object",
            "properties": {
                "scan": {
                    "$ref": "#/definitions/entity.Scan"
                }
            }
        },
        "response.GetScansResponse": {
            "type": "object",
            "properties": {
                "scans": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/entity.Scan"
                    }
                }
            }
        },
        "response.GetStreamResponse": {
            "type": "object",
            "properties": {
                "stream": {
                    "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream"
                }
            }
        },
        "response.GetStreamsResponse": {
            "type": "object",
            "properties": {
                "streams": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/git_moderntv_eu_multicast-probe_internal_management_domain_entity.Stream"
                    }
                }
            }
        },
        "response.GetUserResponse": {
            "type": "object",
            "properties": {
                "user": {
                    "$ref": "#/definitions/entity.User"
                }
            }
        },
        "response.GetUsersResponse": {
            "type": "object",
            "properties": {
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/entity.User"
                    }
                }
            }
        },
        "response.PatchConfigurationsResponse": {
            "type": "object"
        },
        "response.PatchUserResponse": {
            "type": "object",
            "properties": {
                "user": {
                    "$ref": "#/definitions/entity.User"
                }
            }
        },
        "response.PostAuthLoginResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "response.PostAuthRefreshResponse": {
            "type": "object",
            "required": [
                "token"
            ],
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "response.PostScansResponse": {
            "type": "object",
            "properties": {
                "scanId": {
                    "type": "string"
                }
            }
        },
        "response.PostUsersResponse": {
            "type": "object",
            "properties": {
                "user": {
                    "$ref": "#/definitions/entity.User"
                }
            }
        },
        "value.ScanParameters": {
            "type": "object",
            "properties": {
                "multicastCidr": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                }
            }
        },
        "value.UNIXTime": {
            "type": "object",
            "properties": {
                "time.Time": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Bearer token authentication.\nThe token is JWT and is expected to be in the \"Bearer [TOKEN]\" format.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfomanagement holds exported Swagger Info so clients can modify it
var SwaggerInfomanagement = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Multicase Probe Management Service",
	Description:      "Management service exposing REST API.",
	InfoInstanceName: "management",
	SwaggerTemplate:  docTemplatemanagement,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfomanagement.InstanceName(), SwaggerInfomanagement)
}
