# Multicast Probe

## Services

The project currently contains 2 cervices:

- Relay service that scans multicast transmissions and reports discovered content.
- Management service that exposes the discovered content and manages
things like configuration and users etc.

## Techspec

The technical specification can be found in [Confluence](https://moderntv.atlassian.net/wiki/spaces/DEV/pages/3530162184/Multicastov+sonda+techspec).

## Deployment
Je to setupnuté v rámci moderntv_internal inventáře. Stačí jenom play-multicast-probe.sh a nainstaluje to nejnovější verzi Relay i Managementu.

Sondovej server je ttc-mcast1.moderntv.eu