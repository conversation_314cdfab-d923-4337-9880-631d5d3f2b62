---
version: "2"

run:
  tests: true

linters:
  default: all
  disable:
    # restrictive
    - exhaustruct
    - varnamelen
    - nlreturn
    - funlen
    - nonamedreturns
    - gochecknoglobals
    - wsl
    - nakedret
    - err113
    # enable later
    - wrapcheck
    - godox
  settings:
    depguard:
      rules:
        main:
          list-mode: strict
          files:
            - $all
          allow:
            - $gostd
            - git.moderntv.eu
            - github.com/swaggo/swag
            - github.com/moderntv/cadre
            - github.com/gin-gonic/gin
            - github.com/getsentry/sentry-go
            - github.com/sveatlo/zerolog-sentry
            - golang.org/x/net/ipv4
            - google.golang.org/grpc
            - github.com/rs/zerolog
            - github.com/google/uuid
            - go.uber.org/mock/gomock
            - github.com/Comcast/gots
            - github.com/stretchr/testify
            - github.com/dgraph-io/badger
            - github.com/spf13/viper
            - github.com/golang-jwt/jwt
            - github.com/prometheus/client_golang
    dupl:
      threshold: 200
    godot:
      exclude:
        - ^ @ # swaggo annotations
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$

formatters:
  enable:
    - gci
    - gofmt
    - gofumpt
    - goimports
    - golines
  settings:
    golines:
      max-len: 120
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$

issues:
  fix: true

output:
  formats:
    text:
      path: stdout
    tab:
      path: golangci-lint.out
      colors: false
    html:
      path: golangci-lint.out.html
