#!/usr/bin/env python3

import struct
import random
import sys

class MPEGTSGenerator:
    def __init__(self, duration_seconds=30, cc_jumps=25, planned_discontinuities=10, repeated_packets=15):
        self.duration = duration_seconds
        self.cc_jumps = cc_jumps
        self.planned_discontinuities = planned_discontinuities
        self.repeated_packets = repeated_packets
        self.TS_PACKET_SIZE = 188
        
        # PIDs
        self.PAT_PID = 0x0000
        self.PMT_PID = 0x0100
        self.VIDEO_PID = 0x0101
        self.AUDIO_PID = 0x0102
        
        # Continuity counters
        self.pat_cc = 0
        self.pmt_cc = 0
        self.video_cc = 0
        self.audio_cc = 0
        
        # Track discontinuities created
        self.cc_jumps_created = 0
        self.planned_created = 0
        self.repeated_created = 0
        self.packets_written = 0
        
        # Define phases - SEQUENTIAL CREATION
        self.warmup_packets = 100                                           # Normal packets first
        self.cc_jump_start = self.warmup_packets                          # Start CC jumps
        self.cc_jump_end = self.cc_jump_start + (cc_jumps * 10)           # 10 packets per CC jump
        self.planned_start = self.cc_jump_end                             # Start planned after CC jumps
        self.planned_end = self.planned_start + (planned_discontinuities * 10)  # 10 packets per planned
        self.repeated_start = self.planned_end                            # Start repeated after planned
        self.repeated_end = self.repeated_start + (repeated_packets * 10)  # 10 packets per repeated
        
        print(f"Phase plan:")
        print(f"  Warmup: packets 0-{self.warmup_packets}")
        print(f"  CC Jumps: packets {self.cc_jump_start}-{self.cc_jump_end} ({cc_jumps} jumps)")
        print(f"  Planned: packets {self.planned_start}-{self.planned_end} ({planned_discontinuities} planned)")
        print(f"  Repeated: packets {self.repeated_start}-{self.repeated_end} ({repeated_packets} repeated)")
        print()
        
    def create_adaptation_field(self, discontinuity_indicator=False):
        """Create adaptation field with optional discontinuity indicator"""
        adaptation_length = 1
        flags = 0x00
        
        if discontinuity_indicator:
            flags |= 0x80  # Set discontinuity_indicator bit
            
        adaptation_field = struct.pack('BB', adaptation_length, flags)
        return adaptation_field
        
    def create_ts_packet(self, pid, payload, pusi=False, adaptation_field=None, force_repeat_cc=False):
        """Create a Transport Stream packet"""
        sync_byte = 0x47
        pid_flags = (0 << 15) | (int(pusi) << 14) | (0 << 13) | pid
        
        # Get continuity counter for this PID
        if pid == self.PAT_PID:
            cc = self.pat_cc
            if not force_repeat_cc:
                self.pat_cc = (self.pat_cc + 1) % 16
        elif pid == self.PMT_PID:
            cc = self.pmt_cc
            if not force_repeat_cc:
                self.pmt_cc = (self.pmt_cc + 1) % 16
        elif pid == self.VIDEO_PID:
            cc = self.video_cc
            if not force_repeat_cc:
                self.video_cc = (self.video_cc + 1) % 16
        elif pid == self.AUDIO_PID:
            cc = self.audio_cc
            if not force_repeat_cc:
                self.audio_cc = (self.audio_cc + 1) % 16
        else:
            cc = 0
            
        # Adaptation field control
        if adaptation_field is None:
            adaptation_control = 1  # payload only
        else:
            adaptation_control = 3  # adaptation field + payload
            
        flags_cc = (0 << 6) | (adaptation_control << 4) | cc
        
        # Build packet
        header = struct.pack('>BHB', sync_byte, pid_flags, flags_cc)
        packet_data = header
        
        if adaptation_field:
            packet_data += adaptation_field
            
        if payload:
            available_space = self.TS_PACKET_SIZE - len(packet_data)
            if len(payload) > available_space:
                payload = payload[:available_space]
            packet_data += payload
            
        # Pad to 188 bytes
        padding_needed = self.TS_PACKET_SIZE - len(packet_data)
        if padding_needed > 0:
            packet_data += b'\xFF' * padding_needed
            
        self.packets_written += 1
        return packet_data[:self.TS_PACKET_SIZE]
    
    def create_pat(self):
        """Create Program Association Table"""
        table_id = 0x00
        section_syntax = 0xB0
        section_length = 13
        
        pat_data = struct.pack('>BBH', table_id, section_syntax, section_length)
        pat_data += struct.pack('>HBBH', 0x0001, 0xC1, 0x00, 0x0001)
        pat_data += struct.pack('>HH', 0x0001, 0xE000 | self.PMT_PID)
        pat_data += b'\x00\x00\x00\x00'  # CRC32 placeholder
        
        return self.create_ts_packet(self.PAT_PID, pat_data, pusi=True)
    
    def create_pmt(self):
        """Create Program Map Table"""
        table_id = 0x02
        section_syntax = 0xB0
        section_length = 18
        
        pmt_data = struct.pack('>BBH', table_id, section_syntax, section_length)
        pmt_data += struct.pack('>HBBB', 0x0001, 0xC1, 0x00, 0x00)
        pmt_data += struct.pack('>HH', 0xE000 | self.VIDEO_PID, 0xF000)
        pmt_data += struct.pack('>BHH', 0x1B, 0xE000 | self.VIDEO_PID, 0xF000)
        pmt_data += b'\x00\x00\x00\x00'  # CRC32 placeholder
        
        return self.create_ts_packet(self.PMT_PID, pmt_data, pusi=True)
    
    def create_video_packet(self, frame_num):
        """Create video packet based on current phase"""
        current_packet = self.packets_written
        
        # Determine which phase we're in and what to create
        adaptation_field = None
        force_repeat_cc = False
        
        # PHASE 1: CC JUMPS (first 1000 video packets)
        if (self.cc_jumps_created < self.cc_jumps and 
            current_packet >= 200 and current_packet < 1200):  # Skip first 200 for PAT/PMT setup
            
            if (current_packet - 200) % 10 == 0:
                self.video_cc = (self.video_cc + random.randint(2, 8)) % 16
                self.cc_jumps_created += 1
                print(f"Created CC Jump #{self.cc_jumps_created} at packet {current_packet}")
                
        # PHASE 2: PLANNED DISCONTINUITIES (next 1000 video packets)
        elif (self.planned_created < self.planned_discontinuities and 
              current_packet >= 1200 and current_packet < 2200):
            
            if (current_packet - 1200) % 10 == 0:
                adaptation_field = self.create_adaptation_field(discontinuity_indicator=True)
                self.planned_created += 1
                print(f"Created Planned Discontinuity #{self.planned_created} at packet {current_packet}")
                
        # PHASE 3: REPEATED PACKETS (next 1000 video packets)  
        elif (self.repeated_created < self.repeated_packets and 
              current_packet >= 2200 and current_packet < 3200):
            
            if (current_packet - 2200) % 10 == 0:
                force_repeat_cc = True
                self.repeated_created += 1
                print(f"Created Repeated Packet #{self.repeated_created} at packet {current_packet}")
        
        # Create payload
        nal_header = b'\x00\x00\x00\x01\x67'
        fake_data = bytes([random.randint(0, 255) for _ in range(120)])
        payload = nal_header + fake_data
        
        return self.create_ts_packet(
            self.VIDEO_PID, 
            payload, 
            pusi=(frame_num % 25 == 0),
            adaptation_field=adaptation_field,
            force_repeat_cc=force_repeat_cc
        )
    
    def generate_stream(self, output_filename):
        """Generate the complete MPEG-TS stream"""
        print(f"Generating MPEG-TS stream:")
        print(f"Duration: {self.duration} seconds")
        print(f"Target discontinuities: {self.cc_jumps + self.planned_discontinuities + self.repeated_packets}")
        print(f"Output: {output_filename}")
        print()
        
        with open(output_filename, 'wb') as f:
            total_packets = max(self.repeated_end + 500, self.duration * 25 * 8)
            
            for i in range(total_packets):
                # Write PAT every 1000 packets
                if i % 1000 == 0:
                    f.write(self.create_pat())
                
                # Write PMT every 1000 packets (offset)
                if i % 1000 == 100:
                    f.write(self.create_pmt())
                
                # Write video packets
                f.write(self.create_video_packet(i))
                
                # Progress indicator
                if i % 5000 == 0:
                    total_created = self.cc_jumps_created + self.planned_created + self.repeated_created
                    progress = (i / total_packets) * 100
                    print(f"Progress: {progress:.1f}% ({total_created} discontinuities created)")
        
        print(f"\n✅ Generated {output_filename}")
        print(f"Total packets: {self.packets_written}")
        print(f"Discontinuities created:")
        print(f"  - CC Jumps: {self.cc_jumps_created}/{self.cc_jumps}")
        print(f"  - Planned: {self.planned_created}/{self.planned_discontinuities}")
        print(f"  - Repeated: {self.repeated_created}/{self.repeated_packets}")
        print(f"  - Total: {self.cc_jumps_created + self.planned_created + self.repeated_created}")
        print(f"\nExpected detectable by app: {self.cc_jumps_created + self.repeated_created} (excluding planned)")

def main():
    # Configuration - CHANGE THESE VALUES
    DURATION_SECONDS = 120          # Length of video
    CC_JUMPS = 100                 # Continuity counter jumps
    PLANNED_DISCONTINUITIES = 0   # Adaptation field discontinuity indicators  
    REPEATED_PACKETS = 0          # Duplicate continuity counter packets
    OUTPUT_FILE = "test.ts"        # Output filename
    
    print("=== MPEG-TS Generator with Sequential Discontinuity Creation ===")
    
    generator = MPEGTSGenerator(
        duration_seconds=DURATION_SECONDS,
        cc_jumps=CC_JUMPS,
        planned_discontinuities=PLANNED_DISCONTINUITIES,
        repeated_packets=REPEATED_PACKETS
    )
    
    generator.generate_stream(OUTPUT_FILE)


if __name__ == "__main__":
    main()
