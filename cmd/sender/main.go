package main

import (
	"errors"
	"flag"
	"io"
	"log"
	"net"
	"os"
	"strconv"
	"time"

	"golang.org/x/net/ipv4"
)

func main() {
	ret := mainRet()
	os.Exit(ret)
}

var (
	ifaceName string
	groupPort string
	filePath  string
)

const (
	// Exit status codes.
	exitSuccess   = 0
	exitFailure   = 1
	exitBadParams = 2

	// Default values.
	defaultTTL        = 3
	defaultPacketSize = 188 // MPEG-TS packet size.
	tsPacketsPerUDP   = 7   // Number of TS packets per UDP packet
	udpPacketSize     = defaultPacketSize * tsPacketsPerUDP
	defaultTickRate   = 1 * time.Millisecond // 1000 packets per second
)

func mainRet() int {
	flag.StringVar(&ifaceName, "i", "", "Interface name")
	flag.StringVar(
		&filePath,
		"file",
		"",
		"Path to file to send (optional, defaults to 'Hello, World!')",
	)
	flag.Parse()
	groupPort = flag.Arg(0)

	if ifaceName == "" || groupPort == "" {
		flag.Usage()
		return exitBadParams
	}
	var reader io.Reader
	if filePath != "" {
		file, err := os.Open(filePath)
		if err != nil {
			log.Printf("Error opening file %s: %v", filePath, err)
			return exitFailure
		}
		defer file.Close()
		reader = file
	} else {
		reader = &helloWorldReader{}
	}

	err := sender(ifaceName, groupPort, reader)
	if err != nil {
		log.Printf("Error: %v", err)
		return exitFailure
	}

	return exitSuccess
}

// helloWorldReader implements io.Reader to send "Hello, World!\n" repeatedly.
type helloWorldReader struct{}

func (r *helloWorldReader) Read(p []byte) (n int, err error) {
	msg := []byte("Hello, World!\n")
	copy(p, msg)
	return len(msg), nil
}

// sender sends data from reader to UDP multicast with rate limiting.
// Error definitions.
var (
	ErrInvalidGroupPort     = errors.New("invalid group:port format")
	ErrInvalidPort          = errors.New("invalid port")
	ErrInvalidInterface     = errors.New("invalid interface")
	ErrFailedUDPListen      = errors.New("failed to listen on UDP")
	ErrFailedSetTTL         = errors.New("failed to set TTL")
	ErrFailedMulticastIface = errors.New("failed to set multicast interface")
	ErrInvalidIPAddress     = errors.New("invalid IP address")
	ErrFailedReadInput      = errors.New("failed to read input")
	ErrFailedSendPacket     = errors.New("failed to send packet")
)

func sender(ifaceName, groupPort string, reader io.Reader) error { //nolint: cyclop
	host, portString, err := net.SplitHostPort(groupPort)
	if err != nil {
		return errors.Join(ErrInvalidGroupPort, err)
	}

	port, err := strconv.Atoi(portString)
	if err != nil {
		return errors.Join(ErrInvalidPort, err)
	}

	iface, err := net.InterfaceByName(ifaceName)
	if err != nil {
		return errors.Join(ErrInvalidInterface, err)
	}

	conn, err := net.ListenPacket("udp", ":0")
	if err != nil {
		return errors.Join(ErrFailedUDPListen, err)
	}
	defer conn.Close()

	packetConn := ipv4.NewPacketConn(conn)
	if err := packetConn.SetTTL(defaultTTL); err != nil {
		return errors.Join(ErrFailedSetTTL, err)
	}
	if err := packetConn.SetMulticastInterface(iface); err != nil {
		return errors.Join(ErrFailedMulticastIface, err)
	}

	groupIP := net.ParseIP(host)
	if groupIP == nil {
		return ErrInvalidIPAddress
	}

	dst := &net.UDPAddr{IP: groupIP, Port: port}
	log.Printf("Sending to %v via %s", dst, ifaceName)

	// Buffer for reading packets.
	buf := make([]byte, udpPacketSize) // Buffer for 7 TS packets
	ticker := time.NewTicker(defaultTickRate)
	defer ticker.Stop()

	for {
		// Read 7 TS packets worth of data
		totalRead := 0
		for totalRead < udpPacketSize {
			n, err := reader.Read(buf[totalRead:])
			if err != nil && !errors.Is(err, io.EOF) {
				return errors.Join(ErrFailedReadInput, err)
			}
			if n == 0 || errors.Is(err, io.EOF) {
				// For Hello World, loop indefinitely; for files, stop at EOF.
				if filePath == "" {
					continue // Keep sending Hello World.
				}
				break // File ended.
			}
			totalRead += n
		}

		// If we didn't read a full UDP packet worth of data, pad with zeros
		if totalRead < udpPacketSize {
			for i := totalRead; i < udpPacketSize; i++ {
				buf[i] = 0
			}
		}

		_, err = packetConn.WriteTo(buf, nil, dst)
		if err != nil {
			return errors.Join(ErrFailedSendPacket, err)
		}

		<-ticker.C // Rate limit.
	}
}
