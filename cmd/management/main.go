package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"io"
	"log/slog"
	"os"
	"time"

	_ "git.moderntv.eu/multicast-probe/docs/rest-api/management"
	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/controllers"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/middleware"
	"git.moderntv.eu/multicast-probe/internal/management/application/services"
	relaygrpc "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client/grpc_relay_client"
	"git.moderntv.eu/multicast-probe/internal/management/infrastructure/metrics"
	"git.moderntv.eu/multicast-probe/internal/management/infrastructure/persistence/badgerdb"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	badger "github.com/dgraph-io/badger/v4"
	sentry "github.com/getsentry/sentry-go"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-gonic/gin"
	"github.com/moderntv/cadre"
	cadremetrics "github.com/moderntv/cadre/metrics"
	"github.com/moderntv/cadre/status"
	"github.com/rs/zerolog"
	zlogsentry "github.com/sveatlo/zerolog-sentry"
	"github.com/swaggo/swag"
)

var Version string

var ErrMissingOpenAPISpec = errors.New("open api spec not generated or has invalid instance name")

const (
	suggestedSentrySampleRate = 0.6
	sentryFlushDuration       = 10 * time.Second
)

//	@title			Multicase Probe Management Service
//	@version		1.0
//	@description	Management service exposing REST API.

//	@securityDefinitions.apikey	BearerAuth
//	@in							header
//	@name						Authorization
//	@description				Bearer token authentication.
//	@description				The token is JWT and is expected to be in the "Bearer [TOKEN]" format.

func main() {
	err := runApp()
	if err != nil {
		slog.Error("failed running app",
			"error", err,
		)
		os.Exit(1)
	}
}

func runApp() error { //nolint: cyclop
	var (
		configFilePath string
		printVersion   bool
	)

	flag.StringVar(&configFilePath, "config", "", "path to config file")
	flag.BoolVar(&printVersion, "version", false, "print version and exit")
	flag.Parse()
	if printVersion {
		fmt.Println("Version: ", Version) //nolint: forbidigo
		return nil
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	conf, err := config.NewManagementConfig(configFilePath)
	if err != nil {
		return fmt.Errorf("app configuration failed: %w", err)
	}

	enableSentry := initSentry(conf.SentryDSN)

	log, err := initZerolog(conf, enableSentry)
	if err != nil {
		return fmt.Errorf("zerolog initialization failed: %w", err)
	}

	metricsRegistry, err := cadremetrics.NewRegistry("multicast_probe_management", nil)
	if err != nil {
		return fmt.Errorf("failed creating prometheus registry: %w", err)
	}

	appStatus := status.NewStatus(Version)

	// ==================================================
	// REAL APPLICATION INITIATION BEGINS HERE
	// ==================================================

	relayGRPCClient, err := relaygrpc.NewClient(log, conf)
	if err != nil {
		return fmt.Errorf("failed creating gRPC Relay service: %w", err)
	}

	dbOptions := badger.DefaultOptions(conf.Database.Path)
	db, err := badger.Open(dbOptions)
	if err != nil {
		return fmt.Errorf("failed to setup Badger database: %w", err)
	}
	defer db.Close()

	// Initialize and start garbage collector
	garbageCollector := sharedBadgerDB.NewGarbageCollector(db, log, conf.Database, "management")
	if err := garbageCollector.Start(); err != nil {
		return fmt.Errorf("failed to start garbage collector: %w", err)
	}
	defer func() {
		if err := garbageCollector.Stop(); err != nil {
			log.Error().Err(err).Msg("Failed to stop garbage collector")
		}
	}()

	configurationRepository, err := badgerdb.NewConfigurationRepository(log, db)
	if err != nil {
		return fmt.Errorf("failed to create configuration repository: %w", err)
	}

	userRepository := badgerdb.NewUserRepository(log, conf, db)
	scanRepository := badgerdb.NewScanRepository(log, conf, db)

	userService := services.NewUserService(log, relayGRPCClient, userRepository)
	configurationService := services.NewConfigurationService(log, conf, relayGRPCClient, configurationRepository)
	scanService, err := services.NewScanService(log, configurationRepository, relayGRPCClient, scanRepository)
	if err != nil {
		return fmt.Errorf("failed to create scan service: %w", err)
	}
	streamService := services.NewStreamService(log, relayGRPCClient)

	// Register stream metrics collector
	streamMetricsCollector := metrics.NewStreamMetricsCollector(log, relayGRPCClient)
	if err := metricsRegistry.Register("stream_metrics", streamMetricsCollector); err != nil {
		return fmt.Errorf("failed to register stream metrics collector: %w", err)
	}

	authService, err := services.NewAuthService(log, conf)
	if err != nil {
		return fmt.Errorf("failed to initialize auth service: %w", err)
	}

	openAPISpec := swag.GetSwagger("management")
	if openAPISpec == nil {
		return ErrMissingOpenAPISpec
	}

	am := middleware.NewJWTAuthMiddleware(log, authService)

	mc := controllers.NewMetaController(log, openAPISpec)
	ac := controllers.NewAuthController(log, am, authService)
	rc := controllers.NewStreamController(log, am, streamService)
	cc := controllers.NewConfigurationController(log, am, configurationService)
	sc := controllers.NewScanController(log, am, scanService)
	uc := controllers.NewUserController(log, am, userService)

	globalMiddleware := []gin.HandlerFunc{}
	if enableSentry {
		globalMiddleware = append(globalMiddleware, sentrygin.New(sentrygin.Options{
			Repanic: true,
		}))
	}
	b, err := cadre.NewBuilder(
		"management",
		cadre.WithContext(ctx),
		cadre.WithLogger(log),
		cadre.WithStatus(appStatus),
		cadre.WithMetricsRegistry(metricsRegistry),
		cadre.WithMetricsListeningAddress(conf.ListenAddressPrometheus),
		cadre.WithHTTP(
			"main_http",
			cadre.WithHTTPListeningAddress(conf.ListenAddressHTTP),
			cadre.WithGlobalMiddleware(globalMiddleware...),
			cadre.WithRoutingGroup(mc.GetRoutes()),
			cadre.WithRoutingGroup(ac.GetRoutes()),
			cadre.WithRoutingGroup(rc.GetRoutes()),
			cadre.WithRoutingGroup(cc.GetRoutes()),
			cadre.WithRoutingGroup(sc.GetRoutes()),
			cadre.WithRoutingGroup(uc.GetRoutes()),
		),
	)
	if err != nil {
		return fmt.Errorf("failed creating cadre builder: %w", err)
	}

	c, err := b.Build()
	if err != nil {
		return fmt.Errorf("failed constructing cadre: %w", err)
	}

	log.Info().Msg(` ██████   ██████          ████  █████    ███                          █████   `)
	log.Info().Msg(`░░██████ ██████          ░░███ ░░███    ░░░                          ░░███    `)
	log.Info().Msg(` ░███░█████░███ █████ ████░███ ███████  ████   ██████  ██████   ███████████  `)
	log.Info().Msg(` ░███░░███ ░███░░███ ░███ ░███░░░███░  ░░███  ███░░███░░░░░███ ███░░░░░███░   `)
	log.Info().Msg(` ░███ ░░░  ░███ ░███ ░███ ░███  ░███    ░███ ░███ ░░░  ███████░░█████ ░███    `)
	log.Info().Msg(` ░███      ░███ ░███ ░███ ░███  ░███ ███░███ ░███  ███░██  ███ ░░░░███░███ ███`)
	log.Info().Msg(` █████     █████░░███████ █████ ░░█████ █████░░██████░░████████ █████ ░░█████ `)
	log.Info().Msg(`░░░░░     ░░░░░  ░░░░░░░░░░█████ ░░░░░ ░░░░░  ░░░░░░  ░░░░░░░░░░░░░░   ░░░░░  `)
	log.Info().Msg(`                          ░░███                                               `)
	log.Info().Msg(` ████████  ████████ ██████ ░███████   ██████                                  `)
	log.Info().Msg(`░░███░░███░░███░░██ ██░░███░███░░███ ███░░███                                 `)
	log.Info().Msg(` ░███ ░███ ░███ ░░░███ ░███░███ ░███░███████                                  `)
	log.Info().Msg(` ░███ ░███ ░███   ░███ ░███░███ ░███░███░░░                                   `)
	log.Info().Msg(` ░███████  █████  ░░██████ ████████ ░░██████                                  `)
	log.Info().Msg(` ░███░░░  ░░░░░    ░░░░░░ ░░░░░░░░   ░░░░░░                                   `)
	log.Info().Msg(` ░███                                                                         `)
	log.Info().Msg(` █████                                                                        `)
	log.Info().Msg("Application started on " + conf.ListenAddressHTTP)

	err = c.Start()
	if err != nil {
		return fmt.Errorf("failed running cadre: %w", err)
	}

	return nil
}

func initSentry(sentryDSN string) bool {
	if sentryDSN == "" {
		return false
	}

	hostname, err := os.Hostname()
	if err != nil {
		hostname = "cannot get hostname"
	}

	err = sentry.Init(sentry.ClientOptions{
		Dsn:        sentryDSN,
		ServerName: hostname,
		Release:    "management@" + Version,

		SampleRate: suggestedSentrySampleRate,
	})

	defer sentry.Flush(sentryFlushDuration)
	defer sentry.Recover()

	if err != nil {
		slog.Warn("cannot create sentry client (sentry will be disabled)",
			"error", err,
		)
		return false
	}

	return true
}

func initZerolog(dc config.ManagementConfig, enableSentry bool) (logger zerolog.Logger, err error) {
	level, err := zerolog.ParseLevel(dc.Loglevel)
	if err != nil {
		err = fmt.Errorf("failed parsing configured log level: %w", err)
		return
	}

	zerolog.SetGlobalLevel(level)
	zerolog.DisableSampling(true)
	writers := []io.Writer{
		zerolog.ConsoleWriter{
			Out:        os.Stderr,
			TimeFormat: time.RFC3339,
		},
	}

	if enableSentry {
		writers = registerSentryWriter(writers)
	}

	logger = zerolog.New(zerolog.MultiLevelWriter(writers...)).With().Timestamp().Logger()
	return
}

func registerSentryWriter(writers []io.Writer) []io.Writer {
	w, err := zlogsentry.New(sentry.CurrentHub().Client())
	if err != nil {
		slog.Warn("failed initializing sentry zerolog writer",
			"error", err,
		)
		return writers
	}

	writers = append(writers, w)
	return writers
}
