package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"io"
	"log/slog"
	"os"
	"time"

	_ "git.moderntv.eu/multicast-probe/docs/rest-api/relay"
	"git.moderntv.eu/multicast-probe/internal/config"
	server "git.moderntv.eu/multicast-probe/internal/relay/api/grpc/servers"
	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/controllers"
	aservices "git.moderntv.eu/multicast-probe/internal/relay/application/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/factory"
	"git.moderntv.eu/multicast-probe/internal/relay/infrastructure/network"
	persistence "git.moderntv.eu/multicast-probe/internal/relay/infrastructure/persistence/badgerdb"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	relay_pb "git.moderntv.eu/multicast-probe/proto/relay/v1"
	badger "github.com/dgraph-io/badger/v4"
	sentry "github.com/getsentry/sentry-go"
	"github.com/moderntv/cadre"
	"github.com/moderntv/cadre/metrics"
	"github.com/moderntv/cadre/status"
	"github.com/rs/zerolog"
	zlogsentry "github.com/sveatlo/zerolog-sentry"
	"github.com/swaggo/swag"
	"google.golang.org/grpc"
)

var Version string

var ErrMissingOpenAPISpec = errors.New("open api spec not generated or has invalid instance name")

const (
	suggestedSentrySampleRate = 0.6
	sentryFlushDuration       = 10 * time.Second
)

//	@title			Multicase Probe Relay Service
//	@version		1.0
//	@description	Low level stream processing service.

func main() {
	err := runApp()
	if err != nil {
		slog.Error("failed running app",
			"error", err,
		)
		os.Exit(1)
	}
}

func runApp() error { //nolint: cyclop
	var (
		configFilePath string
		printVersion   bool
	)

	flag.StringVar(&configFilePath, "config", "", "path to config file")
	flag.BoolVar(&printVersion, "version", false, "print version and exit")
	flag.Parse()
	if printVersion {
		fmt.Println("Version: ", Version) //nolint: forbidigo
		return nil
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	conf, err := config.NewRelayConfig(configFilePath)
	if err != nil {
		return fmt.Errorf("app configuration failed: %w", err)
	}

	enableSentry := initSentry(conf.SentryDSN)

	log, err := initZerolog(conf, enableSentry)
	if err != nil {
		return fmt.Errorf("zerolog initialization failed: %w", err)
	}

	metricsRegistry, err := metrics.NewRegistry("multicast_probe_relay", nil)
	if err != nil {
		return fmt.Errorf("failed creating prometheus registry: %w", err)
	}

	appStatus := status.NewStatus(Version)
	log.Debug().Interface("config", conf).Msg("inititalized with config")

	// ==================================================
	// REAL APPLICATION INITIATION BEGINS HERE
	// ==================================================
	dbOptions := badger.DefaultOptions(conf.Database.Path)
	db, err := badger.Open(dbOptions)
	if err != nil {
		return fmt.Errorf("failed to setup Badger database: %w", err)
	}
	defer db.Close()

	// Initialize and start garbage collector
	garbageCollector := sharedBadgerDB.NewGarbageCollector(db, log, conf.Database, "relay")
	if err := garbageCollector.Start(); err != nil {
		return fmt.Errorf("failed to start garbage collector: %w", err)
	}
	defer func() {
		if err := garbageCollector.Stop(); err != nil {
			log.Error().Err(err).Msg("Failed to stop garbage collector")
		}
	}()

	configurationRepository, err := persistence.NewConfigurationRepository(log, db)
	if err != nil {
		return fmt.Errorf("failed to create configuration repository: %w", err)
	}
	configurationService := aservices.NewConfigurationService(log, configurationRepository)

	streamFactory := factory.NewStreamServiceFactory(log, configurationRepository)

	streamListenerService := network.NewStreamListenerService(log, configurationRepository)
	streamService, err := aservices.NewStreamService(
		log,
		configurationRepository,
		streamListenerService,
		streamFactory,
	)
	if err != nil {
		return fmt.Errorf("failed creating stream service: %w", err)
	}

	openAPISpec := swag.GetSwagger("relay")
	if openAPISpec == nil {
		return ErrMissingOpenAPISpec
	}

	mc := controllers.NewMetaController(log, openAPISpec)
	rs := server.NewConfigurationServer(log, configurationService)
	ss := server.NewStreamServer(log, streamService)
	sc := controllers.NewStreamController(log, streamService)

	configurationServerRegistrator := func(s *grpc.Server) {
		relay_pb.RegisterRelayConfigurationServiceServer(s, rs)
	}
	streamServerRegistrator := func(s *grpc.Server) {
		relay_pb.RegisterRelayStreamServiceServer(s, ss)
	}

	b, err := cadre.NewBuilder(
		"relay",
		cadre.WithContext(ctx),
		cadre.WithLogger(log),
		cadre.WithStatus(appStatus),
		cadre.WithMetricsRegistry(metricsRegistry),
		cadre.WithMetricsListeningAddress(conf.ListenAddressPrometheus),
		cadre.WithGRPC(
			cadre.WithGRPCListeningAddress(conf.ListenAddressGRPC),
			cadre.WithService(
				relay_pb.RelayConfigurationService_ServiceDesc.ServiceName,
				configurationServerRegistrator,
			),
			cadre.WithService(
				relay_pb.RelayStreamService_ServiceDesc.ServiceName,
				streamServerRegistrator,
			),
		),
		cadre.WithHTTP(
			"main_http",
			cadre.WithHTTPListeningAddress(conf.ListenAddressHTTP),
			cadre.WithRoutingGroup(mc.GetRoutes()),
			cadre.WithRoutingGroup(sc.GetRoutes()),
		),
	)
	if err != nil {
		return fmt.Errorf("failed creating cadre builder: %w", err)
	}

	c, err := b.Build()
	if err != nil {
		return fmt.Errorf("failed constructing cadre: %w", err)
	}

	log.Info().Msg(` ██▀███  ▓█████  ██▓    ▄▄▄     ▓██   ██▓`)
	log.Info().Msg(`▓██ ▒ ██▒▓█   ▀ ▓██▒   ▒████▄    ▒██  ██▒`)
	log.Info().Msg(`▓██ ░▄█ ▒▒███   ▒██░   ▒██  ▀█▄   ▒██ ██░`)
	log.Info().Msg(`▒██▀▀█▄  ▒▓█  ▄ ▒██░   ░██▄▄▄▄██  ░ ▐██▓░`)
	log.Info().Msg(`░██▓ ▒██▒░▒████▒░██████▒▓█   ▓██▒ ░ ██▒▓░`)
	log.Info().Msg(`░ ▒▓ ░▒▓░░░ ▒░ ░░ ▒░▓  ░▒▒   ▓▒█░  ██▒▒▒ `)
	log.Info().Msg(`  ░▒ ░ ▒░ ░ ░  ░░ ░ ▒  ░ ▒   ▒▒ ░▓██ ░▒░ `)
	log.Info().Msg(`  ░░   ░    ░     ░ ░    ░   ▒   ▒ ▒ ░░  `)
	log.Info().Msg(`   ░        ░  ░    ░  ░     ░  ░░ ░     `)
	log.Info().Msg(`                                 ░ ░     `)
	log.Info().Msg("")
	log.Info().Msg("Application HTTP started on " + conf.ListenAddressHTTP)
	log.Info().Msg("Application gRPC started on " + conf.ListenAddressGRPC)

	err = c.Start()
	if err != nil {
		return fmt.Errorf("failed running cadre: %w", err)
	}

	return nil
}

func initSentry(sentryDSN string) bool {
	if sentryDSN == "" {
		return false
	}

	hostname, err := os.Hostname()
	if err != nil {
		hostname = "cannot get hostname"
	}

	err = sentry.Init(sentry.ClientOptions{
		Dsn:        sentryDSN,
		ServerName: hostname,
		Release:    "relay@" + Version,

		SampleRate: suggestedSentrySampleRate,
	})

	defer sentry.Flush(sentryFlushDuration)
	defer sentry.Recover()

	if err != nil {
		slog.Warn("cannot create sentry client (sentry will be disabled)",
			"error", err,
		)
		return false
	}

	return true
}

func initZerolog(dc config.RelayConfig, enableSentry bool) (logger zerolog.Logger, err error) {
	level, err := zerolog.ParseLevel(dc.Loglevel)
	if err != nil {
		err = fmt.Errorf("failed parsing configured log level: %w", err)
		return
	}

	zerolog.SetGlobalLevel(level)
	zerolog.DisableSampling(true)
	writers := []io.Writer{
		zerolog.ConsoleWriter{
			Out:        os.Stderr,
			TimeFormat: time.RFC3339,
		},
	}

	if enableSentry {
		writers = registerSentryWriter(writers)
	}

	logger = zerolog.New(zerolog.MultiLevelWriter(writers...)).With().Timestamp().Logger()
	return
}

func registerSentryWriter(writers []io.Writer) []io.Writer {
	w, err := zlogsentry.New(sentry.CurrentHub().Client())
	if err != nil {
		slog.Warn("failed initializing sentry zerolog writer",
			"error", err,
		)
		return writers
	}

	writers = append(writers, w)
	return writers
}
