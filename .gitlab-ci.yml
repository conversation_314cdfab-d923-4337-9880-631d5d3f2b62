stages:
  - test
  - code-quality
  - build
  - package
  - docs

variables: &global-variables
    IMAGE: "${CI_REGISTRY_IMAGE}"
    VERSION: "${CI_COMMIT_REF_NAME}"
    BUILD: "${CI_PIPELINE_ID}"
    BUILDIMAGE: golang:1.18-buster
    GOPRIVATE: "git.moderntv.eu/*"
    DOCKER_BUILDKIT: 0
    PIPELINE_ENV_FILE: job_urls.env
    GITLAB_USER: gitlab-ci-token
    GITLAB_ACCESS_TOKEN: "${CI_JOB_TOKEN}"
    PROJECT_PRODUCTION_BRANCH: master
    DOCKER_APP_VERSION: $CI_COMMIT_REF_SLUG

default:
  image: golang:latest
  tags:
    - docker

  interruptible: true

  before_script:
    # Gitlab
    - export GOPRIVATE=git.moderntv.eu
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@git.moderntv.eu/".insteadOf "https://git.moderntv.eu/"
    - mkdir -p ~/.ssh
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" >> ~/.ssh/config

build:
  stage: build
  needs:
    - test
    - lint
  script:
    - make build
  artifacts:
    name: "binaries"
    paths:
        - bin/*
    expire_in: 1 week

lint:
  stage: test
  script:
    - make lint

test:
  stage: test
  script:
    - make test

package:
  stage: package
  image: registry.moderntv.eu/devops/dockerfiles/debian-builder:latest
  needs:
    - job: build
      artifacts: true
  variables:
    VERSION: "${CI_COMMIT_TAG}"
  dependencies:
    - build
  before_script:
    - apt-get --allow-releaseinfo-change update
  script:
    - export VERSION=${VERSION#"v"}
    - make package-upload
  after_script:
    - echo PACKAGE_JOB_URL=${CI_JOB_URL} >> ${PIPELINE_ENV_FILE}
    - echo PACKAGE_VERSION=${VERSION} >> ${PIPELINE_ENV_FILE}
  only:
   - tags
  artifacts:
    paths:
      - "build/*.deb"
      - "build/*.changes"
    reports:
      dotenv: ${PIPELINE_ENV_FILE}
    expire_in: 1 week

docker:
  stage: package
  needs:
        - lint
        - test
        - build
  image: dockette/docker
  services:
    - docker:20.10.5-dind
  before_script:
        - apk add make bash
        - apk add make gawk
        - docker info
        - docker login -u gitlab-ci-token -p ${CI_JOB_TOKEN} ${CI_REGISTRY}
        - '[[ ! -z "${CI_COMMIT_TAG}" ]] && export VERSION=${CI_COMMIT_TAG} || export VERSION=${CI_COMMIT_REF_SLUG}'
  script:
        - make docker-build;
        - make docker-release;
  artifacts:
    reports:
      dotenv: ${PIPELINE_ENV_FILE}

pages:
    stage: docs
    script:
        - mkdir -p ./public
        - make generate-docs
        - cp -r docs/* ./public/
    artifacts:
        paths:
            - public
    only:
        - tags
        - master
