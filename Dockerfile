FROM golang:latest AS builder

ARG GITLAB_USER
ARG GITLAB_ACCESS_TOKEN
ARG CMD_NAME

WORKDIR /app

# prepare system for private repo
RUN git config --global url."https://${GITLAB_USER}:${GITLAB_ACCESS_TOKEN}@git.moderntv.eu/".insteadOf "https://git.moderntv.eu/"
ENV GOPRIVATE git.moderntv.eu/*

COPY go.mod .
COPY go.sum .
RUN go mod download
COPY . . 
ENV CMDS ${CMD_NAME}
RUN make build-${CMD_NAME}

FROM alpine:latest as runner

RUN apk update
RUN apk add gcompat

ARG CMD_BIN_PATH
ARG CMD_CONFIG_PATH
ARG CMD_CONFIG_FOLDER

WORKDIR /app/

COPY --from=builder /app/${CMD_BIN_PATH} ./service
COPY --from=builder /app/${CMD_CONFIG_PATH} ./config.yml
COPY --from=builder /app/${CMD_CONFIG_FOLDER} ./config/

CMD ["./service", "-config", "./config.yml"]
