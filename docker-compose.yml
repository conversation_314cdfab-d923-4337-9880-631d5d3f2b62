---
services:
    management:
        build:
            dockerfile: Dockerfile.dev
            args:
                GITLAB_USER: ${GITLAB_USER}
                GITLAB_ACCESS_TOKEN: ${GITLAB_ACCESS_TOKEN}
        command: ["make", "run-management"]
        environment:
            ARGS: "--config config/management.yml"
        networks:
            - default
            - app
        volumes:
            - "./:/app"
        ports:
            - "8003:8003"
        profiles:
            - services
            - management
    relay:
        build:
            dockerfile: Dockerfile.dev
            args:
                GITLAB_USER: ${GITLAB_USER}
                GITLAB_ACCESS_TOKEN: ${GITLAB_ACCESS_TOKEN}
        command: ["make", "run-relay"]
        environment:
            ARGS: "--config config/relay.yml"
        networks:
            - default
            - app
        volumes:
            - "./:/app"
        ports:
            - "8008:8008" # http
            - "8010:8010" # grpc
        profiles:
            - services
            - relay

networks:
    app:
        internal: true
