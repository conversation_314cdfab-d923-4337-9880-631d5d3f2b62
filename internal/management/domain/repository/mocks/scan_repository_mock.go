// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/domain/repository (interfaces: ScanRepository)
//
// Generated by this command:
//
//	mockgen -destination mocks/scan_repository_mock.go . ScanRepository
//

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockScanRepository is a mock of ScanRepository interface.
type MockScanRepository struct {
	ctrl     *gomock.Controller
	recorder *MockScanRepositoryMockRecorder
	isgomock struct{}
}

// MockScanRepositoryMockRecorder is the mock recorder for MockScanRepository.
type MockScanRepositoryMockRecorder struct {
	mock *MockScanRepository
}

// NewMockScanRepository creates a new mock instance.
func NewMockScanRepository(ctrl *gomock.Controller) *MockScanRepository {
	mock := &MockScanRepository{ctrl: ctrl}
	mock.recorder = &MockScanRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScanRepository) EXPECT() *MockScanRepositoryMockRecorder {
	return m.recorder
}

// DeleteScan mocks base method.
func (m *MockScanRepository) DeleteScan(scanID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteScan", scanID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteScan indicates an expected call of DeleteScan.
func (mr *MockScanRepositoryMockRecorder) DeleteScan(scanID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteScan", reflect.TypeOf((*MockScanRepository)(nil).DeleteScan), scanID)
}

// GetScan mocks base method.
func (m *MockScanRepository) GetScan(scanID string) (entity.Scan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScan", scanID)
	ret0, _ := ret[0].(entity.Scan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScan indicates an expected call of GetScan.
func (mr *MockScanRepositoryMockRecorder) GetScan(scanID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScan", reflect.TypeOf((*MockScanRepository)(nil).GetScan), scanID)
}

// GetScans mocks base method.
func (m *MockScanRepository) GetScans() ([]entity.Scan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScans")
	ret0, _ := ret[0].([]entity.Scan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScans indicates an expected call of GetScans.
func (mr *MockScanRepositoryMockRecorder) GetScans() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScans", reflect.TypeOf((*MockScanRepository)(nil).GetScans))
}

// StoreScan mocks base method.
func (m *MockScanRepository) StoreScan(scan entity.Scan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreScan", scan)
	ret0, _ := ret[0].(error)
	return ret0
}

// StoreScan indicates an expected call of StoreScan.
func (mr *MockScanRepositoryMockRecorder) StoreScan(scan any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreScan", reflect.TypeOf((*MockScanRepository)(nil).StoreScan), scan)
}
