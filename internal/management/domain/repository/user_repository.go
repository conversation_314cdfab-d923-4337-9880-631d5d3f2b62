package repository

//go:generate go tool mockgen -destination mocks/user_repository_mock.go . UserRepository

import (
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"github.com/google/uuid"
)

type UserRepository interface {
	GetUsers() (users []entity.User, err error)
	GetUser(userID uuid.UUID) (user entity.User, err error)
	CreateUser(user entity.User) (u entity.User, err error)
	UpdateUser(user entity.User) (u entity.User, err error)
	DeleteUser(userID uuid.UUID) (err error)
}
