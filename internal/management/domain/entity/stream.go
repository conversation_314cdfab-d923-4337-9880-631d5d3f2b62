package entity

import "git.moderntv.eu/multicast-probe/internal/management/domain/value"

type Stream struct {
	Address   value.Address       `json:"address,omitzero"`
	Clients   int                 `json:"clients,omitzero"`
	Metrics   value.StreamMetrics `json:"metrics,omitzero"`
	Programs  []Program           `json:"programs,omitempty"`
	ScanError string              `json:"scanError,omitzero"`
}

func (s *Stream) HasValidStreamInfo() bool {
	if len(s.Programs) == 0 {
		return false
	}

	for _, program := range s.Programs {
		if program.Title != "" {
			return true
		}
	}

	return false
}
