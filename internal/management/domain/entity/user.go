package entity

import "github.com/google/uuid"

type User struct {
	ID uuid.UUID `json:"uuid,omitzero"`
	AnonymousUser
}

// AnonymousUser represents user without ID.
// This entity is used in the API to better reflect expected values.
// E.g. in POST body when creating user.
type AnonymousUser struct {
	Username string `json:"username,omitzero"`
	Password string `json:"password,omitzero"`
}

// Merge merges old user with new user.
// Merge only changes those options that are defined by the new user.
func (u *User) Merge(newU User) {
	if newU.Username != "" {
		u.Username = newU.Username
	}

	if newU.Password != "" {
		u.Password = newU.Password
	}
}
