package entity

import "git.moderntv.eu/multicast-probe/internal/management/domain/value"

type Program struct {
	ProgramID int                  `json:"programId,omitzero"`
	Title     string               `json:"title,omitzero"`
	Provider  string               `json:"provider,omitzero"`
	Metrics   value.ProgramMetrics `json:"metrics,omitzero"`
	Tracks    []Track              `json:"tracks,omitempty"`
}
