package entity

import (
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	"github.com/google/uuid"
)

type Scan struct {
	ID         uuid.UUID            `json:"id,omitzero"`
	Parameters value.ScanParameters `json:"parameters,omitzero"`
	CreatedAt  value.UNIXTime       `json:"createdAt,omitzero"`
	Status     ScanStatus           `json:"status,omitzero"`
	Streams    []Stream             `json:"streams,omitempty"`
}

type ScanStatus string

const (
	ScanStatusQueued     ScanStatus = "queued"
	ScanStatusProcessing ScanStatus = "processing"
	ScanStatusCompleted  ScanStatus = "completed"
	ScanStatusFailed     ScanStatus = "failed"
	ScanStatusCancelled  ScanStatus = "cancelled"
)
