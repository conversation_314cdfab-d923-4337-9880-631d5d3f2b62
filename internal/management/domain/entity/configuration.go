package entity

import (
	"time"
)

type Configuration struct {
	ScanDelay           time.Duration `json:"scanDelay,omitzero"`
	ScanInterval        time.Duration `json:"scanInterval,omitzero"`
	ScanMaxTime         time.Duration `json:"scanMaxTime,omitzero"`
	ScansPerSecond      int           `json:"scansPerSecond,omitzero"`
	ScanChannelSize     int           `json:"scanChannelSize,omitzero"`
	ScanRetention       time.Duration `json:"scanRetention,omitzero"`
	ScanCleanupInterval time.Duration `json:"scanCleanupInterval,omitzero"` // imuatble
}

func NewDefaultConfiguration() Configuration {
	return Configuration{
		ScanDelay:           10 * time.Second, //nolint:mnd
		ScanInterval:        10 * time.Second, //nolint:mnd
		ScanMaxTime:         58 * time.Second, //nolint:mnd
		ScansPerSecond:      2,                //nolint:mnd
		ScanChannelSize:     200000,           //nolint:mnd
		ScanRetention:       168 * time.Hour,  //nolint:mnd
		ScanCleanupInterval: 24 * time.Hour,   //nolint:mnd
	}
}

// Merge merges old configuration with new configuration.
// Merge only changes those options that are defined by the new configuration.
func (c *Configuration) Merge(newC Configuration) {
	if newC.ScanDelay > 0 {
		c.ScanDelay = newC.ScanDelay
	}
	if newC.ScanInterval > 0 {
		c.ScanInterval = newC.ScanInterval
	}
	if newC.ScanMaxTime > 0 {
		c.ScanMaxTime = newC.ScanMaxTime
	}
	if newC.ScansPerSecond > 0 {
		c.ScansPerSecond = newC.ScansPerSecond
	}
	if newC.ScanChannelSize > 0 {
		c.ScanChannelSize = newC.ScanChannelSize
	}
	if newC.ScanRetention > 0 {
		c.ScanRetention = newC.ScanRetention
	}
}
