package entity

import (
	"time"

	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
)

type RelayConfiguration struct {
	ScanTimeout             time.Duration            `json:"scanTimeout"             mapstructure:"scan_timeout"`
	ScanCleanupDelay        time.Duration            `json:"cleanupDelay"            mapstructure:"cleanup_delay"`
	DispatcherBufferSize    int                      `json:"dispatcherBufferSize"    mapstructure:"dispatcher_buffer_size"`     //nolint: lll
	StreamChannelBufferSize int                      `json:"streamChannelBufferSize" mapstructure:"stream_channel_buffer_size"` //nolint: lll
	JoinBufferSize          int                      `json:"joinBufferSize"          mapstructure:"join_buffer_size"`
	JoinsPerSecond          int                      `json:"joinsPerSecond"          mapstructure:"joins_per_second"`
	NetworkInterfaces       []value.NetworkInterface `json:"networkInterfaces"       mapstructure:"network_interfaces"` //nolint: lll
	UDPPacketSize           int                      `json:"udpPacketSize"`
	ConnectionTimeout       time.Duration            `json:"connectionTimeout"`
}

func (c *RelayConfiguration) Merge(newC RelayConfiguration) {
	if newC.ScanTimeout != 0 {
		c.ScanTimeout = newC.ScanTimeout
	}
	if newC.ScanCleanupDelay != 0 {
		c.ScanCleanupDelay = newC.ScanCleanupDelay
	}
	if newC.DispatcherBufferSize != 0 {
		c.DispatcherBufferSize = newC.DispatcherBufferSize
	}
	if newC.StreamChannelBufferSize != 0 {
		c.StreamChannelBufferSize = newC.StreamChannelBufferSize
	}
	if newC.JoinBufferSize != 0 {
		c.JoinBufferSize = newC.JoinBufferSize
	}
	if newC.JoinsPerSecond != 0 {
		c.JoinsPerSecond = newC.JoinsPerSecond
	}
	if newC.UDPPacketSize != 0 {
		c.UDPPacketSize = newC.UDPPacketSize
	}
	if newC.ConnectionTimeout != 0 {
		c.ConnectionTimeout = newC.ConnectionTimeout
	}
	if len(newC.NetworkInterfaces) > 0 {
		c.NetworkInterfaces = newC.NetworkInterfaces
	}
}
