package value_test

import (
	"encoding/json"
	"testing"

	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAddressMarshaling(t *testing.T) {
	t.<PERSON>()

	t.Run("MarshalJSON_ValidAddress_MarshalsCorrectly", func(t *testing.T) {
		t.<PERSON>()

		address, err := value.ParseAddress("**********", 60000)
		require.NoError(t, err)

		bytes, err := json.<PERSON>(address)
		require.NoError(t, err)

		assert.JSONEq(t, `{"address":"**********","port":60000}`, string(bytes))
	})

	t.Run("UnmarshalJSON_ValidAddress_MarshalsCorrectly", func(t *testing.T) {
		t.<PERSON>llel()

		var address value.Address
		raw := `{"address":"**********","port":57000}`

		err := json.Unmarshal([]byte(raw), &address)
		require.NoError(t, err)

		assert.Equal(t,
			value.MustParseAddress("**********", 57000),
			address,
		)
	})

	t.Run("UnmarshalJSON_InvalidAddress_FailsToMarshal", func(t *testing.T) {
		t.Parallel()

		var address value.Address
		raw := `{"address":"123.0.x.12","port":57000}`

		err := json.Unmarshal([]byte(raw), &address)
		require.Error(t, err)
	})
}
