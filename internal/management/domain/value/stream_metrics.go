package value

type StreamMetrics struct {
	Receivers          int64 `json:"receivers"`
	Discontinuities    int64 `json:"discontinuities"`
	ReceivedUDPPackets int64 `json:"receivedUdpPackets"`
	MissingUDPPackets  int64 `json:"missingUdpPackets"`
	ReceivedTSPackets  int64 `json:"receivedTsPackets"`
	ReceivedBytes      int64 `json:"receivedBytes"`
	PresentDuration    int64 `json:"presentDuration"`
	ReceivingDuration  int64 `json:"receivingDuration"`
}
