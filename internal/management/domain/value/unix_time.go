package value

import (
	"encoding/json"
	"time"
)

var (
	_ json.Marshaler   = &UNIXTime{}
	_ json.Unmarshaler = &UNIXTime{}
)

// UNIXTime time is a time.Time wrapper that uses
// UNIX timestamp for marshalling and unmarshalling.
type UNIXTime struct {
	time.Time
}

func NewUNIXTime(t time.Time) UNIXTime {
	return UNIXTime{
		Time: t,
	}
}

func (ut UNIXTime) MarshalJSON() ([]byte, error) {
	return json.Marshal(ut.Unix())
}

func (ut *UNIXTime) UnmarshalJSON(data []byte) (err error) {
	var n int64
	if err = json.Unmarshal(data, &n); err != nil {
		return err
	}

	ut.Time = time.Unix(n, 0)
	return
}
