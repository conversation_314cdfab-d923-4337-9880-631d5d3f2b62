package value

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/netip"
)

var ErrPortOutOfBounds = errors.New("port value is out of bounds")

type Address struct {
	netip.AddrPort
}

func ParseAddress(ip string, port uint16) (a Address, err error) {
	address, err := netip.ParseAddr(ip)
	if err != nil {
		err = fmt.Errorf("failed parsing address ip: %w", err)
		return
	}

	a = ParseAddressPortFrom(address, port)

	return
}

func ParseAddressPortFrom(address netip.Addr, port uint16) Address {
	return Address{
		AddrPort: netip.AddrPortFrom(address, port),
	}
}

func ParseAddressI32(ip string, port int32) (a Address, err error) {
	if port < 0 || port > 65535 {
		err = ErrPortOutOfBounds
		return
	}

	return ParseAddress(ip, uint16(port))
}

func ParseAddressRaw(raw string) (a Address, err error) {
	address, err := netip.ParseAddrPort(raw)
	if err != nil {
		err = fmt.Errorf("failed parsing address: %w", err)
		return
	}

	a = Address{
		AddrPort: address,
	}

	return
}

// MustParseAddress panics when given invalid input.
// This function should only be used in tests.
func MustParseAddress(ip string, port uint16) Address {
	address := netip.MustParseAddr(ip)
	return Address{
		AddrPort: netip.AddrPortFrom(address, port),
	}
}

// MarshalJSON implements custom JSON marshaling.
func (a Address) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Address string `json:"address"`
		Port    uint16 `json:"port"`
	}{
		Address: a.Addr().String(),
		Port:    a.Port(),
	})
}

// UnmarshalJSON implements custom JSON unmarshaling.
func (a *Address) UnmarshalJSON(data []byte) error {
	var aux struct {
		Address string `json:"address"`
		Port    uint16 `json:"port"`
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	address, err := netip.ParseAddr(aux.Address)
	if err != nil {
		return err
	}

	a.AddrPort = netip.AddrPortFrom(address, aux.Port)
	return nil
}
