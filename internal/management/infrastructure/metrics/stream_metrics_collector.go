package metrics

import (
	"context"
	"strconv"
	"sync"
	"time"

	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
)

const (
	metricsCollectionTimeout = time.Second * 5
)

var _ prometheus.Collector = &StreamMetricsCollector{}

type StreamMetricsCollector struct {
	log         zerolog.Logger
	relayClient relay.Client

	streamReceiverGauge             *prometheus.GaugeVec
	streamDiscontinuitiesCounter    *prometheus.CounterVec
	streamReceivedUDPPacketsCounter *prometheus.CounterVec
	streamMissingUDPPacketsCounter  *prometheus.CounterVec
	streamReceivedTSPacketsCounter  *prometheus.CounterVec
	streamReceivedBytesCounter      *prometheus.CounterVec
	streamPresentDurationCounter    *prometheus.CounterVec
	streamReceivingDurationCounter  *prometheus.CounterVec

	programReceiverGauge            *prometheus.GaugeVec
	programDiscontinuitiesCounter   *prometheus.CounterVec
	programReceivedTSPacketsCounter *prometheus.CounterVec
	programReceivedBytesCounter     *prometheus.CounterVec

	trackDiscontinuitiesCounter   *prometheus.CounterVec
	trackReceivedTSPacketsCounter *prometheus.CounterVec
	trackReceivedBytesCounter     *prometheus.CounterVec

	// Metrics slice only groups all metrics
	// so mass operations on all metrics are easier.
	metrics []metric

	mu sync.Mutex
}

func NewStreamMetricsCollector(
	log zerolog.Logger,
	relayClient relay.Client,
) *StreamMetricsCollector {
	systemPrefix := "multicast_probe_"
	smc := &StreamMetricsCollector{
		log:         log.With().Str("component", "metrics/stream_metrics_collector").Logger(),
		relayClient: relayClient,

		streamReceiverGauge: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: systemPrefix + "stream_receiver_count",
				Help: "Number of receivers currently consuming the stream",
			},
			[]string{"stream"},
		),
		streamDiscontinuitiesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_discontinuities_total",
				Help: "Number of discontinuities that occurred",
			},
			[]string{"stream"},
		),
		streamReceivedUDPPacketsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_received_udp_packets_total",
				Help: "Number of processed UDP packets",
			},
			[]string{"stream"},
		),
		streamMissingUDPPacketsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_missing_udp_packets_total",
				Help: "Number of missing UDP packets",
			},
			[]string{"stream"},
		),
		streamReceivedTSPacketsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_received_ts_packets_total",
				Help: "Number of processed TS packets",
			},
			[]string{"stream"},
		),
		streamReceivedBytesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_received_bytes_total",
				Help: "Number of received bytes",
			},
			[]string{"stream"},
		),
		streamPresentDurationCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_present_duration_ms",
				Help: "Duration for which the stream was monitored",
			},
			[]string{"stream"},
		),
		streamReceivingDurationCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "stream_receiving_duration_ms",
				Help: "Duration for which stream data were processed",
			},
			[]string{"stream"},
		),

		programReceiverGauge: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: systemPrefix + "program_receiver_count",
				Help: "Number of receivers currently consuming the program",
			},
			[]string{"stream", "program"},
		),
		programDiscontinuitiesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "program_discontinuitites_total",
				Help: "Number of discontinuities that occurred",
			},
			[]string{"stream", "program"},
		),
		programReceivedTSPacketsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "program_received_ts_packets_total",
				Help: "Number of processed TS packets",
			},
			[]string{"stream", "program"},
		),
		programReceivedBytesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "program_received_bytes_total",
				Help: "Number of received bytes",
			},
			[]string{"stream", "program"},
		),
		trackDiscontinuitiesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "track_discontinuities_total",
				Help: "Number of discontinuities that occurred",
			},
			[]string{"stream", "program", "track"},
		),
		trackReceivedTSPacketsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "track_received_ts_packets_total",
				Help: "Number of processed TS packets",
			},
			[]string{"stream", "program", "track"},
		),
		trackReceivedBytesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: systemPrefix + "track_received_bytes_total",
				Help: "Number of received bytes",
			},
			[]string{"stream", "program", "track"},
		),
	}

	smc.metrics = []metric{
		smc.streamReceiverGauge,
		smc.streamDiscontinuitiesCounter,
		smc.streamReceivedUDPPacketsCounter,
		smc.streamMissingUDPPacketsCounter,
		smc.streamReceivedTSPacketsCounter,
		smc.streamReceivedBytesCounter,
		smc.streamPresentDurationCounter,
		smc.streamReceivingDurationCounter,

		smc.programReceiverGauge,
		smc.programDiscontinuitiesCounter,
		smc.programReceivedTSPacketsCounter,
		smc.programReceivedBytesCounter,

		smc.trackDiscontinuitiesCounter,
		smc.trackReceivedTSPacketsCounter,
		smc.trackReceivedBytesCounter,
	}

	return smc
}

func (smc *StreamMetricsCollector) Describe(ch chan<- *prometheus.Desc) {
	for _, metric := range smc.metrics {
		metric.Describe(ch)
	}
}

func (smc *StreamMetricsCollector) Collect(ch chan<- prometheus.Metric) {
	smc.mu.Lock()
	defer smc.mu.Unlock()

	// Clear existing metrics as we are only interested in current state.
	// I.e. we don't want to sum counter values.
	for _, metric := range smc.metrics {
		metric.Reset()
	}

	ctx, cancel := context.WithTimeout(context.Background(), metricsCollectionTimeout)
	defer cancel()

	streams, err := smc.relayClient.StreamsInfo(ctx)
	if err != nil {
		smc.log.Error().
			Err(err).
			Msg("failed to fetch stream information")
		return
	}

	smc.log.Trace().
		Int("stream_count", len(streams)).
		Msg("retrieving metrics from streams")

	for _, stream := range streams {
		streamLabels := prometheus.Labels{
			"stream": stream.Address.String(),
		}

		smc.streamReceiverGauge.With(streamLabels).
			Set(float64(stream.Metrics.Receivers))
		smc.streamDiscontinuitiesCounter.With(streamLabels).
			Add(float64(stream.Metrics.Discontinuities))
		smc.streamReceivedUDPPacketsCounter.With(streamLabels).
			Add(float64(stream.Metrics.ReceivedUDPPackets))
		smc.streamMissingUDPPacketsCounter.With(streamLabels).
			Add(float64(stream.Metrics.MissingUDPPackets))
		smc.streamReceivedTSPacketsCounter.With(streamLabels).
			Add(float64(stream.Metrics.ReceivedTSPackets))
		smc.streamReceivedBytesCounter.With(streamLabels).
			Add(float64(stream.Metrics.ReceivedBytes))
		smc.streamPresentDurationCounter.With(streamLabels).
			Add(float64(stream.Metrics.PresentDuration))
		smc.streamReceivingDurationCounter.With(streamLabels).
			Add(float64(stream.Metrics.ReceivingDuration))

		for _, program := range stream.Programs {
			programLabels := prometheus.Labels{
				"stream":  stream.Address.String(),
				"program": program.Title,
			}

			smc.programReceiverGauge.With(programLabels).
				Set(float64(program.Metrics.Receivers))
			smc.programDiscontinuitiesCounter.With(programLabels).
				Add(float64(program.Metrics.Discontinuities))
			smc.programReceivedTSPacketsCounter.With(programLabels).
				Add(float64(program.Metrics.ReceivedTSPackets))
			smc.programReceivedBytesCounter.With(programLabels).
				Add(float64(program.Metrics.ReceivedBytes))

			for _, track := range program.Tracks {
				trackLabels := prometheus.Labels{
					"stream":  stream.Address.String(),
					"program": program.Title,
					"track":   strconv.Itoa(track.PID),
				}

				smc.trackDiscontinuitiesCounter.With(trackLabels).
					Add(float64(track.Metrics.Discontinuities))
				smc.trackReceivedTSPacketsCounter.With(trackLabels).
					Add(float64(track.Metrics.ReceivedTSPackets))
				smc.trackReceivedBytesCounter.With(trackLabels).
					Add(float64(track.Metrics.ReceivedBytes))
			}
		}
	}

	for _, metric := range smc.metrics {
		metric.Collect(ch)
	}
}
