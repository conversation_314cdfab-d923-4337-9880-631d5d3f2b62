package relay

import (
	"context"

	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
)

//go:generate go tool mockgen -destination mocks/relay_client_mock.go . Client

type Client interface {
	GetConfiguration(ctx context.Context) (c entity.RelayConfiguration, err error)
	SetConfiguration(ctx context.Context, c entity.RelayConfiguration) (err error)
	Scan(ctx context.Context, address value.Address) (err error)
	StreamsInfo(ctx context.Context) (streams []entity.Stream, err error)
	StreamInfo(ctx context.Context, address value.Address) (stream entity.Stream, err error)
}
