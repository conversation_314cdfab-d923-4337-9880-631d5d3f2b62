package mappers

import (
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
)

func ProtoStreamToStream(protoStream *proto.StreamInfo) (stream entity.Stream, err error) {
	address, err := value.ParseAddressI32(protoStream.GetMulticastIp(), protoStream.GetPort())
	if err != nil {
		return
	}

	stream = entity.Stream{
		Address:  address,
		Clients:  int(protoStream.GetClients()),
		Programs: make([]entity.Program, 0, len(protoStream.GetPrograms())),
	}

	// Convert stream metrics
	streamMetrics := protoStream.GetMetrics()
	if streamMetrics != nil {
		stream.Metrics.Receivers = streamMetrics.GetStreamReceiverCount()
		stream.Metrics.Discontinuities = streamMetrics.GetStreamDiscontinuitiesTotal()
		stream.Metrics.ReceivedUDPPackets = streamMetrics.GetStreamReceivedUdpPacketsTotal()
		stream.Metrics.MissingUDPPackets = streamMetrics.GetStreamMissingUdpPacketsTotal()
		stream.Metrics.ReceivedTSPackets = streamMetrics.GetStreamReceivedTsPacketsTotal()
		stream.Metrics.ReceivedBytes = streamMetrics.GetStreamReceivedBytesTotal()
		stream.Metrics.PresentDuration = streamMetrics.GetStreamPresentDuration()
		stream.Metrics.ReceivingDuration = streamMetrics.GetStreamReceivingDuration()
	}

	for _, protoProgram := range protoStream.GetPrograms() {
		program := entity.Program{
			ProgramID: int(protoProgram.GetId()),
			Title:     protoProgram.GetTitle(),
			Provider:  protoProgram.GetProvider(),
			Tracks:    make([]entity.Track, 0, len(protoProgram.GetTracks())),
		}

		// Convert program metrics
		programMetrics := protoProgram.GetMetrics()
		if programMetrics != nil {
			program.Metrics.Receivers = programMetrics.GetProgramReceiverCount()
			program.Metrics.Discontinuities = programMetrics.GetProgramDiscontinuities()
			program.Metrics.ReceivedTSPackets = programMetrics.GetProgramReceivedTsPacketsTotal()
			program.Metrics.ReceivedBytes = programMetrics.GetProgramReceivedBytesTotal()
		}

		for _, protoTrack := range protoProgram.GetTracks() {
			track := entity.Track{
				PID:         int(protoTrack.GetPid()),
				Type:        int(protoTrack.GetType()),
				Description: protoTrack.GetDescription(),
			}

			trackMetrics := protoTrack.GetMetrics()
			if trackMetrics != nil {
				track.Metrics.Discontinuities = trackMetrics.GetTrackDiscontinuities()
				track.Metrics.ReceivedBytes = trackMetrics.GetTrackReceivedBytesTotal()
				track.Metrics.ReceivedTSPackets = trackMetrics.GetTrackReceivedTsPacketsTotal()
			}

			program.Tracks = append(program.Tracks, track)
		}

		stream.Programs = append(stream.Programs, program)
	}

	return
}
