package relaygrpc

import (
	"context"
	"fmt"
	"net/netip"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client/grpc_relay_client/mappers"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
	"github.com/rs/zerolog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var _ relay.Client = &Client{}

type Client struct {
	log                 zerolog.Logger
	config              config.ManagementConfig
	configurationClient proto.RelayConfigurationServiceClient
	streamClient        proto.RelayStreamServiceClient
}

func NewClient(log zerolog.Logger, config config.ManagementConfig) (c *Client, err error) {
	configurationConn, err := grpc.NewClient(
		config.Relay.ConfigurationServer.Address,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		err = fmt.Errorf("failed creating configuration grpc client: %w", err)
		return
	}

	streamConn, err := grpc.NewClient(
		config.Relay.StreamServer.Address,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		err = fmt.Errorf("failed creating stream grpc client: %w", err)
		return
	}

	c = &Client{
		log:                 log.With().Str("component", "clients/grpc_relay_client").Logger(),
		config:              config,
		configurationClient: proto.NewRelayConfigurationServiceClient(configurationConn),
		streamClient:        proto.NewRelayStreamServiceClient(streamConn),
	}
	return
}

// GetConfiguration retrieves relay configuration.
func (c *Client) GetConfiguration(ctx context.Context) (conf entity.RelayConfiguration, err error) {
	ctx, cancel := context.WithTimeout(ctx, c.config.Grpc.RequestTimeout)
	defer cancel()

	req := &proto.ConfigurationRequest{}

	res, err := c.configurationClient.Configuration(ctx, req)
	if err != nil {
		err = fmt.Errorf("failed getting configuration: %w", err)
		return
	}

	grpcConf := res.GetConfiguration()
	networkInterfaces := make([]value.NetworkInterface, 0, len(grpcConf.GetNetworkInterfaces()))
	for _, ni := range grpcConf.GetNetworkInterfaces() {
		prefix, err := netip.ParsePrefix(ni.GetPrefix())
		if err != nil {
			return entity.RelayConfiguration{}, err
		}

		networkInterfaces = append(networkInterfaces, value.NetworkInterface{
			Name:   ni.GetName(),
			Prefix: prefix,
		})
	}

	conf = entity.RelayConfiguration{
		ScanTimeout:             time.Duration(grpcConf.GetScanTimeout()),
		ScanCleanupDelay:        time.Duration(grpcConf.GetCleanupDelay()),
		DispatcherBufferSize:    int(grpcConf.GetDispatcherBufferSize()),
		StreamChannelBufferSize: int(grpcConf.GetStreamChannelBufferSize()),
		JoinBufferSize:          int(grpcConf.GetJoinBufferSize()),
		JoinsPerSecond:          int(grpcConf.GetJoinsPerSecond()),
		NetworkInterfaces:       networkInterfaces,
		UDPPacketSize:           int(grpcConf.GetUdpPacketSize()),
		ConnectionTimeout:       time.Duration(grpcConf.GetConnectionTimeout()),
	}

	return
}

// SetConfiguration updates relay configuration.
func (c *Client) SetConfiguration(
	ctx context.Context,
	conf entity.RelayConfiguration,
) (err error) {
	ctx, cancel := context.WithTimeout(ctx, c.config.Grpc.RequestTimeout)
	defer cancel()

	networkInterfaces := make([]*proto.NetworkInterface, 0, len(conf.NetworkInterfaces))
	for _, ni := range conf.NetworkInterfaces {
		networkInterfaces = append(networkInterfaces, &proto.NetworkInterface{
			Name:   ni.Name,
			Prefix: ni.Prefix.String(),
		})
	}

	req := &proto.SetConfigurationRequest{
		Configuration: &proto.Configuration{
			ScanTimeout:             int64(conf.ScanTimeout),
			CleanupDelay:            int64(conf.ScanCleanupDelay),
			DispatcherBufferSize:    int64(conf.DispatcherBufferSize),
			StreamChannelBufferSize: int64(conf.StreamChannelBufferSize),
			JoinBufferSize:          int64(conf.JoinBufferSize),
			JoinsPerSecond:          int64(conf.JoinsPerSecond),
			NetworkInterfaces:       networkInterfaces,
			UdpPacketSize:           int64(conf.UDPPacketSize),
			ConnectionTimeout:       int64(conf.ConnectionTimeout),
		},
	}

	_, err = c.configurationClient.SetConfiguration(ctx, req)
	if err != nil {
		err = fmt.Errorf("failed setting configuration: %w", err)
		return
	}

	return
}

// Scan a multicast stream.
func (c *Client) Scan(ctx context.Context, address value.Address) (err error) {
	req := &proto.ScanRequest{
		MulticastIp: address.Addr().String(),
		Port:        int32(address.Port()),
	}

	_, err = c.streamClient.Scan(ctx, req)
	if err != nil {
		err = fmt.Errorf("failed scanning: %w", err)
		return
	}

	return
}

// Get list of all streams.
func (c *Client) StreamsInfo(ctx context.Context) (streams []entity.Stream, err error) {
	ctx, cancel := context.WithTimeout(ctx, c.config.Grpc.RequestTimeout)
	defer cancel()

	req := &proto.StreamsInfoRequest{}

	res, err := c.streamClient.StreamsInfo(ctx, req)
	if err != nil {
		err = fmt.Errorf("failed getting streams info: %w", err)
		return
	}

	streams = make([]entity.Stream, 0, len(res.GetStreams()))
	for _, protoStream := range res.GetStreams() {
		stream, err := mappers.ProtoStreamToStream(protoStream)
		if err != nil {
			return nil, fmt.Errorf("failed converting proto stream to stream: %w", err)
		}

		streams = append(streams, stream)
	}

	c.log.Debug().
		Int("stream_count", len(streams)).
		Msg("successfully retrieved streams info")

	return
}

// Get details of a specific stream.
func (c *Client) StreamInfo(
	ctx context.Context,
	address value.Address,
) (stream entity.Stream, err error) {
	ctx, cancel := context.WithTimeout(ctx, c.config.Grpc.RequestTimeout)
	defer cancel()

	req := &proto.StreamInfoRequest{
		MulticastIp: address.Addr().String(),
		Port:        int32(address.Port()),
	}

	res, err := c.streamClient.StreamInfo(ctx, req)
	if err != nil {
		err = fmt.Errorf("failed getting stream info: %w", err)
		return
	}

	stream, err = mappers.ProtoStreamToStream(res.GetStream())
	if err != nil {
		err = fmt.Errorf("failed converting proto stream to stream: %w", err)
		return
	}

	c.log.Debug().
		Interface("address", stream.Address).
		Int("clients", stream.Clients).
		Msg("successfully retrieved stream info")

	return
}
