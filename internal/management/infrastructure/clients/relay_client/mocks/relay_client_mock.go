// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination mocks/relay_client_mock.go . Client
//

// Package mock_relay is a generated GoMock package.
package mock_relay

import (
	context "context"
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	value "git.moderntv.eu/multicast-probe/internal/management/domain/value"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetConfiguration mocks base method.
func (m *MockClient) GetConfiguration(ctx context.Context) (entity.RelayConfiguration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfiguration", ctx)
	ret0, _ := ret[0].(entity.RelayConfiguration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfiguration indicates an expected call of GetConfiguration.
func (mr *MockClientMockRecorder) GetConfiguration(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfiguration", reflect.TypeOf((*MockClient)(nil).GetConfiguration), ctx)
}

// Scan mocks base method.
func (m *MockClient) Scan(ctx context.Context, address value.Address) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scan", ctx, address)
	ret0, _ := ret[0].(error)
	return ret0
}

// Scan indicates an expected call of Scan.
func (mr *MockClientMockRecorder) Scan(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockClient)(nil).Scan), ctx, address)
}

// SetConfiguration mocks base method.
func (m *MockClient) SetConfiguration(ctx context.Context, c entity.RelayConfiguration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetConfiguration", ctx, c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetConfiguration indicates an expected call of SetConfiguration.
func (mr *MockClientMockRecorder) SetConfiguration(ctx, c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetConfiguration", reflect.TypeOf((*MockClient)(nil).SetConfiguration), ctx, c)
}

// StreamInfo mocks base method.
func (m *MockClient) StreamInfo(ctx context.Context, address value.Address) (entity.Stream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamInfo", ctx, address)
	ret0, _ := ret[0].(entity.Stream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamInfo indicates an expected call of StreamInfo.
func (mr *MockClientMockRecorder) StreamInfo(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamInfo", reflect.TypeOf((*MockClient)(nil).StreamInfo), ctx, address)
}

// StreamsInfo mocks base method.
func (m *MockClient) StreamsInfo(ctx context.Context) ([]entity.Stream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamsInfo", ctx)
	ret0, _ := ret[0].([]entity.Stream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamsInfo indicates an expected call of StreamsInfo.
func (mr *MockClientMockRecorder) StreamsInfo(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamsInfo", reflect.TypeOf((*MockClient)(nil).StreamsInfo), ctx)
}
