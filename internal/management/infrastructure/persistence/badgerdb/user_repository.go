package badgerdb

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	"github.com/dgraph-io/badger/v4"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

var _ repository.UserRepository = &UserRepository{}

type UserRepository struct {
	log    zerolog.Logger
	config config.ManagementConfig
	db     *badger.DB
}

func NewUserRepository(
	log zerolog.Logger,
	config config.ManagementConfig,
	db *badger.DB,
) *UserRepository {
	return &UserRepository{
		log:    log.With().Str("component", "management/badgerdb_user_repository").Logger(),
		config: config,
		db:     db,
	}
}

// GetUsers implements repository.UserRepository.
func (ur *UserRepository) GetUsers() (users []entity.User, err error) {
	err = ur.db.View(func(txn *badger.Txn) error {
		it := txn.NewIterator(badger.DefaultIteratorOptions)
		defer it.Close()

		prefix := ur.prefix()
		for it.Seek(prefix); it.ValidForPrefix(prefix); it.Next() {
			item := it.Item()

			var user entity.User
			err = item.Value(func(val []byte) error {
				return json.Unmarshal(val, &user)
			})
			if err != nil {
				return fmt.Errorf("failed unmarshalling user: %w", err)
			}

			users = append(users, user)
		}

		return nil
	})
	if err != nil {
		err = fmt.Errorf("failed getting user entities: %w", err)
		return
	}

	return
}

// GetUser implements repository.UserRepository.
func (ur *UserRepository) GetUser(userID uuid.UUID) (u entity.User, err error) {
	key := ur.userKey(userID)

	err = ur.db.View(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if errors.Is(err, badger.ErrKeyNotFound) {
			return repository.ErrUserNotFound
		}

		if err != nil {
			return fmt.Errorf("failed getting user: %w", err)
		}

		var user entity.User
		err = item.Value(func(val []byte) error {
			return json.Unmarshal(val, &user)
		})
		if err != nil {
			return fmt.Errorf("failed unmarshalling user: %w", err)
		}

		u = user

		return nil
	})
	if err != nil {
		err = fmt.Errorf("failed getting user entity: %w", err)
		return
	}

	return
}

// CreateUser implements repository.UserRepository.
func (ur *UserRepository) CreateUser(user entity.User) (u entity.User, err error) {
	var key []byte

	err = ur.db.Update(func(txn *badger.Txn) error {
		for { // Loop IDs until a free is found.
			user.ID = uuid.New()
			key = ur.userKey(user.ID)

			_, err := txn.Get(key)
			if errors.Is(err, badger.ErrKeyNotFound) {
				break
			}

			if err != nil {
				return fmt.Errorf("failed querying key %s: %w", key, err)
			}
		}

		bytes, err := json.Marshal(user)
		if err != nil {
			return fmt.Errorf("failed marshalling user: %w", err)
		}

		e := badger.NewEntry(key, bytes)

		return txn.SetEntry(e)
	})
	if err != nil {
		err = fmt.Errorf("failed saving user entity: %w", err)
		return
	}

	u = user

	return
}

// UpdateUser implements repository.UserRepository.
func (ur *UserRepository) UpdateUser(newU entity.User) (u entity.User, err error) {
	key := ur.userKey(newU.ID)

	err = ur.db.Update(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if errors.Is(err, badger.ErrKeyNotFound) {
			return repository.ErrUserNotFound
		}

		if err != nil {
			return fmt.Errorf("failed querying key %s: %w", key, err)
		}

		err = item.Value(func(bytes []byte) error {
			err := json.Unmarshal(bytes, &u)
			if err != nil {
				return fmt.Errorf("failed unmarshalling old user entry: %w", err)
			}

			return nil
		})
		if err != nil {
			return fmt.Errorf("failed getting old user value: %w", err)
		}

		u.Merge(newU)

		bytes, err := json.Marshal(u)
		if err != nil {
			return fmt.Errorf("failed marshalling user: %w", err)
		}

		e := badger.NewEntry(key, bytes)

		return txn.SetEntry(e)
	})
	if err != nil {
		err = fmt.Errorf("failed saving user entity: %w", err)
		return
	}

	u = newU

	return
}

// DeleteUser implements repository.UserRepository.
func (ur *UserRepository) DeleteUser(userID uuid.UUID) (err error) {
	key := ur.userKey(userID)

	err = ur.db.Update(func(txn *badger.Txn) error {
		_, err := txn.Get(key)
		if errors.Is(err, badger.ErrKeyNotFound) {
			return repository.ErrUserNotFound
		}

		if err != nil && !errors.Is(err, badger.ErrKeyNotFound) {
			return fmt.Errorf("failed querying key %s: %w", key, err)
		}

		err = txn.Delete(key)
		if err != nil {
			return fmt.Errorf("failed deleting key %s: %w", key, err)
		}

		return nil
	})
	if err != nil {
		err = fmt.Errorf("failed deleting user entity: %w", err)
		return
	}

	return
}

func (ur *UserRepository) userKey(userID uuid.UUID) []byte {
	prefix := string(ur.prefix())
	return []byte(prefix + userID.String())
}

func (ur *UserRepository) prefix() []byte {
	return []byte("user:")
}
