package badgerdb

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
)

var _ repository.ScanRepository = &ScanRepository{}

type ScanRepository struct {
	log    zerolog.Logger
	config config.ManagementConfig
	db     *badger.DB
}

func NewScanRepository(
	log zerolog.Logger,
	config config.ManagementConfig,
	db *badger.DB,
) *ScanRepository {
	return &ScanRepository{
		log:    log.With().Str("component", "management/badgerdb_scan_repository").Logger(),
		config: config,
		db:     db,
	}
}

// GetScan implements repository.ScanRepository.
func (sr *ScanRepository) GetScan(scanID string) (s entity.Scan, err error) {
	key := sr.scanKey(scanID)

	err = sr.db.View(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if err != nil {
			if errors.Is(err, badger.ErrKeyNotFound) {
				return nil
			}
			return fmt.Errorf("failed getting scan: %w", err)
		}

		var scan entity.Scan
		err = item.Value(func(val []byte) error {
			return json.Unmarshal(val, &scan)
		})
		if err != nil {
			return fmt.Errorf("failed unmarshalling scan: %w", err)
		}

		s = scan
		return nil
	})

	return
}

// GetScans implements repository.ScanRepository.
func (sr *ScanRepository) GetScans() (s []entity.Scan, err error) {
	err = sr.db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		opts.Prefix = []byte("scan:")

		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			item := it.Item()

			var scan entity.Scan
			err := item.Value(func(val []byte) error {
				return json.Unmarshal(val, &scan)
			})
			if err != nil {
				return fmt.Errorf("failed unmarshalling scan: %w", err)
			}

			s = append(s, scan)
		}

		return nil
	})

	return
}

// StoreScan implements repository.ScanRepository.
func (sr *ScanRepository) StoreScan(scan entity.Scan) error {
	key := sr.scanKey(scan.ID.String())

	value, err := json.Marshal(scan)
	if err != nil {
		return fmt.Errorf("failed marshalling scan: %w", err)
	}

	err = sr.db.Update(func(txn *badger.Txn) error {
		return txn.Set(key, value)
	})
	if err != nil {
		return fmt.Errorf("failed storing scan: %w", err)
	}

	return nil
}

// DeleteScan implements repository.ScanRepository.
func (sr *ScanRepository) DeleteScan(scanID string) error {
	key := sr.scanKey(scanID)

	return sr.db.Update(func(txn *badger.Txn) error {
		return txn.Delete(key)
	})
}

func (sr *ScanRepository) scanKey(scanID string) []byte {
	return []byte("scan:" + scanID)
}
