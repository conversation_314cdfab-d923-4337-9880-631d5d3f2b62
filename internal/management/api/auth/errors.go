package auth

import "errors"

// Autorization header errors.
var (
	ErrMissingAuthorizationHeader       = errors.New("missing Authorization header")
	ErrInvalidAuthorizationHeaderFormat = errors.New("invalid Authorization header format")
)

// Login errors.
var ErrInvalidCredentials = errors.New("invalid credentials")

// JWT token validation and creating errors.
var (
	ErrInvalidJWTSecret      = errors.New("invalid JWT secret")
	ErrInvalidJWT            = errors.New("invalid JWT token")
	ErrCannotParseJWT        = errors.New("cannot parse JWT token")
	ErrFailedGeneratingToken = errors.New("failed generating token")
)

// Claims errors.
var (
	ErrInvalidClaims       = errors.New("invalid claims")
	ErrInvalidClaimsFormat = errors.New("invalid claims format")
)
