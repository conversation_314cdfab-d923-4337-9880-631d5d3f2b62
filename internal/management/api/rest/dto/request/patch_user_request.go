package request

import (
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"github.com/google/uuid"
)

type PatchUserRequest struct {
	UserID string `binding:"required" uri:"userId"`
}

type PatchUserR<PERSON>questBody struct {
	User entity.AnonymousUser `binding:"required" json:"user"`
}

func (pur *PatchUserRequest) ToCommand(purb PatchUserRequestBody) (c command.UserEditCommand, err error) {
	userID, err := uuid.Parse(pur.UserID)
	if err != nil {
		err = fmt.Errorf("failed parsing uuid: %w", err)
		return
	}

	c = command.UserEditCommand{
		User: entity.User{
			ID:            userID,
			AnonymousUser: purb.User,
		},
	}

	return
}
