package request

import (
	"context"

	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
)

type GetStreamRequest struct {
	Address  string `binding:"required" uri:"streamId"`
	Metrics  bool   `                                  form:"metrics"`  // Include stream and program metrics
	Programs bool   `                                  form:"programs"` // Include programs
	Tracks   bool   `                                  form:"tracks"`   // Include tracks (implies programs=true)
}

func (gsr *GetStreamRequest) ToQuery(ctx context.Context) (q query.StreamQuery, err error) {
	address, err := value.ParseAddressRaw(gsr.Address)
	if err != nil {
		return query.StreamQuery{}, err
	}

	q = query.StreamQuery{
		Ctx:     ctx,
		Address: address,
		Filter: query.StreamFilter{
			Metrics:  gsr.Metrics,
			Programs: gsr.Programs,
			Tracks:   gsr.Tracks,
		},
	}

	return
}
