package request

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/dto"
)

type PatchConfigurationsRequest struct {
	ManagementConfiguration dto.Configuration      `binding:"required" json:"configurationManagement"`
	RelayConfiguration      dto.RelayConfiguration `binding:"required" json:"configurationRelay"`
}

func (pcr *PatchConfigurationsRequest) ToCommand() command.ConfigurationEditCommand {
	return command.ConfigurationEditCommand{
		ManagementConfiguration: pcr.ManagementConfiguration.ToConfigurationEntity(),
		RelayConfiguration:      pcr.RelayConfiguration.ToRelayConfigurationEntity(),
	}
}
