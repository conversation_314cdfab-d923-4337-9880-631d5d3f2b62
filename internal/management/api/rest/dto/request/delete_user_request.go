package request

import (
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"github.com/google/uuid"
)

type DeleteUserRequest struct {
	UserID string `binding:"required" uri:"userId"`
}

func (gur *GetUserRequest) ToCommand() (c command.UserDeleteCommand, err error) {
	userID, err := uuid.Parse(gur.UserID)
	if err != nil {
		err = fmt.Errorf("failed parsing uuid: %w", err)
		return
	}

	c = command.UserDeleteCommand{
		UserID: userID,
	}

	return
}
