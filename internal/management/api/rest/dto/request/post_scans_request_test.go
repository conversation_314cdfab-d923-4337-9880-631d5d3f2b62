package request_test

import (
	"net/netip"
	"testing"

	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPostScansRequest(t *testing.T) {
	t.<PERSON>()

	t.Run("Hosts_ValidCIDR28_ReturnsCorrectAddresses", func(t *testing.T) {
		t.<PERSON>()

		r := request.PostScansRequest{
			MulticastCIDR: "***********/28",
		}

		addresses, err := r.Hosts()
		require.NoError(t, err)

		assert.Equal(t,
			[]netip.Addr{
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********"),
				netip.MustParseAddr("***********0"),
				netip.MustParseAddr("***********1"),
				netip.MustParseAddr("***********2"),
				netip.MustParseAddr("***********3"),
				netip.MustParseAddr("***********4"),
				netip.MustParseAddr("***********5"),
			},
			addresses,
		)
	})

	t.Run("Hosts_ValidCIDR32_ReturnsCorrectAddress", func(t *testing.T) {
		t.Parallel()

		r := request.PostScansRequest{
			MulticastCIDR: "***********4/32",
		}

		addresses, err := r.Hosts()
		require.NoError(t, err)

		assert.Equal(t,
			[]netip.Addr{
				netip.MustParseAddr("***********4"),
			},
			addresses,
		)
	})

	t.Run("Hosts_InvalidIPAddress_ReturnsError", func(t *testing.T) {
		t.Parallel()

		r := request.PostScansRequest{
			MulticastCIDR: "***********56/28",
		}

		_, err := r.Hosts()
		require.Error(t, err)
	})

	t.Run("Hosts_InvalidCIDR64_ReturnsError", func(t *testing.T) {
		t.Parallel()

		r := request.PostScansRequest{
			MulticastCIDR: "*************/64",
		}

		_, err := r.Hosts()
		require.Error(t, err)
	})
}
