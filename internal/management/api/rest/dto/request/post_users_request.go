package request

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"github.com/google/uuid"
)

type PostUsersRequest struct {
	User entity.AnonymousUser `binding:"required" json:"user"`
}

func (pur *PostUsersRequest) ToCommand() command.UserCreateCommand {
	return command.UserCreateCommand{
		User: entity.User{
			ID:            uuid.New(),
			AnonymousUser: pur.User,
		},
	}
}
