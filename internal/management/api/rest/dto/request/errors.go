package request

import "errors"

var (
	ErrInvalidAddressFormat = errors.New("invalid address format")
	ErrInvalidPortFormat    = errors.New("invalid port format")
	ErrInvalidIPv4Address   = errors.New("invalid ipv4 address")
	ErrInvalidPort          = errors.New("invalid port")
	ErrIPAddressesRequired  = errors.New("ip addresses are required")
	ErrPortRequired         = errors.New("port is required")
)
