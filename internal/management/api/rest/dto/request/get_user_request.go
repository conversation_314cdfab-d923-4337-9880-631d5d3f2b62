package request

import (
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"github.com/google/uuid"
)

type GetUserRequest struct {
	UserID string `binding:"required" uri:"userId"`
}

func (gur *GetUserRequest) ToQuery() (q query.UserQuery, err error) {
	userID, err := uuid.Parse(gur.UserID)
	if err != nil {
		err = fmt.Errorf("failed parsing uuid: %w", err)
		return
	}

	q = query.UserQuery{
		UserID: userID,
	}

	return
}
