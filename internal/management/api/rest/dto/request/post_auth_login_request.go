package request

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
)

type PostAuthLoginRequest struct {
	Username string `binding:"required" json:"username"`
	Password string `binding:"required" json:"password"`
}

func (palr *PostAuthLoginRequest) ToCommand() command.AuthLoginCommand {
	return command.AuthLoginCommand{
		Username: palr.Username,
		Password: palr.Password,
	}
}
