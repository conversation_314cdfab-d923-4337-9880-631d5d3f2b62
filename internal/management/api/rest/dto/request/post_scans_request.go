package request

import (
	"net/netip"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
)

type PostScansRequest struct {
	MulticastCIDR string `json:"multicastCidr"`
	Port          uint16 `json:"port"`
}

func (psr *PostScansRequest) ToCommand() (command.ScanCommand, error) {
	addresses, err := psr.Hosts()
	if err != nil {
		return command.ScanCommand{}, err
	}

	if len(addresses) == 0 {
		return command.ScanCommand{}, ErrIPAddressesRequired
	}

	if psr.Port == 0 {
		return command.ScanCommand{}, ErrPortRequired
	}

	return command.ScanCommand{
		IPAddresses: addresses,
		Parameters: value.ScanParameters{
			MulticastCIDR: psr.MulticastCIDR,
			Port:          psr.Port,
		},
	}, nil
}

func (psr *PostScansRequest) Hosts() ([]netip.Addr, error) {
	prefix, err := netip.ParsePrefix(psr.MulticastCIDR)
	if err != nil {
		return nil, err
	}

	var ips []netip.Addr
	for addr := prefix.Addr(); prefix.Contains(addr); addr = addr.Next() {
		ips = append(ips, addr)
	}

	return ips, nil
}
