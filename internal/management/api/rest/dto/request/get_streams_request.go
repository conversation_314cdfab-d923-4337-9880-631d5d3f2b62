package request

import (
	"context"

	"git.moderntv.eu/multicast-probe/internal/management/application/query"
)

type GetStreamsRequest struct {
	Metrics  bool `form:"metrics"  json:"metrics"`  // Include stream and program metrics
	Programs bool `form:"programs" json:"programs"` // Include programs
	Tracks   bool `form:"tracks"   json:"tracks"`   // Include tracks (implies programs=true)
}

func (gsr *GetStreamsRequest) ToQuery(ctx context.Context) query.StreamsQuery {
	return query.StreamsQuery{
		Ctx: ctx,
		Filter: query.StreamFilter{
			Metrics:  gsr.Metrics,
			Programs: gsr.Programs,
			Tracks:   gsr.Tracks,
		},
	}
}
