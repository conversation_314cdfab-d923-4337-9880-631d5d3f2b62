package response

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
)

type GetUsersResponse struct {
	Users []entity.User `json:"users,omitempty" validate:"optional"`
}

func NewGetUsersResponseFromQueryResult(r query.UsersQueryResult) GetUsersResponse {
	return GetUsersResponse{
		Users: r.Users,
	}
}
