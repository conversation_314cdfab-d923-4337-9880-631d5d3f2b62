package response

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/dto"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
)

type GetConfigurationsResponse struct {
	ManagementConfiguration dto.Configuration      `json:"configurationManagement"`
	RelayConfiguration      dto.RelayConfiguration `json:"configurationRelay"`
}

func NewGetConfigurationResponseFromQueryResult(r query.ConfigurationQueryResult) GetConfigurationsResponse {
	return GetConfigurationsResponse{
		ManagementConfiguration: dto.ToConfigurationResponse(r.ManagementConfiguration),
		RelayConfiguration:      dto.ToRelayConfigurationResponse(r.RelayConfiguration),
	}
}
