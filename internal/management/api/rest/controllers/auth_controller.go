package controllers

import (
	"errors"
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/management/api/auth"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type AuthController struct {
	log            zerolog.Logger
	authMiddleware gin.HandlerFunc
	authService    interfaces.AuthService
}

func NewAuthController(
	log zerolog.Logger,
	authMiddleware gin.HandlerFunc,
	authService interfaces.AuthService,
) *AuthController {
	return &AuthController{
		log:            log.With().Str("component", "management/auth_controller").<PERSON><PERSON>(),
		authService:    authService,
		authMiddleware: authMiddleware,
	}
}

func (ac *AuthController) GetRoutes() chttp.RoutingGroup {
	return chttp.RoutingGroup{
		Base: "/api/v1/auth",
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/login": {
				http.MethodPost: []gin.HandlerFunc{ac.PostLoginController},
			},
			"/refresh": {
				http.MethodPost: []gin.HandlerFunc{ac.PostRefreshController},
			},
			"/status": {
				http.MethodGet: []gin.HandlerFunc{
					ac.authMiddleware,
					ac.GetStatusController, // Requires authentication
				},
			},
		},
	}
}

// GetStatusController checks correct authentication.
//
//	@ID			getAuthStatus
//	@Summary	Validates correct authentication
//	@Tags		auth
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/auth/status [get]
//	@Success	200				{object}	response.DataResponse[response.GetAuthStatusResponse]
//	@Failure	400,401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (ac *AuthController) GetStatusController(c *gin.Context) {
	// Middleware checks for correct credentials.
	c.Status(http.StatusOK)
}

// PostLoginController logs in user.
//
//	@ID			postAuthLogin
//	@Summary	Logs in user
//	@Tags		auth
//	@Accept		json
//	@Produce	json
//	@Param		credentials	body	request.PostAuthLoginRequest	true	"User credentials"
//	@Router		/api/v1/auth/login [post]
//	@Success	200		{object}	response.DataResponse[response.PostAuthLoginResponse]
//	@Failure	400,500	{object}	response.ErrorResponse
func (ac *AuthController) PostLoginController(c *gin.Context) {
	var req request.PostAuthLoginRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, response.ErrorResponse{
			Error: err.Error(),
		})
		return
	}

	cmd := req.ToCommand()
	r, err := ac.authService.Login(cmd)
	if errors.Is(err, auth.ErrInvalidCredentials) {
		ac.log.Trace().
			Err(auth.ErrInvalidCredentials).
			Msg("user failed to login")

		c.JSON(http.StatusBadRequest, response.ErrorResponse{
			Error: auth.ErrInvalidCredentials.Error(),
		})
		return
	}

	if err != nil {
		ac.log.Error().
			Err(err).
			Msg("user failed to login")

		c.JSON(http.StatusInternalServerError, response.ErrorResponse{
			Error: "internal processing error",
		})
		return
	}

	res := response.NewPostAuthLoginResponseFromCommandResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.PostAuthLoginResponse]{
		Data: &res,
	})
}

// PostRefreshController refreshed auth token.
//
//	@ID			postAuthRefresh
//	@Summary	Refreshes token
//	@Tags		auth
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/auth/refresh [post]
//	@Success	200		{object}	response.DataResponse[response.PostAuthRefreshResponse]
//	@Failure	400,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (ac *AuthController) PostRefreshController(c *gin.Context) {
	var req request.PostAuthRefreshRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, response.ErrorResponse{
			Error: err.Error(),
		})
		return
	}

	cmd := req.ToCommand()
	r, err := ac.authService.RefreshToken(cmd)
	if errors.Is(err, auth.ErrCannotParseJWT) ||
		errors.Is(err, auth.ErrInvalidJWT) ||
		errors.Is(err, auth.ErrInvalidClaimsFormat) {
		ac.log.Trace().
			Err(err).
			Msg("user failed to refresh token")

		c.JSON(http.StatusBadRequest, response.ErrorResponse{
			Error: err.Error(),
		})
		return
	}

	if err != nil {
		ac.log.Error().
			Err(err).
			Msg("failed to refresh token")

		c.JSON(http.StatusInternalServerError, response.ErrorResponse{
			Error: "internal processing error",
		})
		return
	}

	res := response.NewPostAuthRefreshResponseFromCommandResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.PostAuthRefreshResponse]{
		Data: &res,
	})
}
