package controllers

import (
	"fmt"
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type ConfigurationController struct {
	log                  zerolog.Logger
	authMiddleware       gin.HandlerFunc
	configurationService interfaces.ConfigurationService
}

func NewConfigurationController(
	log zerolog.Logger,
	authMiddleware gin.HandlerFunc,
	configurationService interfaces.ConfigurationService,
) *ConfigurationController {
	return &ConfigurationController{
		log: log.With().
			Str("component", "management/configuration_controller").
			Logger(),
		authMiddleware:       authMiddleware,
		configurationService: configurationService,
	}
}

func (cc *ConfigurationController) GetRoutes() chttp.RoutingGroup {
	middlewares := []gin.HandlerFunc{cc.authMiddleware}
	return chttp.RoutingGroup{
		Base:       "/api/v1",
		Middleware: middlewares,
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/configurations": {
				http.MethodGet:   []gin.HandlerFunc{cc.GetConfigurationsController},
				http.MethodPatch: []gin.HandlerFunc{cc.PatchConfigurationsController},
			},
		},
	}
}

// GetConfigurationsController returns configurations.
//
//	@ID			getConfigurations
//	@Summary	Gets list of configurations
//	@Tags		configurations
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/configurations [get]
//	@Success	200			{object}	response.DataResponse[response.GetConfigurationsResponse]
//	@Failure	401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (cc *ConfigurationController) GetConfigurationsController(c *gin.Context) {
	var req request.GetConfigurationsRequest
	q := req.ToQuery()

	r, err := cc.configurationService.Configuration(q)
	if err != nil {
		cc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running configuration query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running configuration query",
			},
		)
		return
	}

	res := response.NewGetConfigurationResponseFromQueryResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.GetConfigurationsResponse]{
		Data: &res,
	})
}

// PatchConfigurationsController updates configurations.
//
//	@ID			patchConfigurations
//	@Summary	Updates configurations
//	@Tags		configurations
//	@Accept		json
//	@Produce	json
//	@Param		configuration	body	request.PatchConfigurationsRequest	true	"Patched configuration"
//	@Router		/api/v1/configurations [patch]
//	@Success	200				{object}	response.DataResponse[response.PatchConfigurationsResponse]
//	@Failure	400,401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (cc *ConfigurationController) PatchConfigurationsController(c *gin.Context) {
	var req request.PatchConfigurationsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cc.log.Debug().
			Err(err).
			Msg("failed parsing patch configuration request body")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: fmt.Sprintf("failed parsing request body: %v", err),
			},
		)
		return
	}

	cmd := req.ToCommand()
	r, err := cc.configurationService.SetConfiguration(cmd)
	if err != nil {
		cc.log.Error().
			Err(err).
			Interface("command", cmd).
			Msg("failed running configuration edit command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running configuration query",
			},
		)
		return
	}

	res := response.NewPatchConfigurationResponseFromCommandResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.PatchConfigurationsResponse]{
		Data: &res,
	})
}
