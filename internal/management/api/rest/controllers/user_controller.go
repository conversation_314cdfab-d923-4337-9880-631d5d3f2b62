package controllers

import (
	"errors"
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type UserController struct {
	log            zerolog.Logger
	authMiddleware gin.HandlerFunc
	userService    interfaces.UserService
}

func NewUserController(
	log zerolog.Logger,
	authMiddleware gin.HandlerFunc,
	userService interfaces.UserService,
) *UserController {
	return &UserController{
		log:            log.With().Str("component", "management/user_controller").Logger(),
		authMiddleware: authMiddleware,
		userService:    userService,
	}
}

func (uc *UserController) GetRoutes() chttp.RoutingGroup {
	middlewares := []gin.HandlerFunc{uc.authMiddleware}
	return chttp.RoutingGroup{
		Base:       "/api/v1",
		Middleware: middlewares,
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/users": {
				http.MethodGet:  []gin.HandlerFunc{uc.GetUsersController},
				http.MethodPost: []gin.HandlerFunc{uc.PostUsersController},
			},
			"/users/:userId": {
				http.MethodGet:    []gin.HandlerFunc{uc.GetUserController},
				http.MethodPatch:  []gin.HandlerFunc{uc.PatchUserController},
				http.MethodDelete: []gin.HandlerFunc{uc.DeleteUserController},
			},
		},
	}
}

// GetUsersController retrieves users.
//
//	@ID			getUsers
//	@Summary	Gets list of users
//	@Tags		users
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/users [get]
//	@Success	200			{object}	response.DataResponse[response.GetUsersResponse]
//	@Failure	401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (uc *UserController) GetUsersController(c *gin.Context) {
	var req request.GetUsersRequest

	q := req.ToQuery()

	r, err := uc.userService.GetUsers(q)
	if err != nil {
		uc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running users query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running users query",
			},
		)
		return
	}

	res := response.NewGetUsersResponseFromQueryResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.GetUsersResponse]{
		Data: &res,
	})
}

// PostUsersController creates new users.
//
//	@ID			postUsers
//	@Summary	Creates users
//	@Tags		users
//	@Accept		json
//	@Produce	json
//	@Param		user	body	request.PostUsersRequest	true	"User"
//	@Router		/api/v1/users [post]
//	@Success	200				{object}	response.DataResponse[response.PostUsersResponse]
//	@Failure	400,401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (uc *UserController) PostUsersController(c *gin.Context) {
	var req request.PostUsersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed to bind json body")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind json body: " + err.Error(),
			},
		)
		return
	}

	cmd := req.ToCommand()

	r, err := uc.userService.CreateUser(cmd)
	if err != nil {
		uc.log.Error().
			Err(err).
			Interface("command", cmd).
			Msg("failed creating user command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running user create command",
			},
		)
		return
	}

	res := response.NewPostUsersResponseFromCommandResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.PostUsersResponse]{
		Data: &res,
	})
}

// GetUserController retrieves user.
//
//	@ID			getUser
//	@Summary	Gets user
//	@Tags		users
//	@Accept		json
//	@Produce	json
//	@Param		userId	path	string	true	"User ID"
//	@Router		/api/v1/users/{userId} [get]
//	@Success	200					{object}	response.DataResponse[response.GetUserResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (uc *UserController) GetUserController(c *gin.Context) {
	var req request.GetUserRequest
	if err := c.ShouldBindUri(&req); err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed to bind path parameters")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind path parameters: " + err.Error(),
			},
		)
		return
	}

	q, err := req.ToQuery()
	if err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed converting request to query")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to query: " + err.Error(),
			},
		)
		return
	}

	r, err := uc.userService.GetUser(q)
	if errors.Is(err, repository.ErrUserNotFound) {
		uc.log.Trace().
			Err(err).
			Interface("query", q).
			Msg("failed running user query")

		c.JSON(http.StatusNotFound,
			response.ErrorResponse{
				Error: repository.ErrUserNotFound.Error(),
			},
		)
		return
	}

	if err != nil {
		uc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running user query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running user query",
			},
		)
		return
	}

	res := response.NewGetUserResponseFromQueryResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.GetUserResponse]{
		Data: &res,
	})
}

// PatchUserController edits user.
//
//	@ID			putUser
//	@Summary	Updates user
//	@Tags		users
//	@Accept		json
//	@Produce	json
//	@Param		userId	path	string						true	"User ID"
//	@Param		user	body	request.PatchUserRequest	true	"Patched user"
//	@Router		/api/v1/users/{userId} [patch]
//	@Success	200					{object}	response.DataResponse[response.PatchUserResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (uc *UserController) PatchUserController(c *gin.Context) {
	var req request.PatchUserRequest
	if err := c.ShouldBindUri(&req); err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed to bind uri parameters")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind uri parameters: " + err.Error(),
			},
		)
		return
	}

	var body request.PatchUserRequestBody
	if err := c.ShouldBindJSON(&body); err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed to bind json body")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind json body: " + err.Error(),
			},
		)
		return
	}

	cmd, err := req.ToCommand(body)
	if err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed converting request to command")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to command: " + err.Error(),
			},
		)
		return
	}

	r, err := uc.userService.EditUser(cmd)
	if errors.Is(err, repository.ErrUserNotFound) {
		uc.log.Trace().
			Err(err).
			Interface("command", cmd).
			Msg("failed running user edit command")

		c.JSON(http.StatusNotFound,
			response.ErrorResponse{
				Error: repository.ErrUserNotFound.Error(),
			},
		)
		return
	}

	if err != nil {
		uc.log.Error().
			Err(err).
			Interface("command", cmd).
			Msg("failed running user edit command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running user edit command",
			},
		)
		return
	}

	res := response.NewPatchUserResponseFromCommandResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.PatchUserResponse]{
		Data: &res,
	})
}

// DeleteUserController deletes user.
//
//	@ID			deleteUser
//	@Summary	Deletes user
//	@Tags		users
//	@Accept		json
//	@Produce	json
//	@Param		userId	path	string						true	"User ID"
//	@Router		/api/v1/users/{userId} [patch]
//	@Success	200					{object}	response.DataResponse[response.DeleteUserResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (uc *UserController) DeleteUserController(c *gin.Context) {
	var req request.GetUserRequest
	if err := c.ShouldBindUri(&req); err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed to bind path parameters")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind path parameters: " + err.Error(),
			},
		)
		return
	}

	cmd, err := req.ToCommand()
	if err != nil {
		uc.log.Trace().
			Err(err).
			Msg("failed converting request to command")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to command: " + err.Error(),
			},
		)
		return
	}

	_, err = uc.userService.DeleteUser(cmd)
	if errors.Is(err, repository.ErrUserNotFound) {
		uc.log.Trace().
			Err(err).
			Interface("command", cmd).
			Msg("failed running delete user command")

		c.JSON(http.StatusNotFound,
			response.ErrorResponse{
				Error: repository.ErrUserNotFound.Error(),
			},
		)
		return
	}

	if err != nil {
		uc.log.Error().
			Err(err).
			Interface("command", cmd).
			Msg("failed running delete user command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running delete user command",
			},
		)
		return
	}

	c.Status(http.StatusOK)
}
