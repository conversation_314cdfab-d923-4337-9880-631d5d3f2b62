package controllers

import (
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type StreamController struct {
	log            zerolog.Logger
	authMiddleware gin.HandlerFunc
	streamService  interfaces.StreamService
}

func NewStreamController(
	log zerolog.Logger,
	authMiddleware gin.HandlerFunc,
	streamService interfaces.StreamService,
) *StreamController {
	return &StreamController{
		log:            log.With().Str("component", "management/stream_controller").Logger(),
		authMiddleware: authMiddleware,
		streamService:  streamService,
	}
}

func (sc *StreamController) GetRoutes() chttp.RoutingGroup {
	middlewares := []gin.HandlerFunc{sc.authMiddleware}
	return chttp.RoutingGroup{
		Base:       "/api/v1",
		Middleware: middlewares,
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/streams": {
				http.MethodGet: []gin.HandlerFunc{sc.GetStreamsController},
			},
			"/streams/:streamId": {
				http.MethodGet: []gin.HandlerFunc{sc.GetStreamController},
			},
		},
	}
}

// GetStreamsController retrieves streams.
//
//	@ID			getStreams
//	@Summary	Gets list of streams
//	@Tags		streams
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/streams [get]
//	@Success	200			{object}	response.DataResponse[response.GetStreamsResponse]
//	@Failure	401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *StreamController) GetStreamsController(c *gin.Context) {
	var req request.GetStreamsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		sc.log.Trace().
			Err(err).
			Msg("failed to bind query parameters")
		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind query parameters: " + err.Error(),
			},
		)
		return
	}

	q := req.ToQuery(c.Request.Context())

	r, err := sc.streamService.GetStreams(q)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running streams query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running streams query",
			},
		)
		return
	}

	res := response.NewGetStreamsResponseFromQueryResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.GetStreamsResponse]{
		Data: &res,
	})
}

// GetStreamController retrieves stream.
//
//	@ID			getStream
//	@Summary	Gets stream
//	@Tags		streams
//	@Accept		json
//	@Produce	json
//	@Param		streamId	path	string	true	"Stream ID"
//	@Param		metrics		query	bool	false	"Export stream metrics"
//	@Param		programs	query	bool	false	"Export stream programs"
//	@Param		tracks		query	bool	false	"Export stream program tracks. It is ignored when programs are not exported."
//	@Router		/api/v1/streams/{streamId} [get]
//	@Param		streamId			path		string	true	"ID of stream represented by IP address and port combination"
//	@Success	200					{object}	response.DataResponse[response.GetStreamResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *StreamController) GetStreamController(c *gin.Context) {
	var req request.GetStreamRequest
	if err := c.ShouldBindUri(&req); err != nil {
		sc.log.Trace().
			Err(err).
			Msg("failed to bind URI parameters")
		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind path parameters: " + err.Error(),
			},
		)
		return
	}
	if err := c.ShouldBindQuery(&req); err != nil {
		sc.log.Trace().
			Err(err).
			Msg("failed to bind query parameters")
		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind query parameters: " + err.Error(),
			},
		)
		return
	}

	q, err := req.ToQuery(c.Request.Context())
	if err != nil {
		sc.log.Trace().
			Err(err).
			Interface("request", req).
			Msg("failed to convert request to query")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to query: " + err.Error(),
			},
		)
		return
	}

	r, err := sc.streamService.GetStream(q)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running stream query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running stream query",
			},
		)
		return
	}

	res := response.NewGetStreamResponseFromQueryResult(r)

	c.JSON(http.StatusOK, response.DataResponse[response.GetStreamResponse]{
		Data: &res,
	})
}
