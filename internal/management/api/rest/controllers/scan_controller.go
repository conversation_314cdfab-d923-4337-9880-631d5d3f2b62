package controllers

import (
	"errors"
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type ScanController struct {
	log            zerolog.Logger
	authMiddleware gin.HandlerFunc
	scanService    interfaces.ScanService
}

func NewScanController(
	log zerolog.Logger,
	authMiddleware gin.HandlerFunc,
	scanService interfaces.ScanService,
) *ScanController {
	return &ScanController{
		log:            log.With().Str("component", "management/scan_controller").Logger(),
		authMiddleware: authMiddleware,
		scanService:    scanService,
	}
}

func (sc *ScanController) GetRoutes() chttp.RoutingGroup {
	middlewares := []gin.HandlerFunc{sc.authMiddleware}
	return chttp.RoutingGroup{
		Base:       "/api/v1",
		Middleware: middlewares,
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/scans": {
				http.MethodGet:  []gin.HandlerFunc{sc.GetScansController},
				http.MethodPost: []gin.HandlerFunc{sc.PostScansController},
			},
			"/scans/:scanId": {
				http.MethodGet:    []gin.HandlerFunc{sc.GetScanController},
				http.MethodDelete: []gin.HandlerFunc{sc.CancelScanController},
			},
		},
	}
}

// GetScansController retrieves list of scans.
//
//	@ID			getScans
//	@Summary	Gets list of scans
//	@Tags		scans
//	@Accept		json
//	@Produce	json
//	@Router		/api/v1/scans [get]
//	@Success	200			{object}	response.DataResponse[response.GetScansResponse]
//	@Failure	401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *ScanController) GetScansController(c *gin.Context) {
	scans, err := sc.scanService.GetScans()
	if err != nil {
		sc.log.Debug().
			Err(err).
			Msg("failed to get scans")

		c.JSON(http.StatusInternalServerError, response.ErrorResponse{Error: "Failed to get scans"})
		return
	}
	res := response.NewGetScansResponseFromQueryResult(scans)

	c.JSON(http.StatusOK, response.DataResponse[response.GetScansResponse]{Data: &res})
}

// PostScansController creates new scan.
//
//	@ID			postScans
//	@Summary	Creates scans
//	@Tags		scans
//	@Accept		json
//	@Produce	json
//	@Param		scan	body	request.PostScansRequest	true	"Scan"
//	@Router		/api/v1/scans [post]
//	@Success	200				{object}	response.DataResponse[response.PostScansResponse]
//	@Failure	400,401,403,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *ScanController) PostScansController(c *gin.Context) {
	var req request.PostScansRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "Invalid request format"})
		return
	}

	scanCommand, err := req.ToCommand()
	if err != nil {
		sc.log.Debug().
			Err(err).
			Interface("request", req).
			Msg("failed to parse multicast CIDR")

		switch {
		case errors.Is(err, request.ErrIPAddressesRequired):
			c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "IP addresses are required"})
		case errors.Is(err, request.ErrPortRequired):
			c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "Port is required"})
		default:
			c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "Invalid request format"})
		}
		return
	}

	scan, err := sc.scanService.StartScan(scanCommand)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("request", req).
			Msg("failed to scan")

		c.JSON(http.StatusInternalServerError, response.ErrorResponse{Error: "Failed to scan"})
		return
	}

	res := response.NewPostScanResponseFromCommandResult(scan)

	c.JSON(http.StatusOK, response.DataResponse[response.PostScansResponse]{Data: &res})
}

// GetScanController retrieves scan.
//
//	@ID			getScan
//	@Tags		scans
//	@Accept		json
//	@Produce	json
//	@Param		scanId	path	string	true	"Scan ID"
//	@Router		/api/v1/scans/{scanId} [get]
//	@Success	200					{object}	response.DataResponse[response.GetScanResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *ScanController) GetScanController(c *gin.Context) {
	var req request.GetScanRequest

	if err := c.ShouldBindUri(&req); err != nil {
		sc.log.Debug().
			Err(err).
			Interface("request", req).
			Msg("failed to bind URI")

		c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "Invalid request format"})
		return
	}

	query := req.ToQuery()

	scan, err := sc.scanService.GetScan(query)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("request", req).
			Msg("failed to get scan")

		c.JSON(http.StatusInternalServerError, response.ErrorResponse{Error: "Failed to get scan"})
		return
	}

	res := response.NewGetScanResponseFromQueryResult(scan)

	c.JSON(http.StatusOK, response.DataResponse[response.GetScanResponse]{Data: &res})
}

// CancelScanController cancels scan.
//
//	@ID		cancelScan
//	@Tags	scans
//	@Accept	json
//	@Produce	json
//	@Param	scanId	path	string	true	"Scan ID"
//	@Router	/api/v1/scans/{scanId} [delete]
//	@Success	200				{object}	response.DataResponse[response.CancelScanResponse]
//	@Failure	400,401,403,404,500	{object}	response.ErrorResponse
//	@Security	BearerAuth
func (sc *ScanController) CancelScanController(c *gin.Context) {
	var req request.CancelScanRequest

	if err := c.ShouldBindUri(&req); err != nil {
		sc.log.Debug().
			Err(err).
			Interface("request", req).
			Msg("failed to bind URI")

		c.JSON(http.StatusBadRequest, response.ErrorResponse{Error: "Invalid request format"})
		return
	}

	command := req.ToCommand()

	result, err := sc.scanService.CancelScan(command)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("request", req).
			Msg("failed to cancel scan")

		c.JSON(
			http.StatusInternalServerError,
			response.ErrorResponse{Error: "Failed to cancel scan"},
		)
		return
	}

	res := response.NewCancelScanResponseFromCommandResult(result)

	c.JSON(http.StatusOK, response.DataResponse[response.CancelScanResponse]{Data: &res})
}
