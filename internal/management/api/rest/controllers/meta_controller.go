package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
	"github.com/swaggo/swag"
)

type MetaController struct {
	log         zerolog.Logger
	openAPISpec swag.Swagger
}

func NewMetaController(log zerolog.Logger, openAPISpec swag.Swagger) *MetaController {
	mc := &MetaController{
		log:         log.With().Str("component", "management/meta_controller").<PERSON><PERSON>(),
		openAPISpec: openAPISpec,
	}

	return mc
}

func (mc *MetaController) GetRoutes() chttp.RoutingGroup {
	return chttp.RoutingGroup{
		Base: "/_",
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/apidoc": {
				http.MethodGet: []gin.HandlerFunc{mc.getOpenAPIController},
			},
		},
	}
}

// APIDOC
//
//	@Summary		OpenAPI specification
//	@Description	Returns OpenAPI specification
//	@ID				apidoc
//	@Tags			common
//	@Router			/_/apidoc [get]
//	@Success		200	{object}	any
func (mc *MetaController) getOpenAPIController(c *gin.Context) {
	c.Data(http.StatusOK, gin.MIMEJSON, []byte(mc.openAPISpec.ReadDoc()))
}
