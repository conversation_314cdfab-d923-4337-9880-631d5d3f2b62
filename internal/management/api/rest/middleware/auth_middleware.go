package middleware

import (
	"context"
	"net/http"
	"regexp"

	"git.moderntv.eu/multicast-probe/internal/management/api/auth"
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/dto/response"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

type ContextKey string

const (
	jwtAuthTokenContextKey ContextKey = "authTokenPayload"
	// #nosec G101: This is not a hardcoded credential; it's a regex pattern.
	bearerRegex            string = `Bearer (?P<token>.+)` // Used by JWT token auth
	jwtAuthorizationHeader string = "Authorization"
	jwtRegexMinMatches     int    = 2
)

type AuthMiddleware struct {
	log         zerolog.Logger
	authService interfaces.AuthService
	headerRegex *regexp.Regexp
}

func NewJWTAuthMiddleware(
	log zerolog.Logger,
	authService interfaces.AuthService,
) gin.HandlerFunc {
	a := &AuthMiddleware{
		log:         log.With().Str("component", "management/auth_middleware").Logger(),
		authService: authService,
		headerRegex: regexp.MustCompile(bearerRegex),
	}

	return a.handler
}

func (am *AuthMiddleware) handler(c *gin.Context) {
	authHeader := c.GetHeader(jwtAuthorizationHeader)
	if authHeader == "" {
		am.log.Trace().
			Msg("user failed to provide authorization header")

		c.AbortWithStatusJSON(http.StatusUnauthorized,
			response.ErrorResponse{
				Error: auth.ErrMissingAuthorizationHeader.Error(),
			},
		)
		return
	}

	matches := am.headerRegex.FindStringSubmatch(authHeader)
	if len(matches) < jwtRegexMinMatches {
		am.log.Trace().
			Msg("user failed to provide valid authorization header")

		c.AbortWithStatusJSON(http.StatusUnauthorized,
			response.ErrorResponse{
				Error: auth.ErrInvalidAuthorizationHeaderFormat.Error(),
			},
		)
		return
	}

	token := matches[1]

	cmd := command.AuthValidateTokenCommand{
		Token: token,
	}

	r, err := am.authService.ValidateToken(cmd)
	if err != nil {
		am.log.Trace().
			Err(err).
			Msg("user failed to provide valid token")

		c.AbortWithStatusJSON(http.StatusUnauthorized,
			response.ErrorResponse{
				Error: auth.ErrInvalidJWT.Error(),
			},
		)
		return
	}

	// set value to request's context, which will be used in later queries
	c.Set(string(jwtAuthTokenContextKey), r.Claims)
	ctx := context.WithValue(c.Request.Context(), jwtAuthTokenContextKey, r.Claims)
	c.Request = c.Request.WithContext(ctx)

	c.Next()
}
