package services

import (
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"github.com/rs/zerolog"
)

var _ interfaces.StreamService = &StreamService{}

type StreamService struct {
	log   zerolog.Logger
	relay relay.Client
}

func NewStreamService(
	log zerolog.Logger,
	relay relay.Client,
) *StreamService {
	return &StreamService{
		log:   log.With().Str("component", "management/stream_service").Logger(),
		relay: relay,
	}
}

// GetStream implements interfaces.StreamService.
func (ss *StreamService) GetStream(q query.StreamQuery) (r query.StreamQueryResult, err error) {
	stream, err := ss.relay.StreamInfo(q.Ctx, q.Address)
	if err != nil {
		err = fmt.Errorf("failed getting stream info: %w", err)
		return
	}

	r = query.StreamQueryResult{
		Stream: ss.filterStream(stream, q.Filter),
	}
	return
}

// GetStreams implements interfaces.StreamService.
func (ss *StreamService) GetStreams(q query.StreamsQuery) (r query.StreamsQueryResult, err error) {
	streams, err := ss.relay.StreamsInfo(q.Ctx)
	if err != nil {
		err = fmt.Errorf("failed getting streams info: %w", err)
		return
	}

	filteredStreams := make([]entity.Stream, len(streams))
	for i, stream := range streams {
		filteredStreams[i] = ss.filterStream(stream, q.Filter)
	}

	r = query.StreamsQueryResult{
		Streams: filteredStreams,
	}
	return
}

// filterStream creates a filtered copy of a stream based on query parameters.
func (ss *StreamService) filterStream(
	stream entity.Stream,
	filter query.StreamFilter,
) entity.Stream {
	filteredStream := entity.Stream{
		Address: stream.Address,
		Clients: stream.Clients,
	}
	if filter.Metrics {
		filteredStream.Metrics = stream.Metrics
	}

	if filter.Programs || filter.Tracks {
		filteredStream.Programs = make([]entity.Program, len(stream.Programs))
		for i, p := range stream.Programs {
			filteredProgram := entity.Program{
				ProgramID: p.ProgramID,
				Title:     p.Title,
			}
			if filter.Metrics {
				filteredProgram.Metrics = p.Metrics
			}
			if filter.Tracks {
				filteredProgram.Tracks = p.Tracks
				if filter.Metrics {
					for j, track := range p.Tracks {
						filteredProgram.Tracks[j].Metrics = track.Metrics
					}
				}
			}

			filteredStream.Programs[i] = filteredProgram
		}
	}

	return filteredStream
}
