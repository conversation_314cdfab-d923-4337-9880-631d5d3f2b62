package services

import (
	"context"
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"github.com/rs/zerolog"
)

var _ interfaces.ConfigurationService = &ConfigurationService{}

type ConfigurationService struct {
	log                     zerolog.Logger
	config                  config.ManagementConfig
	relay                   relay.Client
	configurationRepository repository.ConfigurationRepository
}

func NewConfigurationService(
	log zerolog.Logger,
	config config.ManagementConfig,
	relay relay.Client,
	configurationRepository repository.ConfigurationRepository,
) *ConfigurationService {
	return &ConfigurationService{
		log: log.With().
			Str("component", "management/configuration_service").
			Logger(),
		config:                  config,
		relay:                   relay,
		configurationRepository: configurationRepository,
	}
}

// Configuration implements interfaces.ConfigurationService.
func (cs *ConfigurationService) Configuration(
	_ query.ConfigurationQuery,
) (r query.ConfigurationQueryResult, err error) {
	mgmtConfig, err := cs.configurationRepository.GetConfiguration()
	if err != nil {
		cs.log.Warn().
			Err(err).
			Msg("Failed to get management configuration")
		return query.ConfigurationQueryResult{}, fmt.Errorf(
			"failed to get management configuration: %w",
			err,
		)
	}

	// Get relay config from relay client
	relayConfig, err := cs.relay.GetConfiguration(context.Background())
	if err != nil {
		cs.log.Warn().
			Err(err).
			Msg("Failed to get relay configuration")
		return query.ConfigurationQueryResult{}, fmt.Errorf(
			"failed to get relay configuration: %w",
			err,
		)
	}

	cs.log.Debug().
		Interface("management_configuration", mgmtConfig).
		Interface("relay_configuration", relayConfig).
		Msg("Retrieved configuration")

	return query.ConfigurationQueryResult{
		ManagementConfiguration: mgmtConfig,
		RelayConfiguration:      relayConfig,
	}, nil
}

// SetConfiguration implements interfaces.ConfigurationService.
func (cs *ConfigurationService) SetConfiguration(
	q command.ConfigurationEditCommand,
) (r command.ConfigurationEditCommandResult, err error) {
	relayConfig, err := cs.relay.GetConfiguration(context.Background())
	if err != nil {
		cs.log.Warn().
			Err(err).
			Msg("Failed to get relay configuration")
		return command.ConfigurationEditCommandResult{}, fmt.Errorf(
			"failed to get relay configuration: %w",
			err,
		)
	}

	relayConfig.Merge(q.RelayConfiguration)
	err = cs.relay.SetConfiguration(context.Background(), relayConfig)
	if err != nil {
		cs.log.Warn().
			Err(err).
			Interface("relay_configuration", q.RelayConfiguration).
			Msg("Failed to set relay configuration")
		return command.ConfigurationEditCommandResult{}, fmt.Errorf(
			"failed to set relay configuration: %w",
			err,
		)
	}

	updatedConfig, err := cs.configurationRepository.UpdateConfiguration(q.ManagementConfiguration)
	if err != nil {
		cs.log.Warn().
			Err(err).
			Interface("configuration", updatedConfig).
			Msg("Failed to store management configuration")
		return command.ConfigurationEditCommandResult{}, fmt.Errorf(
			"failed to store management configuration: %w",
			err,
		)
	}

	cs.log.Debug().
		Interface("configuration", updatedConfig).
		Msg("Successfully updated configuration")

	return command.ConfigurationEditCommandResult{}, nil
}
