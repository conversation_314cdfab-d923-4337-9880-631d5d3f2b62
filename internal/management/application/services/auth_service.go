package services

import (
	"fmt"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/management/api/auth"
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog"
)

var _ interfaces.AuthService = &AuthService{}

type AuthService struct {
	log    zerolog.Logger
	config config.ManagementConfig
}

func NewAuthService(
	log zerolog.Logger,
	config config.ManagementConfig,
) (as *AuthService, err error) {
	if config.Auth.JWTSecret == "" {
		err = auth.ErrInvalidJWTSecret
		return
	}

	as = &AuthService{
		log:    log.With().Str("component", "management/auth_service").<PERSON>gger(),
		config: config,
	}

	return
}

func (as *AuthService) Login(
	c command.AuthLoginCommand,
) (r command.AuthLoginCommandResult, err error) {
	// TODO: Replace with actual user authentication (DB check, hashing, etc.)
	if c.Username != "admin" || c.Password != "password" {
		err = auth.ErrInvalidCredentials
		return
	}

	token, err := as.generateToken()
	if err != nil {
		as.log.Error().
			Err(err).
			Msg("failed to generate token")

		err = auth.ErrFailedGeneratingToken
		return
	}

	r = command.AuthLoginCommandResult{
		Token: token,
	}

	return
}

func (as *AuthService) RefreshToken(
	c command.AuthRefreshTokenCommand,
) (r command.AuthRefreshTokenCommandResult, err error) {
	_, err = as.validateToken(c.Token)
	if err != nil {
		err = fmt.Errorf("failed validating token: %w", err)
		return
	}

	token, err := as.generateToken()
	if err != nil {
		as.log.Error().
			Err(err).
			Msg("failed to generate new token")

		err = auth.ErrFailedGeneratingToken
		return
	}

	r = command.AuthRefreshTokenCommandResult{
		RefreshedToken: token,
	}

	return
}

func (as *AuthService) ValidateToken(
	c command.AuthValidateTokenCommand,
) (r command.AuthValidateTokenCommandResult, err error) {
	claims, err := as.validateToken(c.Token)
	if err != nil {
		return
	}

	r = command.AuthValidateTokenCommandResult{
		Claims: claims,
	}

	return
}

func (as *AuthService) validateToken(rawToken string) (claims *auth.Claims, err error) {
	token, err := jwt.ParseWithClaims(
		rawToken,
		&auth.Claims{},
		func(_ *jwt.Token) (any, error) {
			return []byte(as.config.Auth.JWTSecret), nil
		},
	)
	if err != nil {
		err = auth.ErrCannotParseJWT
		return
	}

	if !token.Valid {
		err = auth.ErrInvalidJWT
		return
	}

	claims, ok := token.Claims.(*auth.Claims)
	if !ok {
		err = auth.ErrInvalidClaimsFormat
		return
	}

	return
}

func (as *AuthService) generateToken() (string, error) {
	now := time.Now()
	claims := auth.Claims{
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(as.config.Auth.JWTExpiration)),
		},
		CustomClaims: auth.CustomClaims{},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	return token.SignedString([]byte(as.config.Auth.JWTSecret))
}
