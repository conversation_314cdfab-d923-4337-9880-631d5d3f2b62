package services

import (
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"github.com/rs/zerolog"
)

var _ interfaces.UserService = &UserService{}

type UserService struct {
	log            zerolog.Logger
	relay          relay.Client
	userRepository repository.UserRepository
}

func NewUserService(
	log zerolog.Logger,
	relay relay.Client,
	userRepository repository.UserRepository,
) *UserService {
	return &UserService{
		log:            log.With().Str("component", "management/user_service").Logger(),
		relay:          relay,
		userRepository: userRepository,
	}
}

// GetUsers implements interfaces.UserService.
func (us *UserService) GetUsers(_ query.UsersQuery) (r query.UsersQueryResult, err error) {
	u, err := us.userRepository.GetUsers()
	if err != nil {
		err = fmt.Errorf("failed getting users from repository: %w", err)
		return
	}

	r = query.UsersQueryResult{
		Users: u,
	}

	return
}

// GetUser implements interfaces.UserService.
func (us *UserService) GetUser(q query.UserQuery) (r query.UserQueryResult, err error) {
	u, err := us.userRepository.GetUser(q.UserID)
	if err != nil {
		err = fmt.Errorf("failed getting user from repository: %w", err)
		return
	}

	r = query.UserQueryResult{
		User: u,
	}

	return
}

// CreateUser implements interfaces.UserService.
func (us *UserService) CreateUser(
	q command.UserCreateCommand,
) (r command.UserCreateCommandResult, err error) {
	u, err := us.userRepository.CreateUser(q.User)
	if err != nil {
		err = fmt.Errorf("failed creating user in repository: %w", err)
		return
	}

	r = command.UserCreateCommandResult{
		User: u,
	}

	return
}

// EditUser implements interfaces.UserService.
func (us *UserService) EditUser(
	q command.UserEditCommand,
) (r command.UserEditCommandResult, err error) {
	u, err := us.userRepository.UpdateUser(q.User)
	if err != nil {
		err = fmt.Errorf("failed editing user in repository: %w", err)
		return
	}

	r = command.UserEditCommandResult{
		User: u,
	}

	return
}

// DeleteUser implements interfaces.UserService.
func (us *UserService) DeleteUser(
	q command.UserDeleteCommand,
) (r command.UserDeleteCommandResult, err error) {
	err = us.userRepository.DeleteUser(q.UserID)
	if err != nil {
		err = fmt.Errorf("failed deleting user in repository: %w", err)
		return
	}

	r = command.UserDeleteCommandResult{}

	return
}
