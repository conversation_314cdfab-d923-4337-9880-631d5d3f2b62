package services_test

import (
	"errors"
	"net/netip"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/application/services"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	mock_repository "git.moderntv.eu/multicast-probe/internal/management/domain/repository/mocks"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	relay_mocks "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client/mocks"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

// TestScanService_GetScan tests the GetScan method of the ScanService.
func TestScanService_GetScan(t *testing.T) {
	t.<PERSON>()

	t.Run("Success", func(t *testing.T) {
		t.<PERSON>()

		service, mockScanRepo, _, _ := setupScanServiceTest(t)

		// Create test data
		scanID := uuid.New()
		scan := entity.Scan{
			ID:        scanID,
			CreatedAt: value.NewUNIXTime(time.Now()),
			Status:    entity.ScanStatusCompleted,
		}

		// Setup expectations
		mockScanRepo.EXPECT().
			GetScan(scanID.String()).
			Return(scan, nil)

		// Call the method
		result, err := service.GetScan(query.ScanQuery{ID: scanID.String()})

		// Assert results
		require.NoError(t, err)
		assert.Equal(t, scan, result.Scan)
	})

	t.Run("RepositoryError", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, _, _ := setupScanServiceTest(t)

		// Create test data
		scanID := uuid.New()
		expectedErr := errors.New("repository error")

		// Setup expectations
		mockScanRepo.EXPECT().
			GetScan(scanID.String()).
			Return(entity.Scan{}, expectedErr)

		// Call the method
		result, err := service.GetScan(query.ScanQuery{ID: scanID.String()})

		// Assert results
		require.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Empty(t, result.Scan)
	})
}

// TestScanService_GetScans tests the GetScans method of the ScanService.
func TestScanService_GetScans(t *testing.T) {
	t.Parallel()

	t.Run("Success", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, _, _ := setupScanServiceTest(t)

		// Create test data
		scans := []entity.Scan{
			{
				ID:        uuid.New(),
				CreatedAt: value.NewUNIXTime(time.Now()),
				Status:    entity.ScanStatusCompleted,
			},
			{
				ID:        uuid.New(),
				CreatedAt: value.NewUNIXTime(time.Now()),
				Status:    entity.ScanStatusProcessing,
			},
		}

		// Setup expectations
		mockScanRepo.EXPECT().
			GetScans().
			Return(scans, nil)

		// Call the method
		result, err := service.GetScans()

		// Assert results
		require.NoError(t, err)
		assert.Equal(t, scans, result.Scans)
	})

	t.Run("RepositoryError", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, _, _ := setupScanServiceTest(t)

		// Create test data
		expectedErr := errors.New("repository error")

		// Setup expectations
		mockScanRepo.EXPECT().
			GetScans().
			Return(nil, expectedErr)

		// Call the method
		result, err := service.GetScans()

		// Assert results
		require.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Empty(t, result.Scans)
	})
}

// TestScanService_StartScan tests the StartScan method of the ScanService.
func TestScanService_StartScan(t *testing.T) {
	t.Parallel()

	t.Run("Success", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, mockRelayClient, _ := setupScanServiceTest(t)

		// Create test data
		ipAddresses := []netip.Addr{
			netip.MustParseAddr("*********"),
			netip.MustParseAddr("*********"),
		}
		port := uint16(1234)
		scanCommand := command.ScanCommand{
			IPAddresses: ipAddresses,
			Parameters: value.ScanParameters{
				MulticastCIDR: "*********/30",
				Port:          port,
			},
		}

		// Setup expectations
		mockScanRepo.EXPECT().
			StoreScan(gomock.Any()).
			Return(nil).AnyTimes()

		// For each IP address, expect a Scan call
		for _, ip := range ipAddresses {
			mockRelayClient.EXPECT().
				Scan(gomock.Any(), value.MustParseAddress(ip.String(), port)).
				Return(nil).AnyTimes()

			// Expect a StreamInfo call that returns a valid stream
			mockRelayClient.EXPECT().
				StreamInfo(gomock.Any(), value.MustParseAddress(ip.String(), port)).
				Return(
					entity.Stream{
						Address: value.MustParseAddress(ip.String(), port),
						Programs: []entity.Program{
							{
								ProgramID: 1,
								Title:     "Test Program",
							},
						},
					}, nil,
				).AnyTimes()
		}

		// Call the method
		result, err := service.StartScan(scanCommand)

		// Assert results
		require.NoError(t, err)
		assert.NotEmpty(t, result.ScanID)

		// Wait for goroutines to complete
		time.Sleep(100 * time.Millisecond)
	})

	t.Run("StoreScanError", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, _, _ := setupScanServiceTest(t)

		// Create test data
		scanCommand := command.ScanCommand{
			IPAddresses: []netip.Addr{
				netip.MustParseAddr("*********"),
			},
			Parameters: value.ScanParameters{
				MulticastCIDR: "*********/32",
				Port:          1234,
			},
		}
		expectedErr := errors.New("store scan error")

		// Setup expectations
		mockScanRepo.EXPECT().
			StoreScan(gomock.Any()).
			Return(expectedErr)

		// Call the method
		result, err := service.StartScan(scanCommand)

		// Assert results
		require.Error(t, err)
		assert.Contains(t, err.Error(), "failed to store initial scan")
		assert.Empty(t, result.ScanID)
	})

	t.Run("ScanError", func(t *testing.T) {
		t.Parallel()

		service, mockScanRepo, mockRelayClient, _ := setupScanServiceTest(t)

		// Create test data
		ipAddresses := []netip.Addr{
			netip.MustParseAddr("*********"),
		}
		port := uint16(1234)
		scanCommand := command.ScanCommand{
			IPAddresses: ipAddresses,
			Parameters: value.ScanParameters{
				MulticastCIDR: "*********/32",
				Port:          port,
			},
		}
		scanError := errors.New("scan error")

		// Setup expectations
		mockScanRepo.EXPECT().
			StoreScan(gomock.Any()).
			Return(nil).AnyTimes()

		// Expect a Scan call that returns an error
		mockRelayClient.EXPECT().
			Scan(gomock.Any(), value.MustParseAddress(ipAddresses[0].String(), port)).
			Return(scanError).AnyTimes()

		// Call the method
		result, err := service.StartScan(scanCommand)

		// Assert results
		require.NoError(t, err)
		assert.NotEmpty(t, result.ScanID)

		// Wait for goroutines to complete
		time.Sleep(100 * time.Millisecond)
	})
}

func setupScanServiceTest(t *testing.T) (
	*services.ScanService,
	*mock_repository.MockScanRepository,
	*relay_mocks.MockClient,
	*mock_repository.MockConfigurationRepository, //nolint: unparam
) {
	t.Helper()

	ctrl := gomock.NewController(t)
	mockScanRepo := mock_repository.NewMockScanRepository(ctrl)
	mockRelayClient := relay_mocks.NewMockClient(ctrl)
	logger := zerolog.Nop()
	mockConfigRepository := mock_repository.NewMockConfigurationRepository(ctrl)

	entityConf := entity.Configuration{
		ScanDelay:           10 * time.Millisecond,
		ScanInterval:        10 * time.Millisecond,
		ScanMaxTime:         60 * time.Millisecond,
		ScansPerSecond:      2,
		ScanChannelSize:     200000,
		ScanRetention:       7 * 24 * time.Millisecond, // 1 week
		ScanCleanupInterval: 100 * time.Second,
	}

	mockConfigRepository.EXPECT().
		GetConfiguration().
		Return(entityConf, nil).
		AnyTimes()

	service, err := services.NewScanService(
		logger,
		mockConfigRepository,
		mockRelayClient,
		mockScanRepo,
	)
	if err != nil {
		panic(err)
	}
	return service, mockScanRepo, mockRelayClient, mockConfigRepository
}
