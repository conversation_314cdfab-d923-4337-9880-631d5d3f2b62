package services

import (
	"context"
	"fmt"
	"net/netip"
	"sync"
	"time"

	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/dto"
	"git.moderntv.eu/multicast-probe/internal/management/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/repository"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
	relay "git.moderntv.eu/multicast-probe/internal/management/infrastructure/clients/relay_client"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

var _ interfaces.ScanService = &ScanService{}

type ScanService struct {
	log              zerolog.Logger
	configRepository repository.ConfigurationRepository
	relay            relay.Client
	scanRepository   repository.ScanRepository
	scanCh           chan dto.ScanRequest
	CancelledScans   map[string]struct{}
	mu               sync.RWMutex
}

func NewScanService(
	log zerolog.Logger,
	configRepository repository.ConfigurationRepository,
	relay relay.Client,
	scanRepository repository.ScanRepository,
) (ss *ScanService, err error) {
	cfg, err := configRepository.GetConfiguration()
	if err != nil {
		log.Fatal().Err(err).Msg("failed to get configuration")
	}

	scanCh := make(chan dto.ScanRequest, cfg.ScanChannelSize)

	ss = &ScanService{
		log:              log.With().Str("component", "management/scan_service").Logger(),
		configRepository: configRepository,
		relay:            relay,
		scanRepository:   scanRepository,
		scanCh:           scanCh,
		CancelledScans:   make(map[string]struct{}),
	}

	go ss.limitScans()
	go ss.periodicScanCleanup()

	return ss, nil
}

// GetScan implements interfaces.ScanService.
func (ss *ScanService) GetScan(q query.ScanQuery) (r query.ScanQueryResult, err error) {
	scan, err := ss.scanRepository.GetScan(q.ID)
	if err != nil {
		return query.ScanQueryResult{}, err
	}

	return query.ScanQueryResult{
		Scan: scan,
	}, nil
}

// GetScans implements interfaces.ScanService.
func (ss *ScanService) GetScans() (r query.ScansQueryResult, err error) {
	scans, err := ss.scanRepository.GetScans()
	if err != nil {
		return query.ScansQueryResult{}, err
	}

	return query.ScansQueryResult{
		Scans: scans,
	}, nil
}

// StartScan implements interfaces.ScanService.
func (ss *ScanService) StartScan(
	q command.ScanCommand,
) (r command.ScanCommandResult, err error) {
	scanID := uuid.New()

	resultScan := entity.Scan{
		ID:         scanID,
		Parameters: q.Parameters,
		CreatedAt:  value.NewUNIXTime(time.Now()),
		Status:     entity.ScanStatusQueued,
	}

	// Store initial scan
	err = ss.scanRepository.StoreScan(resultScan)
	if err != nil {
		ss.log.Warn().
			Err(err).
			Interface("command", q).
			Interface("scan", resultScan).
			Msg("failed to store initial scan")

		return command.ScanCommandResult{}, fmt.Errorf("failed to store initial scan: %w", err)
	}

	// Create buffered channel to handle concurrent stream updates
	streamCh := make(chan entity.Stream, len(q.IPAddresses))

	// Start goroutine to collect streams and update scan
	go ss.writeResults(streamCh, resultScan, q.IPAddresses)

	ss.log.Debug().
		Interface("command", q).
		Msg("starting scan")

	for _, ipAddress := range q.IPAddresses {
		ss.scanCh <- dto.ScanRequest{
			Address:  value.ParseAddressPortFrom(ipAddress, q.Parameters.Port),
			StreamCh: streamCh,
			ScanID:   scanID,
		}
	}

	return command.ScanCommandResult{
		ScanID: scanID.String(),
	}, nil
}

func (ss *ScanService) CancelScan(
	cmd command.ScanCancelCommand,
) (c command.ScanCancelCommandResult, err error) {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	scan, err := ss.scanRepository.GetScan(cmd.ScanID)
	if err != nil {
		return c, fmt.Errorf("failed to get scan: %w", err)
	}
	ss.CancelledScans[cmd.ScanID] = struct{}{}
	scan.Status = entity.ScanStatusCancelled
	err = ss.scanRepository.StoreScan(scan)
	c.ScanID = scan.ID.String()
	if err != nil {
		return c, fmt.Errorf("failed to update scan status: %w", err)
	}

	return c, nil
}

func (ss *ScanService) limitScans() {
	cfg, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Fatal().Err(err).Msg("failed to get configuration")
	}

	limiter := time.NewTicker(
		time.Second / time.Duration(cfg.ScansPerSecond),
	)
	defer limiter.Stop()

	for {
		scan := <-ss.scanCh
		ss.mu.RLock()
		_, deleted := ss.CancelledScans[scan.ScanID.String()]
		ss.mu.RUnlock()
		if deleted {
			continue
		}

		<-limiter.C
		go ss.scanIPAddress(scan.Address, scan.StreamCh)
	}
}

func (ss *ScanService) scanIPAddress(
	address value.Address,
	streamCh chan entity.Stream,
) {
	err := ss.relay.Scan(context.Background(), address)
	if err != nil {
		ss.log.Debug().
			Err(err).
			Interface("address", address).
			Msg("failed to scan")

		streamCh <- entity.Stream{
			Address:   address,
			ScanError: err.Error(),
		}
		return
	}

	stream := entity.Stream{}
	startTime := time.Now()
	cfg, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Fatal().Err(err).Msg("failed to get configuration")
	}
	for time.Since(startTime) < cfg.ScanMaxTime {
		time.Sleep(cfg.ScanInterval)

		stream, err = ss.relay.StreamInfo(context.Background(), address)
		if err != nil {
			ss.log.Trace().
				Err(err).
				Interface("address", address).
				Msg("failed to get stream info")
			continue
		}
		if stream.HasValidStreamInfo() { // stream info is valid
			ss.log.Trace().
				Interface("address", address).
				Interface("stream_info", stream).
				Msg("stream info is valid")
		}
	}
	streamCh <- stream
}

func (ss *ScanService) writeResults(
	streamCh chan entity.Stream,
	resultScan entity.Scan,
	ipAddresses []netip.Addr,
) {
	resultScan.Streams = make([]entity.Stream, 0, len(ipAddresses))
	resultScan.Status = entity.ScanStatusProcessing

	for stream := range streamCh {
		resultScan.Streams = append(resultScan.Streams, stream)

		if err := ss.scanRepository.StoreScan(resultScan); err != nil {
			ss.log.Warn().
				Err(err).
				Interface("scan", resultScan).
				Msg("failed to store scan update")
		}
		if len(resultScan.Streams) == len(ipAddresses) {
			resultScan.Status = entity.ScanStatusCompleted

			for _, stream := range resultScan.Streams {
				if stream.ScanError != "" {
					resultScan.Status = entity.ScanStatusFailed
					break
				}
			}

			if err := ss.scanRepository.StoreScan(resultScan); err != nil {
				ss.log.Warn().
					Err(err).
					Interface("scan", resultScan).
					Msg("failed to store scan update")
			}

			close(streamCh)
			return
		}
	}
}

func (ss *ScanService) periodicScanCleanup() {
	cfg, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Fatal().Err(err).Msg("failed to get configuration")
	}
	ticker := time.NewTicker(cfg.ScanCleanupInterval)
	defer ticker.Stop()
	for range ticker.C {
		ss.cleanupOldScans()
	}
}

func (ss *ScanService) cleanupOldScans() {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	scans, err := ss.scanRepository.GetScans()
	if err != nil {
		ss.log.Warn().Err(err).Msg("failed to list scans for cleanup")
		return
	}
	cfg, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Fatal().Err(err).Msg("failed to get configuration")
		return
	}
	now := time.Now()
	for _, scan := range scans {
		if now.Sub(scan.CreatedAt.Time) > cfg.ScanRetention {
			_ = ss.scanRepository.DeleteScan(scan.ID.String())
			delete(ss.CancelledScans, scan.ID.String())
		}
	}
}
