package dto

import (
	"time"

	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
)

// ConfigurationRequest represents configuration with duration fields in milliseconds for API requests.
type Configuration struct {
	ScanDelay           int64 `json:"scanDelay,omitzero"`    // milliseconds
	ScanInterval        int64 `json:"scanInterval,omitzero"` // milliseconds
	ScanMaxTime         int64 `json:"scanMaxTime,omitzero"`  // milliseconds
	ScansPerSecond      int   `json:"scansPerSecond,omitzero"`
	ScanChannelSize     int   `json:"scanChannelSize,omitzero"`
	ScanRetention       int64 `json:"scanRetention,omitzero"`       // milliseconds
	ScanCleanupInterval int64 `json:"scanCleanupInterval,omitzero"` // milliseconds
}

// ToConfiguration converts ConfigurationRequest to entity.Configuration with duration conversion.
func (cr *Configuration) ToConfigurationEntity() entity.Configuration {
	return entity.Configuration{
		ScanDelay:           time.Duration(cr.ScanDelay) * time.Millisecond,
		ScanInterval:        time.Duration(cr.ScanInterval) * time.Millisecond,
		ScanMaxTime:         time.Duration(cr.ScanMaxTime) * time.Millisecond,
		ScansPerSecond:      cr.ScansPerSecond,
		ScanChannelSize:     cr.ScanChannelSize,
		ScanRetention:       time.Duration(cr.ScanRetention) * time.Millisecond,
		ScanCleanupInterval: time.Duration(cr.ScanCleanupInterval) * time.Millisecond,
	}
}

// ToConfigurationResponse converts entity.Configuration to Configuration with duration conversion.
func ToConfigurationResponse(config entity.Configuration) Configuration {
	return Configuration{
		ScanDelay:           config.ScanDelay.Milliseconds(),
		ScanInterval:        config.ScanInterval.Milliseconds(),
		ScanMaxTime:         config.ScanMaxTime.Milliseconds(),
		ScansPerSecond:      config.ScansPerSecond,
		ScanChannelSize:     config.ScanChannelSize,
		ScanRetention:       config.ScanRetention.Milliseconds(),
		ScanCleanupInterval: config.ScanCleanupInterval.Milliseconds(),
	}
}
