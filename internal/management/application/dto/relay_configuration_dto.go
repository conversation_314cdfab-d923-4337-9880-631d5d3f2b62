package dto

import (
	"time"

	"git.moderntv.eu/multicast-probe/internal/management/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/management/domain/value"
)

// RelayConfiguration DTO represents relay configuration with duration fields in milliseconds for API requests.
type RelayConfiguration struct {
	ScanTimeout             int64                    `json:"scanTimeout,omitzero"`  // milliseconds
	ScanCleanupDelay        int64                    `json:"cleanupDelay,omitzero"` // milliseconds
	DispatcherBufferSize    int                      `json:"dispatcherBufferSize,omitzero"`
	StreamChannelBufferSize int                      `json:"streamChannelBufferSize,omitzero"`
	JoinBufferSize          int                      `json:"joinBufferSize,omitzero"`
	JoinsPerSecond          int                      `json:"joinsPerSecond,omitzero"`
	NetworkInterfaces       []value.NetworkInterface `json:"networkInterfaces,omitempty"`
	UDPPacketSize           int                      `json:"udpPacketSize,omitzero"`
	ConnectionTimeout       int64                    `json:"connectionTimeout,omitzero"` // milliseconds
}

// ToRelayConfiguration converts RelayConfigurationRequest to entity.RelayConfiguration with duration conversion.
func (rcr *RelayConfiguration) ToRelayConfigurationEntity() entity.RelayConfiguration {
	return entity.RelayConfiguration{
		ScanTimeout:             time.Duration(rcr.ScanTimeout) * time.Millisecond,
		ScanCleanupDelay:        time.Duration(rcr.ScanCleanupDelay) * time.Millisecond,
		DispatcherBufferSize:    rcr.DispatcherBufferSize,
		StreamChannelBufferSize: rcr.StreamChannelBufferSize,
		JoinBufferSize:          rcr.JoinBufferSize,
		JoinsPerSecond:          rcr.JoinsPerSecond,
		NetworkInterfaces:       rcr.NetworkInterfaces,
		UDPPacketSize:           rcr.UDPPacketSize,
		ConnectionTimeout:       time.Duration(rcr.ConnectionTimeout) * time.Millisecond,
	}
}

// ToRelayConfigurationResponse converts entity.RelayConfiguration to RelayConfiguration with duration conversion.
func ToRelayConfigurationResponse(config entity.RelayConfiguration) RelayConfiguration {
	return RelayConfiguration{
		ScanTimeout:             config.ScanTimeout.Milliseconds(),
		ScanCleanupDelay:        config.ScanCleanupDelay.Milliseconds(),
		DispatcherBufferSize:    config.DispatcherBufferSize,
		StreamChannelBufferSize: config.StreamChannelBufferSize,
		JoinBufferSize:          config.JoinBufferSize,
		JoinsPerSecond:          config.JoinsPerSecond,
		NetworkInterfaces:       config.NetworkInterfaces,
		UDPPacketSize:           config.UDPPacketSize,
		ConnectionTimeout:       config.ConnectionTimeout.Milliseconds(),
	}
}
