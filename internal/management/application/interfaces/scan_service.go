package interfaces

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
)

//go:generate go tool mockgen -destination mocks/scan_service_mock.go . ScanService

type ScanService interface {
	GetScan(q query.ScanQuery) (r query.ScanQueryResult, err error)
	GetScans() (r query.ScansQueryResult, err error)
	StartScan(q command.ScanCommand) (r command.ScanCommandResult, err error)
	CancelScan(cmd command.ScanCancelCommand) (c command.ScanCancelCommandResult, err error)
}
