// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/application/interfaces (interfaces: StreamService)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_service_mock.go . StreamService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	query "git.moderntv.eu/multicast-probe/internal/management/application/query"
	gomock "go.uber.org/mock/gomock"
)

// MockStreamService is a mock of StreamService interface.
type MockStreamService struct {
	ctrl     *gomock.Controller
	recorder *MockStreamServiceMockRecorder
	isgomock struct{}
}

// MockStreamServiceMockRecorder is the mock recorder for MockStreamService.
type MockStreamServiceMockRecorder struct {
	mock *MockStreamService
}

// NewMockStreamService creates a new mock instance.
func NewMockStreamService(ctrl *gomock.Controller) *MockStreamService {
	mock := &MockStreamService{ctrl: ctrl}
	mock.recorder = &MockStreamServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamService) EXPECT() *MockStreamServiceMockRecorder {
	return m.recorder
}

// GetStream mocks base method.
func (m *MockStreamService) GetStream(q query.StreamQuery) (query.StreamQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStream", q)
	ret0, _ := ret[0].(query.StreamQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStream indicates an expected call of GetStream.
func (mr *MockStreamServiceMockRecorder) GetStream(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStream", reflect.TypeOf((*MockStreamService)(nil).GetStream), q)
}

// GetStreams mocks base method.
func (m *MockStreamService) GetStreams(q query.StreamsQuery) (query.StreamsQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStreams", q)
	ret0, _ := ret[0].(query.StreamsQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStreams indicates an expected call of GetStreams.
func (mr *MockStreamServiceMockRecorder) GetStreams(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStreams", reflect.TypeOf((*MockStreamService)(nil).GetStreams), q)
}
