// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/application/interfaces (interfaces: ScanService)
//
// Generated by this command:
//
//	mockgen -destination mocks/scan_service_mock.go . ScanService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	command "git.moderntv.eu/multicast-probe/internal/management/application/command"
	query "git.moderntv.eu/multicast-probe/internal/management/application/query"
	gomock "go.uber.org/mock/gomock"
)

// MockScanService is a mock of ScanService interface.
type MockScanService struct {
	ctrl     *gomock.Controller
	recorder *MockScanServiceMockRecorder
	isgomock struct{}
}

// MockScanServiceMockRecorder is the mock recorder for MockScanService.
type MockScanServiceMockRecorder struct {
	mock *MockScanService
}

// NewMockScanService creates a new mock instance.
func NewMockScanService(ctrl *gomock.Controller) *MockScanService {
	mock := &MockScanService{ctrl: ctrl}
	mock.recorder = &MockScanServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScanService) EXPECT() *MockScanServiceMockRecorder {
	return m.recorder
}

// CancelScan mocks base method.
func (m *MockScanService) CancelScan(cmd command.ScanCancelCommand) (command.ScanCancelCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelScan", cmd)
	ret0, _ := ret[0].(command.ScanCancelCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelScan indicates an expected call of CancelScan.
func (mr *MockScanServiceMockRecorder) CancelScan(cmd any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelScan", reflect.TypeOf((*MockScanService)(nil).CancelScan), cmd)
}

// GetScan mocks base method.
func (m *MockScanService) GetScan(q query.ScanQuery) (query.ScanQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScan", q)
	ret0, _ := ret[0].(query.ScanQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScan indicates an expected call of GetScan.
func (mr *MockScanServiceMockRecorder) GetScan(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScan", reflect.TypeOf((*MockScanService)(nil).GetScan), q)
}

// GetScans mocks base method.
func (m *MockScanService) GetScans() (query.ScansQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScans")
	ret0, _ := ret[0].(query.ScansQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScans indicates an expected call of GetScans.
func (mr *MockScanServiceMockRecorder) GetScans() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScans", reflect.TypeOf((*MockScanService)(nil).GetScans))
}

// StartScan mocks base method.
func (m *MockScanService) StartScan(q command.ScanCommand) (command.ScanCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartScan", q)
	ret0, _ := ret[0].(command.ScanCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartScan indicates an expected call of StartScan.
func (mr *MockScanServiceMockRecorder) StartScan(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartScan", reflect.TypeOf((*MockScanService)(nil).StartScan), q)
}
