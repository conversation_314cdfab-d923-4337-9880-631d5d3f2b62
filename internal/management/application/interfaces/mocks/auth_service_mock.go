// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/application/interfaces (interfaces: AuthService)
//
// Generated by this command:
//
//	mockgen -destination mocks/auth_service_mock.go . AuthService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	command "git.moderntv.eu/multicast-probe/internal/management/application/command"
	gomock "go.uber.org/mock/gomock"
)

// MockAuthService is a mock of AuthService interface.
type MockAuthService struct {
	ctrl     *gomock.Controller
	recorder *MockAuthServiceMockRecorder
	isgomock struct{}
}

// MockAuthServiceMockRecorder is the mock recorder for MockAuthService.
type MockAuthServiceMockRecorder struct {
	mock *MockAuthService
}

// NewMockAuthService creates a new mock instance.
func NewMockAuthService(ctrl *gomock.Controller) *MockAuthService {
	mock := &MockAuthService{ctrl: ctrl}
	mock.recorder = &MockAuthServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthService) EXPECT() *MockAuthServiceMockRecorder {
	return m.recorder
}

// Login mocks base method.
func (m *MockAuthService) Login(c command.AuthLoginCommand) (command.AuthLoginCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Login", c)
	ret0, _ := ret[0].(command.AuthLoginCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockAuthServiceMockRecorder) Login(c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockAuthService)(nil).Login), c)
}

// RefreshToken mocks base method.
func (m *MockAuthService) RefreshToken(c command.AuthRefreshTokenCommand) (command.AuthRefreshTokenCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", c)
	ret0, _ := ret[0].(command.AuthRefreshTokenCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockAuthServiceMockRecorder) RefreshToken(c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockAuthService)(nil).RefreshToken), c)
}

// ValidateToken mocks base method.
func (m *MockAuthService) ValidateToken(c command.AuthValidateTokenCommand) (command.AuthValidateTokenCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateToken", c)
	ret0, _ := ret[0].(command.AuthValidateTokenCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateToken indicates an expected call of ValidateToken.
func (mr *MockAuthServiceMockRecorder) ValidateToken(c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateToken", reflect.TypeOf((*MockAuthService)(nil).ValidateToken), c)
}
