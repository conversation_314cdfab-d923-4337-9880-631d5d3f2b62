// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/management/application/interfaces (interfaces: UserService)
//
// Generated by this command:
//
//	mockgen -destination mocks/user_service_mock.go . UserService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	command "git.moderntv.eu/multicast-probe/internal/management/application/command"
	query "git.moderntv.eu/multicast-probe/internal/management/application/query"
	gomock "go.uber.org/mock/gomock"
)

// MockUserService is a mock of UserService interface.
type MockUserService struct {
	ctrl     *gomock.Controller
	recorder *MockUserServiceMockRecorder
	isgomock struct{}
}

// MockUserServiceMockRecorder is the mock recorder for MockUserService.
type MockUserServiceMockRecorder struct {
	mock *MockUserService
}

// NewMockUserService creates a new mock instance.
func NewMockUserService(ctrl *gomock.Controller) *MockUserService {
	mock := &MockUserService{ctrl: ctrl}
	mock.recorder = &MockUserServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserService) EXPECT() *MockUserServiceMockRecorder {
	return m.recorder
}

// CreateUser mocks base method.
func (m *MockUserService) CreateUser(q command.UserCreateCommand) (command.UserCreateCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", q)
	ret0, _ := ret[0].(command.UserCreateCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockUserServiceMockRecorder) CreateUser(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockUserService)(nil).CreateUser), q)
}

// DeleteUser mocks base method.
func (m *MockUserService) DeleteUser(q command.UserDeleteCommand) (command.UserDeleteCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUser", q)
	ret0, _ := ret[0].(command.UserDeleteCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUser indicates an expected call of DeleteUser.
func (mr *MockUserServiceMockRecorder) DeleteUser(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUser", reflect.TypeOf((*MockUserService)(nil).DeleteUser), q)
}

// EditUser mocks base method.
func (m *MockUserService) EditUser(q command.UserEditCommand) (command.UserEditCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditUser", q)
	ret0, _ := ret[0].(command.UserEditCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditUser indicates an expected call of EditUser.
func (mr *MockUserServiceMockRecorder) EditUser(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditUser", reflect.TypeOf((*MockUserService)(nil).EditUser), q)
}

// GetUser mocks base method.
func (m *MockUserService) GetUser(q query.UserQuery) (query.UserQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", q)
	ret0, _ := ret[0].(query.UserQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockUserServiceMockRecorder) GetUser(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockUserService)(nil).GetUser), q)
}

// GetUsers mocks base method.
func (m *MockUserService) GetUsers(q query.UsersQuery) (query.UsersQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", q)
	ret0, _ := ret[0].(query.UsersQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockUserServiceMockRecorder) GetUsers(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockUserService)(nil).GetUsers), q)
}
