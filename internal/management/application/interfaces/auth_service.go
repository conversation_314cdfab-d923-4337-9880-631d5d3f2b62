package interfaces

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
)

//go:generate go tool mockgen -destination mocks/auth_service_mock.go . AuthService

type AuthService interface {
	Login(c command.AuthLoginCommand) (r command.AuthLoginCommandResult, err error)
	RefreshToken(
		c command.AuthRefreshTokenCommand,
	) (r command.AuthRefreshTokenCommandResult, err error)
	ValidateToken(
		c command.AuthValidateTokenCommand,
	) (r command.AuthValidateTokenCommandResult, err error)
}
