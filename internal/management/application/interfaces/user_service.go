package interfaces

import (
	"git.moderntv.eu/multicast-probe/internal/management/application/command"
	"git.moderntv.eu/multicast-probe/internal/management/application/query"
)

//go:generate go tool mockgen -destination mocks/user_service_mock.go . UserService

type UserService interface {
	GetUsers(q query.UsersQuery) (r query.UsersQueryResult, err error)
	GetUser(q query.UserQuery) (r query.UserQueryResult, err error)
	CreateUser(q command.UserCreateCommand) (r command.UserCreateCommandResult, err error)
	EditUser(q command.UserEditCommand) (r command.UserEditCommandResult, err error)
	DeleteUser(q command.UserDeleteCommand) (r command.UserDeleteCommandResult, err error)
}
