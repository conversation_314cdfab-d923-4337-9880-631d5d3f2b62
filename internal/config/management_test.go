package config_test

import (
	"os"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewManagementConfig_DefaultValues(t *testing.T) {
	t.Parallel()

	c, err := config.NewManagementConfig()
	require.NoError(t, err)

	// Test existing defaults
	assert.Equal(t, "/var/lib/badgerdb/probe/data", c.Database.Path)
	assert.Equal(t, 24*time.Hour, c.Auth.JWTExpiration)

	// Test new GC defaults
	assert.Equal(t, 24*time.Hour, c.Database.GCInterval)
	assert.InEpsilon(t, 0.7, c.Database.GCDiscardRatio, 0.001)
}

func TestNewManagementConfig_EmptyPath(t *testing.T) {
	t.Parallel()

	// Create a temporary config file that sets empty path
	tmpFile := createTempConfigFile(t, `
database:
  path: ""
`)
	defer cleanupTempFile(t, tmpFile)

	_, err := config.NewManagementConfig(tmpFile)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "no path configured for database")
}

func TestNewManagementConfig_GCConfiguration(t *testing.T) {
	t.Parallel()

	// Create a temporary config file with GC settings
	tmpFile := createTempConfigFile(t, `
database:
  path: "/test/path"
  gc_interval: "1h"
  gc_discard_ratio: 0.8
`)
	defer cleanupTempFile(t, tmpFile)

	c, err := config.NewManagementConfig(tmpFile)
	require.NoError(t, err)

	assert.Equal(t, "/test/path", c.Database.Path)
	assert.Equal(t, 1*time.Hour, c.Database.GCInterval)
	assert.InEpsilon(t, 0.8, c.Database.GCDiscardRatio, 0.001)
}

func TestManagementConfig_PostLoad(t *testing.T) {
	t.Parallel()

	c := config.ManagementConfig{}
	err := c.PostLoad()
	assert.NoError(t, err)
}

// Helper functions for creating temporary config files.
func createTempConfigFile(t *testing.T, content string) string {
	t.Helper()
	tmpFile, err := os.CreateTemp(t.TempDir(), "config_test_*.yml")
	require.NoError(t, err)

	_, err = tmpFile.WriteString(content)
	require.NoError(t, err)

	err = tmpFile.Close()
	require.NoError(t, err)

	return tmpFile.Name()
}

func cleanupTempFile(t *testing.T, filename string) {
	t.Helper()
	err := os.Remove(filename)
	require.NoError(t, err)
}
