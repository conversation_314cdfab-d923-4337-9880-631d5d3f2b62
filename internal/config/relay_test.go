package config_test

import (
	"os"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRelayConfig_DefaultValues(t *testing.T) {
	t.<PERSON>llel()

	c, err := config.NewRelayConfig()
	require.NoError(t, err)

	// Test existing defaults
	assert.Equal(t, "data/relay/badgerdb", c.Database.Path)
	assert.Equal(t, ":8008", c.ListenAddressHTTP)
	assert.Equal(t, ":8009", c.ListenAddressPrometheus)
	assert.Equal(t, ":8010", c.ListenAddressGRPC)

	// Test new GC defaults
	assert.Equal(t, 24*time.Hour, c.Database.GCInterval)
	assert.InEpsilon(t, 0.7, c.Database.GCDiscardRatio, 0.001)
}

func TestNewRelayConfig_GCConfiguration(t *testing.T) {
	t.<PERSON>()

	// Create a temporary config file with GC settings
	tmpFile := createTempRelayConfigFile(t, `
database:
  path: "/test/relay/path"
  gc_interval: "2h"
  gc_discard_ratio: 0.9
`)
	defer cleanupTempRelayFile(t, tmpFile)

	c, err := config.NewRelayConfig(tmpFile)
	require.NoError(t, err)

	assert.Equal(t, "/test/relay/path", c.Database.Path)
	assert.Equal(t, 2*time.Hour, c.Database.GCInterval)
	assert.InEpsilon(t, 0.9, c.Database.GCDiscardRatio, 0.001)
}

func TestRelayConfig_PostLoad(t *testing.T) {
	t.Parallel()

	c := config.RelayConfig{}
	err := c.PostLoad()
	assert.NoError(t, err)
}

// Helper functions for creating temporary config files (relay specific).
func createTempRelayConfigFile(t *testing.T, content string) string {
	t.Helper()
	tmpFile, err := os.CreateTemp(t.TempDir(), "relay_config_test_*.yml")
	require.NoError(t, err)

	_, err = tmpFile.WriteString(content)
	require.NoError(t, err)

	err = tmpFile.Close()
	require.NoError(t, err)

	return tmpFile.Name()
}

func cleanupTempRelayFile(t *testing.T, filename string) {
	t.Helper()
	err := os.Remove(filename)
	require.NoError(t, err)
}
