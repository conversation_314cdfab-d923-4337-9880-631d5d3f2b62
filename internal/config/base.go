package config

import (
	"time"
)

// DatabaseConfig contains common database configuration for both management and relay.
type DatabaseConfig struct {
	Path string `mapstructure:"path"`

	// GC Configuration
	GCInterval     time.Duration `mapstructure:"gc_interval"`
	GCDiscardRatio float64       `mapstructure:"gc_discard_ratio"`
}

type Base struct {
	Loglevel  string `mapstructure:"loglevel"`
	SentryDSN string `mapstructure:"sentry_dsn"`

	ListenAddressHTTP       string `mapstructure:"listen_address_http"`
	ListenAddressPrometheus string `mapstructure:"listen_address_prometheus"`
}

func NewCommonConfig() (c Base) {
	c = Base{}
	return
}

func (c *Base) PostLoad() (err error) {
	return
}
