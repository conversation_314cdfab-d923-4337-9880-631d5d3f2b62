package config

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/spf13/viper"
)

type Manager struct {
	files []string
	viper *viper.Viper

	conf Config
}

func NewManager(configPtr Config, rawFiles ...string) (*Manager, error) {
	files := []string{}
	for _, file := range rawFiles {
		if file != "" {
			files = append(files, file)
		}
	}

	m := &Manager{
		viper: viper.New(),
		files: files,

		conf: configPtr,
	}

	m.viper.SetEnvPrefix("MULTICAST_PROBE")
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	for _, file := range m.files {
		m.viper.SetConfigFile(file)
	}

	return m, nil
}

func (m *Manager) Load() (err error) {
	if len(m.files) > 0 {
		err = m.viper.ReadInConfig()
		if err != nil {
			err = fmt.Erro<PERSON>("cannot read config: %w", err)
			return
		}
	}

	m.bindEnvs(m.conf)

	err = m.viper.Unmarshal(m.conf)
	if err != nil {
		err = fmt.Errorf("config loading failed at unmarshal: %w", err)
		return err
	}

	err = m.conf.PostLoad()
	if err != nil {
		err = fmt.Errorf("config loading failed at post hook: %w", err)
		return
	}

	return nil
}

func (m *Manager) bindEnvs(configStructPtr interface{}, parts ...string) {
	configStructValue := reflect.ValueOf(configStructPtr).Elem()
	configStructType := configStructValue.Type()

	for _, field := range reflect.VisibleFields(configStructType) {
		if !field.IsExported() || field.Anonymous {
			continue
		}

		fieldValue := configStructValue.FieldByName(field.Name)
		fieldType, ok := configStructType.FieldByName(field.Name)
		if !ok {
			continue
		}

		var tag string
		tagRaw := fieldType.Tag.Get("mapstructure")
		tagParts := strings.Split(tagRaw, ",")
		if len(tagParts) == 0 {
			continue
		}
		tag = tagParts[0]
		if tag == "-" {
			continue
		}

		switch fieldValue.Kind() { //nolint:exhaustive
		case reflect.Struct:
			m.bindEnvs(fieldValue.Addr().Interface(), append(parts, tag)...)
		default:
			_ = m.viper.BindEnv(strings.Join(append(parts, tag), "."))
		}
	}
}
