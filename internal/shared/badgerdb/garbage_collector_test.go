package badgerdb_test

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/config"
	sharedBadgerDB "git.moderntv.eu/multicast-probe/internal/shared/badgerdb"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/require"
)

func TestNewGarbageCollector(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NotNil(t, gc)
}

func TestGarbageCollector_StartStop(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test start
	err = gc.Start()
	require.NoError(t, err)

	// Test stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_RunGC(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Test manual GC run
	err = gc.RunGC()
	require.NoError(t, err)
}

func TestGarbageCollector_DoubleStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Start first time
	err = gc.Start()
	require.NoError(t, err)

	// Try to start again - should fail
	err = gc.Start()
	require.Error(t, err)
	require.Contains(t, err.Error(), "already running")

	// Stop
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_StopWithoutStart(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     time.Hour,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Create garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")

	// Stop without starting - should not error
	err = gc.Stop()
	require.NoError(t, err)
}

func TestGarbageCollector_BasicFunctionality(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     50 * time.Millisecond,
		GCDiscardRatio: 0.5,
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database with default settings
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("Creating test data")

	// Create some test data
	for i := range 100 {
		key := []byte(fmt.Sprintf("test_key_%d", i))
		value := []byte(fmt.Sprintf("test_value_%d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, value)
		})
		require.NoError(t, err)
	}

	require.NoError(t, db.Sync())

	t.Log("Testing garbage collection")

	// Create garbage collector and start it
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let the garbage collector run for a while
	time.Sleep(200 * time.Millisecond)

	// Manually trigger GC multiple times
	gcRuns := 0
	for i := range 5 {
		err = gc.RunGC()
		switch {
		case err == nil:
			gcRuns++
			t.Logf("GC run %d successful", gcRuns)
		case errors.Is(err, badger.ErrNoRewrite):
			t.Logf("GC run %d: No rewrite needed", i+1)
		default:
			require.NoError(t, err, "Unexpected GC error")
		}
		time.Sleep(10 * time.Millisecond)
	}

	t.Logf("Completed %d successful GC runs", gcRuns)

	// Verify database is still functional
	count := 0
	err = db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			count++
		}
		return nil
	})
	require.NoError(t, err)
	require.Equal(t, 100, count, "All data should be accessible")

	t.Log("✅ Basic garbage collection test completed successfully")
}

// TestGarbageCollector_RecommendedApproach demonstrates the recommended approach for testing GC.
// This test focuses on verifying that GC runs without errors rather than forcing value log usage.
func TestGarbageCollector_RecommendedApproach(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_recommended_gc.db")

	// Create realistic configuration (similar to production)
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     100 * time.Millisecond,
		GCDiscardRatio: 0.5, // 50% threshold (realistic)
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).Level(zerolog.InfoLevel).With().Timestamp().Logger()

	// Use default BadgerDB settings (realistic for production)
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	t.Log("=== Testing GC with realistic data patterns ===")

	// Create realistic data (similar to scan results)
	const numScans = 100
	for i := range numScans {
		scanData := createRealisticScanData(i)
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, scanData)
		})
		require.NoError(t, err)
	}

	// Update some data (realistic pattern)
	for i := range numScans / 2 {
		scanData := createRealisticScanData(i + 1000) // Different data
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Set(key, scanData)
		})
		require.NoError(t, err)
	}

	// Delete some old data (realistic cleanup)
	for i := range numScans / 4 {
		key := []byte(fmt.Sprintf("scan_%06d", i))

		err := db.Update(func(txn *badger.Txn) error {
			return txn.Delete(key)
		})
		require.NoError(t, err)
	}

	require.NoError(t, db.Sync())

	t.Log("=== Testing garbage collector functionality ===")

	// Create and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "recommended_test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Let automatic GC run
	time.Sleep(300 * time.Millisecond)

	// Test manual GC runs
	gcAttempts := 0
	gcSuccesses := 0
	gcNoRewrites := 0

	for i := range 10 {
		gcAttempts++
		err = gc.RunGC()

		switch {
		case err == nil:
			gcSuccesses++
			t.Logf("GC attempt %d: SUCCESS", i+1)
		case errors.Is(err, badger.ErrNoRewrite):
			gcNoRewrites++
			t.Logf("GC attempt %d: No rewrite needed (normal)", i+1)
		default:
			t.Errorf("GC attempt %d: Unexpected error: %v", i+1, err)
		}

		time.Sleep(20 * time.Millisecond)
	}

	t.Logf("=== Recommended Test Results ===")
	t.Logf("GC attempts: %d", gcAttempts)
	t.Logf("GC successes: %d", gcSuccesses)
	t.Logf("GC no-rewrites: %d", gcNoRewrites)

	// Verify database integrity
	remainingCount := 0
	err = db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		it := txn.NewIterator(opts)
		defer it.Close()

		for it.Rewind(); it.Valid(); it.Next() {
			remainingCount++

			// Verify we can read the data
			item := it.Item()
			err := item.Value(func(_ []byte) error {
				// Just verify we can read it
				return nil
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", string(item.Key()), err)
			}
		}
		return nil
	})
	require.NoError(t, err)

	expectedRemaining := numScans - numScans/4 // Total minus deleted
	t.Logf("Remaining entries: %d (expected: %d)", remainingCount, expectedRemaining)
	require.Equal(t, expectedRemaining, remainingCount)

	// Success criteria:
	// 1. GC runs without fatal errors
	// 2. Database integrity is maintained
	// 3. Data can be read after GC operations

	require.Positive(t, gcAttempts, "Should have attempted GC")
	require.Equal(t, 0, gcAttempts-(gcSuccesses+gcNoRewrites), "All GC attempts should succeed or return no-rewrite")

	t.Log("✅ Recommended GC test completed successfully")
	t.Log("📝 Key insight: GC success is measured by error-free operation, not space reclamation")
	t.Log("📝 BadgerDB GC is designed to run safely even when no cleanup is needed")
}

// createRealisticScanData creates data similar to actual scan results.
func createRealisticScanData(scanID int) []byte {
	// Simulate realistic scan data structure
	data := map[string]interface{}{
		"scan_id":        scanID,
		"timestamp":      time.Now().Unix(),
		"status":         "completed",
		"multicast_cidr": "*********/24",
		"port":           1234,
		"streams": []map[string]interface{}{
			{
				"address": "*********:1234",
				"clients": 5,
				"metrics": map[string]interface{}{
					"received_ts_packets": scanID * 1000,
					"received_bytes":      scanID * 10000,
					"present_duration":    scanID * 60,
					"receiving_duration":  scanID * 50,
				},
			},
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		// Return empty JSON object on error
		return []byte("{}")
	}
	return jsonData
}

// Helper function to verify data integrity.
func verifyDataIntegrity(db *badger.DB) error {
	return db.View(func(txn *badger.Txn) error {
		opts := badger.DefaultIteratorOptions
		opts.PrefetchSize = 10
		it := txn.NewIterator(opts)
		defer it.Close()

		count := 0
		for it.Rewind(); it.Valid(); it.Next() {
			item := it.Item()
			key := item.Key()

			err := item.Value(func(val []byte) error {
				// Just verify we can read the value
				var data map[string]interface{}
				return json.Unmarshal(val, &data)
			})
			if err != nil {
				return fmt.Errorf("failed to read value for key %s: %w", string(key), err)
			}
			count++
		}

		// We should have some remaining data after deletions
		if count == 0 {
			return errors.New("no data found in database")
		}

		return nil
	})
}

func TestGarbageCollectorConcurrency(t *testing.T) {
	t.Parallel()

	tempDir := t.TempDir()
	dbPath := filepath.Join(tempDir, "test_concurrency_gc.db")

	// Create test configuration
	dbConfig := config.DatabaseConfig{
		Path:           dbPath,
		GCInterval:     30 * time.Millisecond, // Very short interval for testing
		GCDiscardRatio: 0.1,                   // Low threshold for testing
	}

	// Create logger
	logger := zerolog.New(zerolog.NewConsoleWriter()).With().Timestamp().Logger()

	// Open database
	dbOptions := badger.DefaultOptions(dbPath)
	db, err := badger.Open(dbOptions)
	require.NoError(t, err)
	defer db.Close()

	// Initialize and start garbage collector
	gc := sharedBadgerDB.NewGarbageCollector(db, logger, dbConfig, "test")
	require.NoError(t, gc.Start())
	defer func() {
		require.NoError(t, gc.Stop())
	}()

	// Run concurrent operations: write data while GC is running
	ctx, cancel := context.WithTimeout(t.Context(), 300*time.Millisecond)
	defer cancel()

	const numWorkers = 3
	errors := make(chan error, numWorkers)

	// Start concurrent workers that write data
	for i := range numWorkers {
		go func(workerID int) {
			err := writeDataConcurrently(ctx, db, workerID)
			errors <- err
		}(i)
	}

	// Wait for all workers to complete
	for range numWorkers {
		err := <-errors
		require.NoError(t, err, "Worker should complete without errors")
	}

	// Verify database integrity after concurrent operations
	err = verifyDataIntegrity(db)
	require.NoError(t, err)
}

// Helper function to write data concurrently.
func writeDataConcurrently(ctx context.Context, db *badger.DB, workerID int) error {
	counter := 0

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			err := db.Update(func(txn *badger.Txn) error {
				key := []byte(fmt.Sprintf("worker_%d_key_%d", workerID, counter))
				value := map[string]interface{}{
					"worker_id": workerID,
					"counter":   counter,
					"timestamp": time.Now().Unix(),
				}

				valueBytes, err := json.Marshal(value)
				if err != nil {
					return err
				}

				return txn.Set(key, valueBytes)
			})
			if err != nil {
				return err
			}
			counter++
			time.Sleep(10 * time.Millisecond) // Small delay to avoid overwhelming the DB
		}
	}
}
