// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/application/interfaces (interfaces: StreamService)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_service_mock.go . StreamService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	command "git.moderntv.eu/multicast-probe/internal/relay/application/command"
	query "git.moderntv.eu/multicast-probe/internal/relay/application/query"
	gomock "go.uber.org/mock/gomock"
)

// MockStreamService is a mock of StreamService interface.
type MockStreamService struct {
	ctrl     *gomock.Controller
	recorder *MockStreamServiceMockRecorder
	isgomock struct{}
}

// MockStreamServiceMockRecorder is the mock recorder for MockStreamService.
type MockStreamServiceMockRecorder struct {
	mock *MockStreamService
}

// NewMockStreamService creates a new mock instance.
func NewMockStreamService(ctrl *gomock.Controller) *MockStreamService {
	mock := &MockStreamService{ctrl: ctrl}
	mock.recorder = &MockStreamServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamService) EXPECT() *MockStreamServiceMockRecorder {
	return m.recorder
}

// Scan mocks base method.
func (m *MockStreamService) Scan(c command.ScanCommand) (command.ScanCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scan", c)
	ret0, _ := ret[0].(command.ScanCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scan indicates an expected call of Scan.
func (mr *MockStreamServiceMockRecorder) Scan(c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockStreamService)(nil).Scan), c)
}

// Stream mocks base method.
func (m *MockStreamService) Stream(q query.StreamUDPQuery) (query.StreamUDPQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stream", q)
	ret0, _ := ret[0].(query.StreamUDPQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Stream indicates an expected call of Stream.
func (mr *MockStreamServiceMockRecorder) Stream(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stream", reflect.TypeOf((*MockStreamService)(nil).Stream), q)
}

// StreamInfo mocks base method.
func (m *MockStreamService) StreamInfo(q query.StreamInfoQuery) (query.StreamInfoQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamInfo", q)
	ret0, _ := ret[0].(query.StreamInfoQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamInfo indicates an expected call of StreamInfo.
func (mr *MockStreamServiceMockRecorder) StreamInfo(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamInfo", reflect.TypeOf((*MockStreamService)(nil).StreamInfo), q)
}

// StreamsInfo mocks base method.
func (m *MockStreamService) StreamsInfo() (query.StreamsInfoQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamsInfo")
	ret0, _ := ret[0].(query.StreamsInfoQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamsInfo indicates an expected call of StreamsInfo.
func (mr *MockStreamServiceMockRecorder) StreamsInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamsInfo", reflect.TypeOf((*MockStreamService)(nil).StreamsInfo))
}

// TeardownStream mocks base method.
func (m *MockStreamService) TeardownStream(c command.StreamTeardownCommand) (command.StreamTeardownCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TeardownStream", c)
	ret0, _ := ret[0].(command.StreamTeardownCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TeardownStream indicates an expected call of TeardownStream.
func (mr *MockStreamServiceMockRecorder) TeardownStream(c any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TeardownStream", reflect.TypeOf((*MockStreamService)(nil).TeardownStream), c)
}
