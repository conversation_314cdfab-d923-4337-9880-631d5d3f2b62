// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/application/interfaces (interfaces: ConfigurationService)
//
// Generated by this command:
//
//	mockgen -destination mocks/configuration_service_mock.go . ConfigurationService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	command "git.moderntv.eu/multicast-probe/internal/relay/application/command"
	query "git.moderntv.eu/multicast-probe/internal/relay/application/query"
	gomock "go.uber.org/mock/gomock"
)

// MockConfigurationService is a mock of ConfigurationService interface.
type MockConfigurationService struct {
	ctrl     *gomock.Controller
	recorder *MockConfigurationServiceMockRecorder
	isgomock struct{}
}

// MockConfigurationServiceMockRecorder is the mock recorder for MockConfigurationService.
type MockConfigurationServiceMockRecorder struct {
	mock *MockConfigurationService
}

// NewMockConfigurationService creates a new mock instance.
func NewMockConfigurationService(ctrl *gomock.Controller) *MockConfigurationService {
	mock := &MockConfigurationService{ctrl: ctrl}
	mock.recorder = &MockConfigurationServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigurationService) EXPECT() *MockConfigurationServiceMockRecorder {
	return m.recorder
}

// Configuration mocks base method.
func (m *MockConfigurationService) Configuration(q query.ConfigurationQuery) (query.ConfigurationQueryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Configuration", q)
	ret0, _ := ret[0].(query.ConfigurationQueryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Configuration indicates an expected call of Configuration.
func (mr *MockConfigurationServiceMockRecorder) Configuration(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Configuration", reflect.TypeOf((*MockConfigurationService)(nil).Configuration), q)
}

// SetConfiguration mocks base method.
func (m *MockConfigurationService) SetConfiguration(q command.ConfigurationEditCommand) (command.ConfigurationEditCommandResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetConfiguration", q)
	ret0, _ := ret[0].(command.ConfigurationEditCommandResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetConfiguration indicates an expected call of SetConfiguration.
func (mr *MockConfigurationServiceMockRecorder) SetConfiguration(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetConfiguration", reflect.TypeOf((*MockConfigurationService)(nil).SetConfiguration), q)
}
