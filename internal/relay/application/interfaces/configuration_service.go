package interfaces

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
)

//go:generate go tool mockgen -destination mocks/configuration_service_mock.go . ConfigurationService

type ConfigurationService interface {
	Configuration(q query.ConfigurationQuery) (r query.ConfigurationQueryResult, err error)
	SetConfiguration(
		q command.ConfigurationEditCommand,
	) (r command.ConfigurationEditCommandResult, err error)
}
