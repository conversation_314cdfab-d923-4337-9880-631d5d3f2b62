package interfaces

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
)

//go:generate go tool mockgen -destination mocks/stream_service_mock.go . StreamService

type StreamService interface {
	Scan(c command.ScanCommand) (r command.ScanCommandResult, err error)
	StreamInfo(q query.StreamInfoQuery) (r query.StreamInfoQueryResult, err error)
	StreamsInfo() (r query.StreamsInfoQueryResult, err error)
	Stream(q query.StreamUDPQuery) (r query.StreamUDPQueryResult, err error)
	TeardownStream(
		c command.StreamTeardownCommand,
	) (r command.StreamTeardownCommandResult, err error)
}
