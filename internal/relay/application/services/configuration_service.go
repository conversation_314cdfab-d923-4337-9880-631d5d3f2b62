package services

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/repository"
	"github.com/rs/zerolog"
)

var _ interfaces.ConfigurationService = &ConfigurationService{}

type ConfigurationService struct {
	log              zerolog.Logger
	configRepository repository.ConfigurationRepository
}

func NewConfigurationService(
	log zerolog.Logger,
	configRepository repository.ConfigurationRepository,
) *ConfigurationService {
	return &ConfigurationService{
		log:              log.With().Str("component", "relay/configuration_service").Logger(),
		configRepository: configRepository,
	}
}

// Configuration implements interfaces.ConfigurationService.
func (cs *ConfigurationService) Configuration(
	_ query.ConfigurationQuery,
) (r query.ConfigurationQueryResult, err error) {
	config, err := cs.configRepository.GetConfiguration()
	if err != nil {
		return query.ConfigurationQueryResult{}, err
	}

	r = query.ConfigurationQueryResult{
		Configuration: config,
	}

	return
}

// SetConfiguration implements interfaces.ConfigurationService.
func (cs *ConfigurationService) SetConfiguration(
	c command.ConfigurationEditCommand,
) (r command.ConfigurationEditCommandResult, err error) {
	_, err = cs.configRepository.UpdateConfiguration(c.Configuration)
	if err != nil {
		cs.log.Error().
			Err(err).
			Interface("new_config", c).
			Msg("failed to update relay configuration")
		return command.ConfigurationEditCommandResult{}, err
	}

	cs.log.Info().
		Interface("new_config", c).
		Msg("relay configuration updated")

	return
}
