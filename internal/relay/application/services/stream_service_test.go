package services_test

import (
	"errors"
	"net/netip"
	"sync"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/application/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	mock_dinterfaces "git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces/mocks"
	mock_listeners "git.moderntv.eu/multicast-probe/internal/relay/domain/listeners/mocks"
	mock_repository "git.moderntv.eu/multicast-probe/internal/relay/domain/repository/mocks"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

var validMulticastAddress = value.MustParseAddress("*********", 1234)

func TestStream(t *testing.T) {
	t.Parallel()

	t.Run("ValidAddress_NewStream_Success", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		// Expect factory.Create to be called exactly once for new stream
		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		cmd := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())

		result, err := ss.Stream(cmd)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result.Client.ID, "ClientID should be set")
	})

	t.Run("DifferentAddresses_CreateStreamServiceTwice", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		addr1 := value.MustParseAddress("*********", 1234)
		addr2 := value.MustParseAddress("*********", 1235) // Different port
		addr3 := value.MustParseAddress("*********", 1235) // Different group, same port as addr2

		// Expect factory.Create to be called exactly three times (once for each unique address)
		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(3)

		cmd1 := query.StreamUDPQuery{
			Address:   addr1,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		cmd2 := query.StreamUDPQuery{
			Address:   addr2,
			ProgramID: 43,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		cmd3 := query.StreamUDPQuery{
			Address:   addr3,
			ProgramID: 44,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		// Setup expectations for the first stream
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())

		// First stream request
		result1, err := ss.Stream(cmd1)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result1.Client.ID)

		// Setup expectations for the second stream
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())

		// Second stream request - should create new stream service
		result2, err := ss.Stream(cmd2)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result2.Client.ID)
		assert.NotEqual(t, result1.Client.ID, result2.Client.ID)

		// Setup expectations for the third stream
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())

		// Third stream request - should create new stream service
		result3, err := ss.Stream(cmd3)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result3.Client.ID)
		assert.NotEqual(t, result2.Client.ID, result3.Client.ID)
	})

	t.Run("ValidAddress_ExistingStream_Success", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		// Expect factory.Create to be called exactly once since same address is reused
		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		// Pre-populate with a stream
		cmd := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())
		_, err := ss.Stream(cmd)
		require.NoError(t, err)

		// Second call reuses the stream
		cmd2 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 43,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockStreamService.EXPECT().AddClient(gomock.Any())

		result, err := ss.Stream(cmd2)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result.Client.ID, "ClientID should be set")
	})

	t.Run("SameAddress_CreateStreamServiceOnce", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		// Expect factory.Create to be called exactly once
		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		cmd1 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		cmd2 := query.StreamUDPQuery{
			Address:   validMulticastAddress, // Same address as cmd1
			ProgramID: 43,                    // Different program ID
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		// Setup expectations for the first stream
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())

		// First stream request
		result1, err := ss.Stream(cmd1)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result1.Client.ID)

		// Setup expectations for the second stream
		mockStreamService.EXPECT().AddClient(gomock.Any())

		// Second stream request - should reuse existing stream service
		result2, err := ss.Stream(cmd2)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, result2.Client.ID)
		assert.NotEqual(t, result1.Client.ID, result2.Client.ID)
	})
}

func TestStreamService(t *testing.T) {
	t.Parallel()

	t.Run("ValidAddress_ClientRemoved_NoCleanup_Success", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		// Setup a stream with two clients
		cmd1 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())
		result1, err := ss.Stream(cmd1)
		require.NoError(t, err)

		cmd2 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 43,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}

		mockStreamService.EXPECT().AddClient(gomock.Any())
		_, err = ss.Stream(cmd2)
		require.NoError(t, err)

		// Teardown one client
		teardownCmd := command.StreamTeardownCommand{
			Address: validMulticastAddress,
			Client:  result1.Client,
		}
		mockStreamService.EXPECT().RemoveClient(result1.Client)
		mockStreamService.EXPECT().ClientCount().Return(1) // Still one client left

		result, err := ss.TeardownStream(teardownCmd)
		require.NoError(t, err)
		assert.Equal(t, command.StreamTeardownCommandResult{}, result)
	})

	t.Run("ValidAddress_LastClientRemoved_Cleanup_Success", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		// Setup a stream with one client
		cmd := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())
		result, err := ss.Stream(cmd)
		require.NoError(t, err)

		// Teardown the client
		teardownCmd := command.StreamTeardownCommand{
			Address: validMulticastAddress,
			Client:  result.Client,
		}
		mockStreamService.EXPECT().RemoveClient(result.Client)
		mockStreamService.EXPECT().ClientCount().Return(0).Times(2)
		var wg sync.WaitGroup
		wg.Add(2)
		// Cleanup expectations
		mockStreamService.EXPECT().Close().Do(func() { wg.Done() })
		mockListenerService.EXPECT().
			DetachStream(validMulticastAddress).
			DoAndReturn(func(_ any) error {
				wg.Done()
				return nil
			})

		resultTear, err := ss.TeardownStream(teardownCmd)
		require.NoError(t, err)
		assert.Equal(t, command.StreamTeardownCommandResult{}, resultTear)
		wg.Wait()
	})

	t.Run("TwoStreams_RemovedClosely_CleanupOnce", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		// Setup first stream
		cmd1 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())
		result1, err := ss.Stream(cmd1)
		require.NoError(t, err)

		// Setup second stream
		cmd2 := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 43,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockStreamService.EXPECT().AddClient(gomock.Any())
		result2, err := ss.Stream(cmd2)
		require.NoError(t, err)

		var wg sync.WaitGroup
		wg.Add(2) // One for Close() and one for DetachStream()

		// Expect RemoveClient and ClientCount calls for both teardowns
		gomock.InOrder(
			// First teardown
			mockStreamService.EXPECT().RemoveClient(result1.Client),
			mockStreamService.EXPECT().ClientCount().Return(1),
			// Second teardown
			mockStreamService.EXPECT().RemoveClient(result2.Client),
			mockStreamService.EXPECT().ClientCount().Return(0).Times(2),
			// Cleanup actions - should happen only once
			mockStreamService.EXPECT().Close().Do(func() { wg.Done() }),
			mockListenerService.EXPECT().
				DetachStream(validMulticastAddress).
				DoAndReturn(func(_ any) error {
					wg.Done()
					return nil
				}),
		)

		// Teardown first stream
		teardown1 := command.StreamTeardownCommand{
			Address: validMulticastAddress,
			Client:  result1.Client,
		}
		result, err := ss.TeardownStream(teardown1)
		require.NoError(t, err)
		assert.Equal(t, command.StreamTeardownCommandResult{}, result)

		// Immediately teardown second stream
		teardown2 := command.StreamTeardownCommand{
			Address: validMulticastAddress,
			Client:  result2.Client,
		}
		result, err = ss.TeardownStream(teardown2)
		require.NoError(t, err)
		assert.Equal(t, command.StreamTeardownCommandResult{}, result)

		// Wait for cleanup to complete
		wg.Wait()
	})

	t.Run("InvalidAddress_Error", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, _, _, _ := setupTestAssets(t)
		defer ctrl.Finish()

		teardownCmd := command.StreamTeardownCommand{
			Address: value.MustParseAddress("*********", 9999),
			Client:  entity.Client{},
		}

		result, err := ss.TeardownStream(teardownCmd)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get domain stream service")
		assert.Equal(t, command.StreamTeardownCommandResult{}, result)
	})
}

func TestStreamFail(t *testing.T) {
	t.Parallel()

	t.Run("DetachStreamFails_LogsError", func(t *testing.T) {
		t.Parallel()

		ctrl, ss, mockListenerService, mockStreamService, factory := setupTestAssets(t)
		defer ctrl.Finish()

		factory.EXPECT().
			Create(gomock.Any()).
			Return(mockStreamService, nil).
			Times(1)

		// Setup stream and client
		cmd := query.StreamUDPQuery{
			Address:   validMulticastAddress,
			ProgramID: 42,
			Writer:    &mockWriter{},
			Ctx:       t.Context(),
		}
		mockListenerService.EXPECT().AttachStream(gomock.Any()).Return(nil)
		mockStreamService.EXPECT().AddClient(gomock.Any())
		result, err := ss.Stream(cmd)
		require.NoError(t, err)

		var wg sync.WaitGroup
		wg.Add(2)

		// Expect cleanup with DetachStream error
		mockStreamService.EXPECT().RemoveClient(result.Client)
		mockStreamService.EXPECT().ClientCount().Return(0).Times(2)
		mockStreamService.EXPECT().Close().Do(func() { wg.Done() })
		mockListenerService.EXPECT().DetachStream(validMulticastAddress).
			DoAndReturn(func(_ any) error {
				wg.Done()
				return errors.New("detach failed")
			})

		teardownCmd := command.StreamTeardownCommand{
			Address: validMulticastAddress,
			Client:  result.Client,
		}
		_, err = ss.TeardownStream(teardownCmd)
		require.NoError(t, err) // Should not return error to caller
		wg.Wait()
	})
}

// setupTestAssets initializes the StreamService with mocked dependencies.
func setupTestAssets(t *testing.T) (
	ctrl *gomock.Controller,
	ss *services.StreamService,
	listenerService *mock_listeners.MockListenerService,
	streamService *mock_dinterfaces.MockStreamService,
	factory *mock_dinterfaces.MockStreamServiceFactory,
) {
	t.Helper()

	ctrl = gomock.NewController(t)
	listenerService = mock_listeners.NewMockListenerService(ctrl)
	factory = mock_dinterfaces.NewMockStreamServiceFactory(ctrl)
	streamService = mock_dinterfaces.NewMockStreamService(ctrl)
	configRepository := mock_repository.NewMockConfigurationRepository(ctrl)
	configRepository.EXPECT().GetConfiguration().Return(entity.Configuration{
		ScanCleanupDelay:        10 * time.Millisecond,
		ScanTimeout:             10 * time.Millisecond,
		DispatcherBufferSize:    100,
		StreamChannelBufferSize: 100,
		JoinBufferSize:          100,
		JoinsPerSecond:          100,
		ConnectionTimeout:       10 * time.Millisecond,
		UDPPacketSize:           100,
		NetworkInterfaces: value.NewNetworkInterfaces([]value.NetworkInterface{
			{
				Name:   "lo",
				Prefix: netip.MustParsePrefix("0.0.0.0/0"),
			},
		}),
	}, nil).AnyTimes()

	ss, err := services.NewStreamService(
		zerolog.Nop(),
		configRepository,
		listenerService,
		factory,
	)
	require.NoError(t, err)

	return ctrl, ss, listenerService, streamService, factory
}

// mockWriter is a simple io.Writer stub for testing.
type mockWriter struct{}

func (mw *mockWriter) Write(p []byte) (n int, err error) {
	return len(p), nil
}
