package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	ainterfaces "git.moderntv.eu/multicast-probe/internal/relay/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	dinterfaces "git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/listeners"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/repository"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/writer"
	"github.com/rs/zerolog"
)

var _ ainterfaces.StreamService = &StreamService{}

type StreamService struct {
	log                  zerolog.Logger
	configRepository     repository.ConfigurationRepository
	streamServiceFactory dinterfaces.StreamServiceFactory

	mu              sync.RWMutex
	listenerService listeners.ListenerService
	streamServices  map[value.Address]dinterfaces.StreamService
	streamJoinCh    chan value.JoinRequest
	scanJoinCh      chan value.JoinRequest
}

func NewStreamService(
	log zerolog.Logger,
	configRepository repository.ConfigurationRepository,
	listenerService listeners.ListenerService,
	streamServiceFactory dinterfaces.StreamServiceFactory,
) (ss *StreamService, err error) {
	config, err := configRepository.GetConfiguration()
	if err != nil {
		log.Error().Err(err).Msg("failed to get configuration")
		return nil, err
	}

	ss = &StreamService{
		log: log.With().
			Str("component", "relay/application_stream_service").
			Logger(),
		configRepository:     configRepository,
		listenerService:      listenerService,
		streamServiceFactory: streamServiceFactory,
		streamServices:       make(map[value.Address]dinterfaces.StreamService),
		streamJoinCh:         make(chan value.JoinRequest, config.JoinBufferSize),
		scanJoinCh:           make(chan value.JoinRequest, config.JoinBufferSize),
	}

	go ss.runJoinRateLimiter()

	return ss, nil
}

// Scan implements interfaces.StreamService.
func (ss *StreamService) Scan(c command.ScanCommand) (r command.ScanCommandResult, err error) {
	ss.log.Debug().
		Interface("address", c.Address).
		Msg("executing scan command")

	ss.mu.RLock()
	dss, exists := ss.streamServices[c.Address]
	ss.mu.RUnlock()
	if !exists {
		// Stream doesn't exists, request multicast join
		req := value.JoinRequest{
			Address: c.Address,
			Result:  make(chan error, 1),
		}
		ss.scanJoinCh <- req
		err = <-req.Result
		if err != nil {
			ss.cleanupStreamService(c.Address)
			return
		}

		ss.mu.RLock()
		dss = ss.streamServices[c.Address]
		ss.mu.RUnlock()
	}

	config, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to get configuration")
		return command.ScanCommandResult{}, err
	}

	client := entity.NewClient(value.StreamFilter{}, config.StreamChannelBufferSize, true)
	writer := writer.NewDummyWriter()
	ws := services.NewClientWriterService(ss.log, client, writer)

	ctx, cancel := context.WithCancel(context.Background())
	// cleanup client and stream after timeout
	time.AfterFunc(config.ScanTimeout, func() {
		cancel()
		dss.RemoveClient(client)
		ss.cleanupStreamService(c.Address)

		ss.log.Debug().
			Interface("address", c.Address).
			Str("dummy_client_id", client.ID.String()).
			Dur("timeout", config.ScanTimeout).
			Msg("scan timeout")
	})

	go ws.Run(ctx)

	dss.AddClient(client)

	ss.log.Debug().
		Interface("address", c.Address).
		Str("dummy_client_id", client.ID.String()).
		Dur("timeout", config.ScanTimeout).
		Msg("executed scan command")

	return
}

// StreamInfo implements interfaces.StreamService.
func (ss *StreamService) StreamInfo(
	q query.StreamInfoQuery,
) (r query.StreamInfoQueryResult, err error) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	dss, ok := ss.streamServices[q.Address]
	if !ok {
		err = fmt.Errorf("%w for address: %v", ErrStreamNotFound, q.Address)
		return
	}

	stream := dss.Stream()
	r.Address = stream.Address
	r.Clients = dss.ClientCount()
	r.Programs = make([]*entity.Program, 0, len(stream.Programs()))
	r.Metrics = &stream.Metrics

	for _, program := range stream.Programs() {
		r.Programs = append(r.Programs, program)
	}

	return
}

// StreamsInfo implements interfaces.StreamService.
func (ss *StreamService) StreamsInfo() (r query.StreamsInfoQueryResult, err error) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	r.Streams = make([]query.StreamInfoQueryResult, 0, len(ss.streamServices))
	for _, dss := range ss.streamServices {
		stream := dss.Stream()
		streamInfo := query.StreamInfoQueryResult{
			Address:  stream.Address,
			Clients:  dss.ClientCount(),
			Programs: make([]*entity.Program, 0, len(stream.Programs())),
			Metrics:  &stream.Metrics,
		}
		for _, program := range stream.Programs() {
			streamInfo.Programs = append(streamInfo.Programs, program)
		}

		r.Streams = append(r.Streams, streamInfo)
	}

	return
}

func (ss *StreamService) Stream(q query.StreamUDPQuery) (
	r query.StreamUDPQueryResult,
	err error,
) {
	ss.log.Debug().
		Interface("address", q.Address).
		Msg("executing stream command")

	ss.mu.RLock()
	dss, exists := ss.streamServices[q.Address]
	ss.mu.RUnlock()
	if !exists {
		req := value.JoinRequest{
			Address: q.Address,
			Result:  make(chan error, 1),
		}
		ss.streamJoinCh <- req
		err = <-req.Result
		if err != nil {
			ss.cleanupStreamService(q.Address)
			return
		}

		ss.mu.RLock()
		dss = ss.streamServices[q.Address]
		ss.mu.RUnlock()
	}

	filter := value.StreamFilter{
		ProgramID: q.ProgramID,
	}
	config, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to get configuration")
		return query.StreamUDPQueryResult{}, err
	}

	client := entity.NewClient(filter, config.StreamChannelBufferSize, false)
	bufferedWriter := writer.NewBufferedWriter(q.Writer)
	writer := services.NewClientWriterService(ss.log, client, bufferedWriter)
	go writer.Run(q.Ctx)

	dss.AddClient(client)

	ss.log.Debug().
		Interface("address", q.Address).
		Msg("subscribed client to stream")

	r.Client = client
	return
}

func (ss *StreamService) TeardownStream(
	c command.StreamTeardownCommand,
) (r command.StreamTeardownCommandResult, err error) {
	ss.log.Debug().
		Interface("address", c.Address).
		Msg("executing stream teardown command")

	ss.mu.Lock()
	defer ss.mu.Unlock()

	dss, ok := ss.streamServices[c.Address]
	if !ok {
		err = fmt.Errorf("failed to get domain stream service: %w", err)
		return
	}

	config, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to get configuration")
		return
	}

	dss.RemoveClient(c.Client)
	if dss.ClientCount() == 0 {
		// Remove the stream from the listener after a certain amount of
		// time to allow collecting information from management api
		time.AfterFunc(config.ScanCleanupDelay, func() {
			ss.cleanupStreamService(c.Address)
		})
	}

	return
}

func (ss *StreamService) getOrInitStreamServices(req value.JoinRequest) {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	// double check stream could have been created in the meantime.
	_, exist := ss.streamServices[req.Address]
	if exist {
		req.Result <- nil
		return
	}

	config, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to get configuration")
		return
	}

	stream := entity.NewStream(req.Address, config.StreamChannelBufferSize)
	err = ss.listenerService.AttachStream(stream)
	if err != nil {
		ss.log.Error().
			Err(err).
			Interface("address", req.Address).
			Msg("Failed to attach stream")
		err = ErrStreamCreate
		req.Result <- err
	}

	dss, err := ss.streamServiceFactory.Create(stream)
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to create stream service")
		req.Result <- err
		return
	}
	ss.streamServices[req.Address] = dss
	req.Result <- nil
}

func (ss *StreamService) cleanupStreamService(address value.Address) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	dss, ok := ss.streamServices[address]
	if !ok {
		return
	}

	// Some client is still subscribed to the stream.
	if dss.ClientCount() > 0 {
		return
	}

	dss.Close()
	delete(ss.streamServices, address)

	err := ss.listenerService.DetachStream(address)
	if err != nil {
		ss.log.Error().
			Err(err).
			Interface("address", address).
			Msg("failed to detach stream from stream listener service")
	}
}

func (ss *StreamService) runJoinRateLimiter() {
	config, err := ss.configRepository.GetConfiguration()
	if err != nil {
		ss.log.Error().Err(err).Msg("failed to get configuration")
		return
	}
	interval := time.Second / time.Duration(config.JoinsPerSecond)
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		select {
		case req := <-ss.streamJoinCh: // Stream has priority.
			ss.getOrInitStreamServices(req)
		default:
			select {
			case req := <-ss.scanJoinCh: // Scan processed if no Stream requests.
				ss.getOrInitStreamServices(req)
			default:
				break
			}
		}
	}
}
