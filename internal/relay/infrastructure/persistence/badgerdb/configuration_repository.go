package badgerdb

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/repository"
	"github.com/dgraph-io/badger/v4"
	"github.com/rs/zerolog"
)

var _ repository.ConfigurationRepository = &ConfigurationRepository{}

type ConfigurationRepository struct {
	log zerolog.Logger
	db  *badger.DB
}

func NewConfigurationRepository(
	log zerolog.Logger,
	db *badger.DB,
) (cr *ConfigurationRepository, err error) {
	cr = &ConfigurationRepository{
		log: log.With().Str("component", "management/badgerdb_configuration_repository").Logger(),
		db:  db,
	}

	err = cr.initConfiguration()
	if err != nil {
		err = fmt.Errorf("failed to initialize configuration: %w", err)
		return
	}

	return
}

// GetConfiguration implements repository.ConfigurationRepository.
func (cr *ConfigurationRepository) GetConfiguration() (c entity.Configuration, err error) {
	key := cr.configurationKey()

	err = cr.db.View(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if err != nil {
			return err
		}

		return item.Value(func(bytes []byte) error {
			err := json.Unmarshal(bytes, &c)
			if err != nil {
				err = fmt.Errorf("failed unmarshalling configuration entry: %w", err)
				return err
			}

			return nil
		})
	})
	if errors.Is(err, badger.ErrKeyNotFound) {
		err = repository.ErrConfigurationNotFound
		return
	}

	if err != nil {
		err = fmt.Errorf("failed getting configuration entry: %w", err)
		return
	}

	return
}

// UpdateConfiguration implements repository.ConfigurationRepository.
func (cr *ConfigurationRepository) UpdateConfiguration(
	newC entity.Configuration,
) (c entity.Configuration, err error) {
	key := cr.configurationKey()

	err = cr.db.Update(func(txn *badger.Txn) error {
		item, err := txn.Get(key)

		// No configuration exists, just save the new one.
		if errors.Is(err, badger.ErrKeyNotFound) {
			bytes, err := json.Marshal(newC)
			if err != nil {
				err = fmt.Errorf("failed marshalling new configuration: %w", err)
				return err
			}

			e := badger.NewEntry(key, bytes)

			return txn.SetEntry(e)
		}

		if err != nil {
			err = fmt.Errorf("failed getting old configuration entry: %w", err)
			return err
		}

		// Configuration exists, merge it with the new one.
		err = item.Value(func(bytes []byte) error {
			err := json.Unmarshal(bytes, &c)
			if err != nil {
				err = fmt.Errorf("failed unmarshalling old configuration entry: %w", err)
				return err
			}

			return nil
		})
		if err != nil {
			err = fmt.Errorf("failed getting old configuration value: %w", err)
			return err
		}

		c.Merge(newC)

		bytes, err := json.Marshal(c)
		if err != nil {
			err = fmt.Errorf("failed marshalling new configuration: %w", err)
			return err
		}

		e := badger.NewEntry(key, bytes)

		return txn.SetEntry(e)
	})
	if err != nil {
		err = fmt.Errorf("failed updating configuration entry: %w", err)
		return
	}

	return
}

func (cr *ConfigurationRepository) initConfiguration() error {
	key := cr.configurationKey()

	err := cr.db.Update(func(txn *badger.Txn) error {
		_, err := txn.Get(key)

		// No configuration exists, save the default.
		if errors.Is(err, badger.ErrKeyNotFound) {
			c := entity.NewDefaultConfiguration()
			bytes, err := json.Marshal(c)
			if err != nil {
				err = fmt.Errorf("failed marshalling default configuration: %w", err)
				return err
			}

			e := badger.NewEntry(key, bytes)

			return txn.SetEntry(e)
		}

		return err
	})
	if err != nil {
		return fmt.Errorf("failed updating configuration entry: %w", err)
	}

	return nil
}

func (*ConfigurationRepository) configurationKey() []byte {
	return []byte("configuration")
}
