package network

import (
	"fmt"
	"sync"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/listeners"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/repository"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
)

var _ listeners.ListenerService = &PortListenerService{}

type PortListenerService struct {
	log              zerolog.Logger
	configRepository repository.ConfigurationRepository

	mu        sync.RWMutex
	listeners map[uint16]listeners.Listener // Port to listener
}

func NewStreamListenerService(
	log zerolog.Logger,
	configRepository repository.ConfigurationRepository,
) *PortListenerService {
	return &PortListenerService{
		log:              log.With().Str("component", "relay/stream_listener_service").<PERSON><PERSON>(),
		configRepository: configRepository,

		listeners: make(map[uint16]listeners.Listener),
	}
}

func (sls *PortListenerService) AttachStream(stream *entity.Stream) error {
	sls.mu.Lock()
	defer sls.mu.Unlock()

	listener, exists := sls.listeners[stream.Address.Port()]
	if exists {
		return listener.AttachStream(stream)
	}

	config, err := sls.configRepository.GetConfiguration()
	if err != nil {
		return fmt.Errorf("failed to get configuration: %w", err)
	}

	networkInterface, err := config.NetworkInterfaces.MatchAddress(stream.Address)
	if err != nil {
		return fmt.Errorf("failed to match address to interface: %w", err)
	}

	listener, err = NewPortListener(
		sls.log,
		stream.Address.Port(),
		config.UDPPacketSize,
		config.ConnectionTimeout,
		networkInterface,
	)
	if err != nil {
		return fmt.Errorf("%w: %w", err, listeners.ErrPortListenCreate)
	}

	sls.listeners[stream.Address.Port()] = listener

	go listener.Listen()

	return listener.AttachStream(stream)
}

func (sls *PortListenerService) DetachStream(address value.Address) error {
	sls.mu.Lock()
	defer sls.mu.Unlock()

	if listener, exists := sls.listeners[address.Port()]; exists {
		err := listener.DetachStream(address)
		if err != nil {
			return err
		}

		if !listener.HasStreams() {
			listener.Close()
			delete(sls.listeners, address.Port())
		}
	}

	return nil
}
