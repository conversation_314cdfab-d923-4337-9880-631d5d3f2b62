package network_test

import (
	"net"
	"net/netip"
	"sync"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/listeners"
	mock_repository "git.moderntv.eu/multicast-probe/internal/relay/domain/repository/mocks"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"git.moderntv.eu/multicast-probe/internal/relay/infrastructure/network"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

// portAllocator helps manage test ports to avoid conflicts.
type portAllocator struct {
	mu    sync.Mutex
	ports map[uint16]bool
	start uint16
}

func newPortAllocator(start uint16) *portAllocator {
	return &portAllocator{
		ports: make(map[uint16]bool),
		start: start,
	}
}

func (pa *portAllocator) nextPort() uint16 {
	pa.mu.Lock()
	defer pa.mu.Unlock()

	port := pa.start
	for pa.ports[port] {
		port++
	}

	pa.ports[port] = true
	return port
}

var testPorts = newPortAllocator(5000)

func TestPortListenerService(t *testing.T) {
	t.Parallel()

	t.Run("AttachStream", func(t *testing.T) {
		t.Parallel()
		t.Run("NewPort_Success", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			stream := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err := ls.AttachStream(stream)
			require.NoError(t, err)
		})

		t.Run("ExistingPort_Success", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			stream1 := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}
			stream2 := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err := ls.AttachStream(stream1)
			require.NoError(t, err)

			err = ls.AttachStream(stream2)
			require.NoError(t, err)
		})

		t.Run("PortInUse_Error", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			// Bind to port first to simulate it being in use
			addr := net.UDPAddr{Port: int(port)}
			conn, err := net.ListenUDP("udp", &addr)
			require.NoError(t, err)
			defer conn.Close()

			stream := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err = ls.AttachStream(stream)
			require.Error(t, err)
			assert.ErrorIs(t, err, listeners.ErrPortListenCreate)
		})
	})

	t.Run("DetachStream", func(t *testing.T) {
		t.Parallel()
		t.Run("ExistingStream_Success", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			stream := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err := ls.AttachStream(stream)
			require.NoError(t, err)

			err = ls.DetachStream(stream.Address)
			require.NoError(t, err)
		})

		t.Run("NonexistentStream_NoError", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			address := value.MustParseAddress("*********", port)

			err := ls.DetachStream(address)
			require.NoError(t, err)
		})

		t.Run("MultipleStreamsOnPort_KeepListenerActive", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()

			stream1 := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}
			stream2 := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}
			stream3 := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err := ls.AttachStream(stream1)
			require.NoError(t, err)

			err = ls.AttachStream(stream2)
			require.NoError(t, err)

			err = ls.DetachStream(stream1.Address)
			require.NoError(t, err)

			err = ls.AttachStream(stream3)
			require.NoError(t, err)
		})
		t.Run("LastStreamOnPort_CloseListener", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)
			port := testPorts.nextPort()
			stream := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err := ls.AttachStream(stream)
			require.NoError(t, err)

			err = ls.DetachStream(stream.Address)
			require.NoError(t, err)

			// Verify internal state (would need to expose a method to check if listener exists)
			// For now, we can verify by trying to attach a new stream
			newStream := &entity.Stream{
				Address: value.MustParseAddress("*********", port),
			}

			err = ls.AttachStream(newStream)
			require.NoError(t, err)
		})
	})

	t.Run("ConcurrentOperations", func(t *testing.T) {
		t.Parallel()
		t.Run("AttachMultipleStreams", func(t *testing.T) {
			t.Parallel()
			ls := setupTestListener(t)

			done := make(chan bool)
			for range 10 {
				go func() {
					port := testPorts.nextPort()
					stream := &entity.Stream{
						Address: value.MustParseAddress("*********", port),
					}

					err := ls.AttachStream(stream)
					assert.NoError(t, err)
					done <- true
				}()
			}

			for range 10 {
				<-done
			}
		})
	})
}

func setupTestListener(t *testing.T) *network.PortListenerService {
	t.Helper()
	ctrl := gomock.NewController(t)
	configRepository := mock_repository.NewMockConfigurationRepository(ctrl)
	configRepository.EXPECT().GetConfiguration().Return(entity.Configuration{
		ScanCleanupDelay:        10 * time.Millisecond,
		ScanTimeout:             10 * time.Millisecond,
		DispatcherBufferSize:    100,
		StreamChannelBufferSize: 100,
		JoinBufferSize:          100,
		JoinsPerSecond:          100,
		ConnectionTimeout:       10 * time.Millisecond,
		UDPPacketSize:           100,
		NetworkInterfaces: value.NewNetworkInterfaces([]value.NetworkInterface{
			{
				Name:   "lo",
				Prefix: netip.MustParsePrefix("0.0.0.0/0"),
			},
		}),
	}, nil).AnyTimes()

	ls := network.NewStreamListenerService(zerolog.Nop(), configRepository)
	return ls
}
