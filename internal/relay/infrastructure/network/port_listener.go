package network

import (
	"fmt"
	"net"
	"net/netip"
	"sync"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/listeners"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
	"golang.org/x/net/ipv4"
)

var _ listeners.Listener = &PortListener{}

type PortListener struct {
	log        zerolog.Logger
	port       uint16
	packetSize int
	timeout    time.Duration
	iface      *net.Interface
	conn       *ipv4.PacketConn

	mu      sync.RWMutex
	streams map[netip.Addr]*entity.Stream

	stopCh chan struct{}
	doneCh chan struct{}
}

func NewPortListener(
	log zerolog.Logger,
	port uint16,
	packetSize int,
	timeout time.Duration,
	ni value.NetworkInterface,
) (pl *PortListener, err error) {
	networkInterface, err := net.InterfaceByName(ni.Name)
	if err != nil {
		log.Error().
			Err(err).
			Msg("Failed to get network interface")
		err = fmt.Errorf("failed getting network interface: %w", err)
		return
	}

	conn, err := net.ListenPacket("udp4", fmt.Sprintf("0.0.0.0:%d", port))
	if err != nil {
		log.Error().
			Err(err).
			Uint16("port", port).
			Msg("Failed to initialize connection for port")
		err = fmt.Errorf("failed listening for packets: %w", err)
		return
	}

	packetConn := ipv4.NewPacketConn(conn)
	err = packetConn.SetControlMessage(ipv4.FlagDst, true)
	if err != nil {
		log.Error().
			Err(err).
			Uint16("port", port).
			Msg("Failed to set control message for port ")
		conn.Close()
		err = fmt.Errorf("failed setting control message: %w", err)
		return
	}

	log.Debug().
		Interface("network_interface", ni.Name).
		Msg("Created port listener")

	pl = &PortListener{
		log:        log.With().Str("component", "relay/network_port_listener").Logger(),
		port:       port,
		packetSize: packetSize,
		timeout:    timeout,
		iface:      networkInterface,
		conn:       packetConn,

		streams: make(map[netip.Addr]*entity.Stream),

		stopCh: make(chan struct{}),
		doneCh: make(chan struct{}),
	}

	return
}

// joins a multicast group on this listener's socket.
func (pl *PortListener) AttachStream(stream *entity.Stream) error {
	pl.mu.Lock()
	defer pl.mu.Unlock()
	group := stream.Address.Addr()

	_, ok := pl.streams[group]
	if ok {
		// Stream is already attached
		return nil
	}

	addr, err := net.ResolveUDPAddr("udp", stream.Address.String())
	if err != nil {
		return fmt.Errorf("failed to resolve UDP address %w", err)
	}

	err = pl.conn.JoinGroup(pl.iface, addr)
	if err != nil {
		return fmt.Errorf("failed to join multicast group %w", err)
	}

	pl.streams[group] = stream

	pl.log.Debug().
		Interface("multicast_group", group).
		Msg("Joined multicast group")

	return nil
}

func (pl *PortListener) DetachStream(address value.Address) error {
	pl.mu.Lock()
	defer pl.mu.Unlock()

	group := address.Addr()
	_, ok := pl.streams[group]
	if !ok {
		pl.log.Debug().
			Interface("multicast_group", group).
			Msg("No streams attached for group")
		return nil
	}

	addr, err := net.ResolveUDPAddr("udp", address.String())
	if err != nil {
		return fmt.Errorf("failed to resolve UDP address: %w", err)
	}

	if err := pl.conn.LeaveGroup(pl.iface, addr); err != nil {
		pl.log.Error().
			Err(err).
			Interface("multicast_group", group).
			Msg("Failed to leave multicast group")
		return fmt.Errorf("failed to leave multicast group %s: %w", group, err)
	}

	delete(pl.streams, group)

	pl.log.Debug().
		Interface("multicast_group", group).
		Msg("Left multicast group")

	return nil
}

func (pl *PortListener) Listen() {
	if pl.conn == nil {
		pl.log.Error().
			Uint16("port", pl.port).
			Msg("Connection not initialized for port")
		return
	}

	defer func() {
		pl.conn.Close()
		pl.log.Info().
			Uint16("port", pl.port).
			Msg("Stopping listener on port")
		close(pl.doneCh)
	}()

	buf := make([]byte, pl.packetSize) // UDP packet size
	for {
		err := pl.conn.SetReadDeadline(time.Now().Add(pl.timeout))
		if err != nil {
			pl.log.Error().Err(err).Send()
			return
		}

		select {
		case <-pl.stopCh:
			return
		default:
			pl.handlePacket(buf)
		}
	}
}

func (pl *PortListener) HasStreams() bool {
	pl.mu.RLock()
	defer pl.mu.RUnlock()

	return len(pl.streams) > 0
}

// Close implements listeners.PortListener.
func (pl *PortListener) Close() {
	close(pl.stopCh)
	<-pl.doneCh
}

func (pl *PortListener) handlePacket(buf []byte) {
	n, cm, _, err := pl.conn.ReadFrom(buf)
	if err != nil {
		pl.log.Error().
			Err(err).
			Msg("failed to read from port")
		return
	}

	// Copy packet data to avoid overwriting buffer on next read
	packet := make(value.UDPPacket, n)
	copy(packet, buf[:n])
	pl.mu.RLock()
	defer pl.mu.RUnlock()

	address, ok := netip.AddrFromSlice(cm.Dst)
	if !ok {
		pl.log.Error().
			Err(err).
			Msg("failed to parse netip address from destination net ip")
		return
	}

	stream, ok := pl.streams[address]
	if !ok {
		pl.log.Debug().
			Str("multicast_group", cm.Dst.String()).
			Msg("handling packet for non-existent stream")
		return
	}

	if stream.State() != value.Running {
		return
	}

	select {
	case stream.Buffer <- packet:
	default:
		pl.log.Warn().
			Interface("address", address).
			Interface("state", stream.State()).
			Msg("buffer full for stream")
	}
}
