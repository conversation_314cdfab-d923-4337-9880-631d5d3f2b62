// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/repository (interfaces: ConfigurationRepository)
//
// Generated by this command:
//
//	mockgen -destination mocks/configuration_repository_mock.go . ConfigurationRepository
//

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockConfigurationRepository is a mock of ConfigurationRepository interface.
type MockConfigurationRepository struct {
	ctrl     *gomock.Controller
	recorder *MockConfigurationRepositoryMockRecorder
	isgomock struct{}
}

// MockConfigurationRepositoryMockRecorder is the mock recorder for MockConfigurationRepository.
type MockConfigurationRepositoryMockRecorder struct {
	mock *MockConfigurationRepository
}

// NewMockConfigurationRepository creates a new mock instance.
func NewMockConfigurationRepository(ctrl *gomock.Controller) *MockConfigurationRepository {
	mock := &MockConfigurationRepository{ctrl: ctrl}
	mock.recorder = &MockConfigurationRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigurationRepository) EXPECT() *MockConfigurationRepositoryMockRecorder {
	return m.recorder
}

// GetConfiguration mocks base method.
func (m *MockConfigurationRepository) GetConfiguration() (entity.Configuration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfiguration")
	ret0, _ := ret[0].(entity.Configuration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfiguration indicates an expected call of GetConfiguration.
func (mr *MockConfigurationRepositoryMockRecorder) GetConfiguration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfiguration", reflect.TypeOf((*MockConfigurationRepository)(nil).GetConfiguration))
}

// UpdateConfiguration mocks base method.
func (m *MockConfigurationRepository) UpdateConfiguration(newC entity.Configuration) (entity.Configuration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfiguration", newC)
	ret0, _ := ret[0].(entity.Configuration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateConfiguration indicates an expected call of UpdateConfiguration.
func (mr *MockConfigurationRepositoryMockRecorder) UpdateConfiguration(newC any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfiguration", reflect.TypeOf((*MockConfigurationRepository)(nil).UpdateConfiguration), newC)
}
