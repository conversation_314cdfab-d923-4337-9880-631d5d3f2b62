package entity

import (
	"sync"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/Comcast/gots/packet"
)

type Stream struct {
	Address  value.Address
	Buffer   chan value.UDPPacket
	programs map[int]*Program
	Metrics  value.StreamMetrics

	mu    sync.RWMutex
	state value.StreamState

	createdAt              time.Time
	lastReceivedTSPacketAt time.Time

	timeout time.Duration
}

func NewStream(address value.Address, streamChannelBufferSize int) *Stream {
	return &Stream{
		Address:   address,
		state:     value.Initializing,
		Buffer:    make(chan value.UDPPacket, streamChannelBufferSize),
		createdAt: time.Now(),
		timeout:   time.Second,
		Metrics:   value.StreamMetrics{},
		programs:  make(map[int]*Program),
	}
}

func (s *Stream) UpdateStreamPresentDuration() {
	s.Metrics.PresentDuration.Store(time.Since(s.createdAt).Milliseconds())
}

func (s *Stream) UpdateStreamReceivingDuration() {
	newTSPacketReceivedAt := time.Now()
	if s.lastReceivedTSPacketAt.IsZero() {
		s.lastReceivedTSPacketAt = newTSPacketReceivedAt
		s.Metrics.ReceivingDuration.Store(0)
		return
	}
	if s.isTSPacketTimeout(newTSPacketReceivedAt) {
		s.lastReceivedTSPacketAt = newTSPacketReceivedAt
		return
	}

	duration := newTSPacketReceivedAt.Sub(s.lastReceivedTSPacketAt).Milliseconds()
	s.Metrics.ReceivingDuration.Add(duration)
	s.lastReceivedTSPacketAt = newTSPacketReceivedAt
}

func (s *Stream) UpdateReceivedTSPackets() {
	s.Metrics.ReceivedTSPackets.Add(1)
	s.Metrics.ReceivedBytes.Add(packet.PacketSize)
}

func (s *Stream) State() value.StreamState {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.state
}

func (s *Stream) SetState(state value.StreamState) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.state = state
}

func (s *Stream) Programs() map[int]*Program {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.programs
}

func (s *Stream) SetPrograms(programs map[int]*Program) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.programs = programs
}

func (s *Stream) isTSPacketTimeout(newTSPacketReceivedAt time.Time) bool {
	return newTSPacketReceivedAt.Sub(s.lastReceivedTSPacketAt) > s.timeout
}
