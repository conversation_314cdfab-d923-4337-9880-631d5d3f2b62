package entity

import (
	"sync"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	gots "github.com/Comcast/gots/packet"
)

type Program struct {
	ID       int    // Program ID from PMT
	Title    string // Program Name
	Provider string // Provider Name
	mu       sync.RWMutex
	tracks   map[int]*Track // List of tracks
	Metrics  *value.ProgramMetrics
}

func NewProgram(id int, title string, provider string) *Program {
	return &Program{
		ID:       id,
		Title:    title,
		Provider: provider,
		tracks:   make(map[int]*Track),
		Metrics:  &value.ProgramMetrics{},
	}
}

func (p *Program) UpdateMetrics(packet *value.Packet) {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.Metrics.ReceivedTSPackets.Add(1)
	p.Metrics.ReceivedBytes.Add(gots.PacketSize)

	if packet.DiscontinuityOccurred {
		p.Metrics.Discontinuities.Add(1)
	}

	track, trackFound := p.tracks[packet.PID]

	if trackFound {
		track.UpdateMetrics(packet)
	}
}

func (p *Program) SetTracks(tracks map[int]*Track) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.tracks = tracks
}

func (p *Program) Tracks() map[int]*Track {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return p.tracks
}
