package entity

import (
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/google/uuid"
)

type Client struct {
	ID       uuid.UUID
	Filter   value.StreamFilter
	StreamCh chan value.TSPacket
	IsDummy  bool
}

func NewClient(filter value.StreamFilter, streamChannelBufferSize int, isDummy bool) Client {
	return Client{
		ID:       uuid.New(),
		Filter:   filter,
		StreamCh: make(chan value.TSPacket, streamChannelBufferSize),
		IsDummy:  isDummy,
	}
}
