package entity

import (
	"errors"
	"net/netip"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
)

var ErrInvalidConfiguration = errors.New("invalid configuration")

type Configuration struct {
	ScanTimeout             time.Duration           `json:"scanTimeout"`
	ScanCleanupDelay        time.Duration           `json:"scanCleanupDelay"`
	DispatcherBufferSize    int                     `json:"dispatchBufferSize"`
	StreamChannelBufferSize int                     `json:"streamChannelBufferSize"`
	JoinBufferSize          int                     `json:"joinBufferSize"`
	JoinsPerSecond          int                     `json:"joinsPerSecond"`
	NetworkInterfaces       value.NetworkInterfaces `json:"networkInterfaces"`
	UDPPacketSize           int                     `json:"udpPacketSize"`
	ConnectionTimeout       time.Duration           `json:"connectionTimeout"`
}

func NewDefaultConfiguration() Configuration {
	return Configuration{
		ScanTimeout:             60 * time.Second, //nolint: mnd
		ScanCleanupDelay:        60 * time.Second, //nolint: mnd
		DispatcherBufferSize:    1024,             //nolint: mnd
		StreamChannelBufferSize: 1024,             //nolint: mnd
		JoinsPerSecond:          2,                //nolint: mnd
		JoinBufferSize:          1024,             //nolint: mnd
		UDPPacketSize:           1500,             //nolint: mnd
		ConnectionTimeout:       1 * time.Second,
		NetworkInterfaces: value.NewNetworkInterfaces([]value.NetworkInterface{
			{
				Name:   "lo",
				Prefix: netip.MustParsePrefix("0.0.0.0/0"),
			},
			{
				Name:   "vlan1",
				Prefix: netip.MustParsePrefix("*********/16"),
			},
			{
				Name:   "vlan2",
				Prefix: netip.MustParsePrefix("*********/24"),
			},
		}),
	}
}

func (c *Configuration) Merge(newC Configuration) {
	if newC.ScanTimeout != 0 {
		c.ScanTimeout = newC.ScanTimeout
	}
	if newC.ScanCleanupDelay != 0 {
		c.ScanCleanupDelay = newC.ScanCleanupDelay
	}
	if newC.DispatcherBufferSize != 0 {
		c.DispatcherBufferSize = newC.DispatcherBufferSize
	}
	if newC.StreamChannelBufferSize != 0 {
		c.StreamChannelBufferSize = newC.StreamChannelBufferSize
	}
	if newC.JoinBufferSize != 0 {
		c.JoinBufferSize = newC.JoinBufferSize
	}
	if newC.JoinsPerSecond != 0 {
		c.JoinsPerSecond = newC.JoinsPerSecond
	}
	if newC.UDPPacketSize != 0 {
		c.UDPPacketSize = newC.UDPPacketSize
	}
	if newC.ConnectionTimeout != 0 {
		c.ConnectionTimeout = newC.ConnectionTimeout
	}
	if len(newC.NetworkInterfaces) > 0 {
		c.NetworkInterfaces = newC.NetworkInterfaces
	}
}

func (c *Configuration) Validate() error {
	if c.ScanCleanupDelay < 0 ||
		c.ScanTimeout < 0 ||
		c.DispatcherBufferSize < 0 ||
		c.StreamChannelBufferSize < 0 ||
		c.JoinsPerSecond < 0 ||
		c.JoinBufferSize < 0 ||
		len(c.NetworkInterfaces) == 0 {
		return ErrInvalidConfiguration
	}

	return nil
}
