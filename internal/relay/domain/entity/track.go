package entity

import (
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	gots "github.com/Comcast/gots/packet"
)

type Track struct {
	PID         int
	Type        uint8
	Description string
	Metrics     *value.TrackMetrics // per-track metrics
}

func NewTrack(pid int, trackType uint8, description string) *Track {
	return &Track{
		PID:         pid,
		Type:        trackType,
		Description: description,
		Metrics:     &value.TrackMetrics{},
	}
}

func (t *Track) UpdateMetrics(packet *value.Packet) {
	t.Metrics.ReceivedTSPackets.Add(1)
	t.Metrics.ReceivedBytes.Add(gots.PacketSize)

	if packet.DiscontinuityOccurred {
		t.Metrics.Discontinuities.Add(1)
	}
}
