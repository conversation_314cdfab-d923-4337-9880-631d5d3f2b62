package factory

import (
	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/repository"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
)

var _ interfaces.StreamServiceFactory = &StreamServiceFactory{}

type StreamServiceFactory struct {
	log              zerolog.Logger
	factoryLogger    zerolog.Logger
	configRepository repository.ConfigurationRepository
}

func NewStreamServiceFactory(
	log zerolog.Logger,
	configRepository repository.ConfigurationRepository,
) *StreamServiceFactory {
	return &StreamServiceFactory{
		log:              log.With().Str("component", "relay/stream_service_factory").Logger(),
		factoryLogger:    log,
		configRepository: configRepository,
	}
}

func (ssf *StreamServiceFactory) Create( //nolint: ireturn
	stream *entity.Stream,
) (interfaces.StreamService, error) {
	config, err := ssf.configRepository.GetConfiguration()
	if err != nil {
		ssf.log.Error().Err(err).Msg("failed to get configuration")
		return nil, err
	}

	dispatchCh := make(chan *value.Packet, config.DispatcherBufferSize)
	ms := services.NewMPEGTSService(ssf.factoryLogger)
	sfs := services.NewStreamForwardingService(ssf.factoryLogger, stream, ms, dispatchCh)
	sds := services.NewStreamDispatchService(ssf.factoryLogger, dispatchCh)
	ssf.log.Debug().
		Str("stream_address", stream.Address.String()).
		Msg("creating stream service for stream")

	return services.NewStreamService(ssf.factoryLogger, ms, sfs, sds, stream), nil
}
