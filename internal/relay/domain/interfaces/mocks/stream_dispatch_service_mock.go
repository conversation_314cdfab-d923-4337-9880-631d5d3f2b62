// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: StreamDispatchService)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_dispatch_service_mock.go . StreamDispatchService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
)

// MockStreamDispatchService is a mock of StreamDispatchService interface.
type MockStreamDispatchService struct {
	ctrl     *gomock.Controller
	recorder *MockStreamDispatchServiceMockRecorder
	isgomock struct{}
}

// MockStreamDispatchServiceMockRecorder is the mock recorder for MockStreamDispatchService.
type MockStreamDispatchServiceMockRecorder struct {
	mock *MockStreamDispatchService
}

// NewMockStreamDispatchService creates a new mock instance.
func NewMockStreamDispatchService(ctrl *gomock.Controller) *MockStreamDispatchService {
	mock := &MockStreamDispatchService{ctrl: ctrl}
	mock.recorder = &MockStreamDispatchServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamDispatchService) EXPECT() *MockStreamDispatchServiceMockRecorder {
	return m.recorder
}

// AddClient mocks base method.
func (m *MockStreamDispatchService) AddClient(client entity.Client) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddClient", client)
}

// AddClient indicates an expected call of AddClient.
func (mr *MockStreamDispatchServiceMockRecorder) AddClient(client any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddClient", reflect.TypeOf((*MockStreamDispatchService)(nil).AddClient), client)
}

// ClientCount mocks base method.
func (m *MockStreamDispatchService) ClientCount() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientCount")
	ret0, _ := ret[0].(int)
	return ret0
}

// ClientCount indicates an expected call of ClientCount.
func (mr *MockStreamDispatchServiceMockRecorder) ClientCount() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientCount", reflect.TypeOf((*MockStreamDispatchService)(nil).ClientCount))
}

// RemoveClient mocks base method.
func (m *MockStreamDispatchService) RemoveClient(clientID uuid.UUID) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RemoveClient", clientID)
}

// RemoveClient indicates an expected call of RemoveClient.
func (mr *MockStreamDispatchServiceMockRecorder) RemoveClient(clientID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveClient", reflect.TypeOf((*MockStreamDispatchService)(nil).RemoveClient), clientID)
}

// Run mocks base method.
func (m *MockStreamDispatchService) Run(readyCh chan bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Run", readyCh)
}

// Run indicates an expected call of Run.
func (mr *MockStreamDispatchServiceMockRecorder) Run(readyCh any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockStreamDispatchService)(nil).Run), readyCh)
}
