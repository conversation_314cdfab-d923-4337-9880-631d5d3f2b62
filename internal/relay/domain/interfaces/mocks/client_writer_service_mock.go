// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: ClientWriterService)
//
// Generated by this command:
//
//	mockgen -destination mocks/client_writer_service_mock.go . ClientWriterService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockClientWriterService is a mock of ClientWriterService interface.
type MockClientWriterService struct {
	ctrl     *gomock.Controller
	recorder *MockClientWriterServiceMockRecorder
	isgomock struct{}
}

// MockClientWriterServiceMockRecorder is the mock recorder for MockClientWriterService.
type MockClientWriterServiceMockRecorder struct {
	mock *MockClientWriterService
}

// NewMockClientWriterService creates a new mock instance.
func NewMockClientWriterService(ctrl *gomock.Controller) *MockClientWriterService {
	mock := &MockClientWriterService{ctrl: ctrl}
	mock.recorder = &MockClientWriterServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientWriterService) EXPECT() *MockClientWriterServiceMockRecorder {
	return m.recorder
}

// Run mocks base method.
func (m *MockClientWriterService) Run(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Run", ctx)
}

// Run indicates an expected call of Run.
func (mr *MockClientWriterServiceMockRecorder) Run(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockClientWriterService)(nil).Run), ctx)
}
