// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: MPEGTSService)
//
// Generated by this command:
//
//	mockgen -destination mocks/mpegts_service_mock.go . MPEGTSService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	value "git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	gomock "go.uber.org/mock/gomock"
)

// MockMPEGTSService is a mock of MPEGTSService interface.
type MockMPEGTSService struct {
	ctrl     *gomock.Controller
	recorder *MockMPEGTSServiceMockRecorder
	isgomock struct{}
}

// MockMPEGTSServiceMockRecorder is the mock recorder for MockMPEGTSService.
type MockMPEGTSServiceMockRecorder struct {
	mock *MockMPEGTSService
}

// NewMockMPEGTSService creates a new mock instance.
func NewMockMPEGTSService(ctrl *gomock.Controller) *MockMPEGTSService {
	mock := &MockMPEGTSService{ctrl: ctrl}
	mock.recorder = &MockMPEGTSServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMPEGTSService) EXPECT() *MockMPEGTSServiceMockRecorder {
	return m.recorder
}

// ProcessPacket mocks base method.
func (m *MockMPEGTSService) ProcessPacket(tsPacket value.TSPacket) (*value.Packet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPacket", tsPacket)
	ret0, _ := ret[0].(*value.Packet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPacket indicates an expected call of ProcessPacket.
func (mr *MockMPEGTSServiceMockRecorder) ProcessPacket(tsPacket any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPacket", reflect.TypeOf((*MockMPEGTSService)(nil).ProcessPacket), tsPacket)
}

// ProgramInfo mocks base method.
func (m *MockMPEGTSService) ProgramInfo() map[int]*entity.Program {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProgramInfo")
	ret0, _ := ret[0].(map[int]*entity.Program)
	return ret0
}

// ProgramInfo indicates an expected call of ProgramInfo.
func (mr *MockMPEGTSServiceMockRecorder) ProgramInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProgramInfo", reflect.TypeOf((*MockMPEGTSService)(nil).ProgramInfo))
}
