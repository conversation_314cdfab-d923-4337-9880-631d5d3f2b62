// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: StreamService)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_service_mock.go . StreamService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockStreamService is a mock of StreamService interface.
type MockStreamService struct {
	ctrl     *gomock.Controller
	recorder *MockStreamServiceMockRecorder
	isgomock struct{}
}

// MockStreamServiceMockRecorder is the mock recorder for MockStreamService.
type MockStreamServiceMockRecorder struct {
	mock *MockStreamService
}

// NewMockStreamService creates a new mock instance.
func NewMockStreamService(ctrl *gomock.Controller) *MockStreamService {
	mock := &MockStreamService{ctrl: ctrl}
	mock.recorder = &MockStreamServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamService) EXPECT() *MockStreamServiceMockRecorder {
	return m.recorder
}

// AddClient mocks base method.
func (m *MockStreamService) AddClient(client entity.Client) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddClient", client)
}

// AddClient indicates an expected call of AddClient.
func (mr *MockStreamServiceMockRecorder) AddClient(client any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddClient", reflect.TypeOf((*MockStreamService)(nil).AddClient), client)
}

// ClientCount mocks base method.
func (m *MockStreamService) ClientCount() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientCount")
	ret0, _ := ret[0].(int)
	return ret0
}

// ClientCount indicates an expected call of ClientCount.
func (mr *MockStreamServiceMockRecorder) ClientCount() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientCount", reflect.TypeOf((*MockStreamService)(nil).ClientCount))
}

// Close mocks base method.
func (m *MockStreamService) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockStreamServiceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockStreamService)(nil).Close))
}

// RemoveClient mocks base method.
func (m *MockStreamService) RemoveClient(client entity.Client) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RemoveClient", client)
}

// RemoveClient indicates an expected call of RemoveClient.
func (mr *MockStreamServiceMockRecorder) RemoveClient(client any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveClient", reflect.TypeOf((*MockStreamService)(nil).RemoveClient), client)
}

// Stream mocks base method.
func (m *MockStreamService) Stream() *entity.Stream {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stream")
	ret0, _ := ret[0].(*entity.Stream)
	return ret0
}

// Stream indicates an expected call of Stream.
func (mr *MockStreamServiceMockRecorder) Stream() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stream", reflect.TypeOf((*MockStreamService)(nil).Stream))
}
