// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: StreamServiceFactory)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_service_factory_mock.go . StreamServiceFactory
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	interfaces "git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	gomock "go.uber.org/mock/gomock"
)

// MockStreamServiceFactory is a mock of StreamServiceFactory interface.
type MockStreamServiceFactory struct {
	ctrl     *gomock.Controller
	recorder *MockStreamServiceFactoryMockRecorder
	isgomock struct{}
}

// MockStreamServiceFactoryMockRecorder is the mock recorder for MockStreamServiceFactory.
type MockStreamServiceFactoryMockRecorder struct {
	mock *MockStreamServiceFactory
}

// NewMockStreamServiceFactory creates a new mock instance.
func NewMockStreamServiceFactory(ctrl *gomock.Controller) *MockStreamServiceFactory {
	mock := &MockStreamServiceFactory{ctrl: ctrl}
	mock.recorder = &MockStreamServiceFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamServiceFactory) EXPECT() *MockStreamServiceFactoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockStreamServiceFactory) Create(stream *entity.Stream) (interfaces.StreamService, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", stream)
	ret0, _ := ret[0].(interfaces.StreamService)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockStreamServiceFactoryMockRecorder) Create(stream any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockStreamServiceFactory)(nil).Create), stream)
}
