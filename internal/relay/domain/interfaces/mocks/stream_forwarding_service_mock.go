// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces (interfaces: StreamForwardingService)
//
// Generated by this command:
//
//	mockgen -destination mocks/stream_forwarding_service_mock.go . StreamForwardingService
//

// Package mock_interfaces is a generated GoMock package.
package mock_interfaces

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockStreamForwardingService is a mock of StreamForwardingService interface.
type MockStreamForwardingService struct {
	ctrl     *gomock.Controller
	recorder *MockStreamForwardingServiceMockRecorder
	isgomock struct{}
}

// MockStreamForwardingServiceMockRecorder is the mock recorder for MockStreamForwardingService.
type MockStreamForwardingServiceMockRecorder struct {
	mock *MockStreamForwardingService
}

// NewMockStreamForwardingService creates a new mock instance.
func NewMockStreamForwardingService(ctrl *gomock.Controller) *MockStreamForwardingService {
	mock := &MockStreamForwardingService{ctrl: ctrl}
	mock.recorder = &MockStreamForwardingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStreamForwardingService) EXPECT() *MockStreamForwardingServiceMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockStreamForwardingService) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockStreamForwardingServiceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockStreamForwardingService)(nil).Close))
}

// Run mocks base method.
func (m *MockStreamForwardingService) Run(readyCh chan bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Run", readyCh)
}

// Run indicates an expected call of Run.
func (mr *MockStreamForwardingServiceMockRecorder) Run(readyCh any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockStreamForwardingService)(nil).Run), readyCh)
}
