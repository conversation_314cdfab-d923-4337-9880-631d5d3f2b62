package services_test

import (
	"errors"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	mock_interfaces "git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces/mocks"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestStreamForwardingService(t *testing.T) { //nolint: cyclop
	t.Parallel()

	t.Run("Run_ForwardsPacketsSuccessfully", func(t *testing.T) {
		t.<PERSON>llel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Running)
		dispatchCh := make(chan *value.Packet, 10)
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Setup mock expectations
		testData := make([]byte, 7*188) // 7 MPEGTS packets of 188 bytes each
		for i := range 7 {
			// Add sync byte at start of each packet
			testData[i*188] = 0x47
			// Fill rest with random data
			for j := 1; j < 188; j++ {
				testData[i*188+j] = byte(i + j)
			}
		}
		processedPacket := &value.Packet{Raw: testData[:188]} // Process first packet
		mockMPEGTS.EXPECT().ProcessPacket(gomock.Any()).Return(processedPacket, nil).AnyTimes()

		// Start service
		readyCh := make(chan bool)
		go sfs.Run(readyCh)
		<-readyCh

		// Send packet to stream buffer
		stream.Buffer <- testData

		// Wait for processing
		time.Sleep(100 * time.Millisecond)

		// Verify packet was forwarded
		select {
		case receivedPacket := <-dispatchCh:
			assert.Equal(t, processedPacket, receivedPacket)
		default:
			t.Fatal("Packet was not forwarded")
		}
	})

	t.Run("Run_HandlesProcessingError", func(t *testing.T) {
		t.Parallel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Running)
		dispatchCh := make(chan *value.Packet, 10)
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Setup mock expectations
		testData := []byte("test packet")
		processingError := errors.New("processing error")
		mockMPEGTS.EXPECT().ProcessPacket(testData).Return(nil, processingError).AnyTimes()

		// Start service
		readyCh := make(chan bool)
		go sfs.Run(readyCh)
		<-readyCh

		// Send packet to stream buffer
		stream.Buffer <- testData

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify no packet was forwarded
		select {
		case <-dispatchCh:
			t.Fatal("Packet was forwarded despite processing error")
		default:
			// Success - no packet forwarded
		}
	})

	t.Run("Run_DropsPacketsWhenStreamNotRunning", func(t *testing.T) {
		t.Parallel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Initializing)
		dispatchCh := make(chan *value.Packet, 10)
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Start service
		readyCh := make(chan bool)
		go sfs.Run(readyCh)
		<-readyCh

		// Send packet to stream buffer
		testData := []byte("test packet")
		stream.Buffer <- testData

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify no packet was forwarded
		select {
		case <-dispatchCh:
			t.Fatal("Packet was forwarded when stream was not running")
		default:
			// Success - no packet forwarded
		}
	})

	t.Run("Run_DropsPacketsWhenDispatchBufferFull", func(t *testing.T) {
		t.Parallel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Running)
		dispatchCh := make(chan *value.Packet, 1) // Small buffer
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Setup mock expectations
		testData := []byte("test packet")
		processedPacket := &value.Packet{Raw: testData}
		mockMPEGTS.EXPECT().ProcessPacket(testData).Return(processedPacket, nil).AnyTimes()

		// Start service
		readyCh := make(chan bool)
		go sfs.Run(readyCh)
		<-readyCh

		// Fill dispatch buffer
		dispatchCh <- &value.Packet{Raw: []byte("buffer filler")}

		// Send packet to stream buffer
		stream.Buffer <- testData

		// Wait for processing
		time.Sleep(50 * time.Millisecond)
	})

	t.Run("Run_HandlesBufferClosure", func(t *testing.T) {
		t.Parallel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Running)
		dispatchCh := make(chan *value.Packet, 10)
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Start service
		readyCh := make(chan bool)
		go sfs.Run(readyCh)
		<-readyCh

		// Close buffer
		close(stream.Buffer)

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify no packets were forwarded
		select {
		case <-dispatchCh:
			t.Fatal("Packet was forwarded after buffer closure")
		default:
			// Success - no packets forwarded
		}
	})

	t.Run("Close_SetsStreamStateAndClosesBuffer", func(t *testing.T) {
		t.Parallel()

		// Setup
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		stream := &entity.Stream{
			Buffer: make(chan value.UDPPacket, 10),
		}
		stream.SetState(value.Running)
		dispatchCh := make(chan *value.Packet, 10)
		mockMPEGTS := mock_interfaces.NewMockMPEGTSService(ctrl)

		// Create service
		sfs := services.NewStreamForwardingService(zerolog.Nop(), stream, mockMPEGTS, dispatchCh)

		// Close service
		sfs.Close()

		// Verify stream state
		assert.Equal(t, value.Removal, stream.State())

		// Verify buffer is closed
		select {
		case _, ok := <-stream.Buffer:
			assert.False(t, ok, "Buffer should be closed")
		default:
			t.Fatal("Buffer should be closed")
		}
	})
}
