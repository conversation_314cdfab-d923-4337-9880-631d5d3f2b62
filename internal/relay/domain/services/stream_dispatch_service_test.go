package services_test

import (
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
)

func TestStreamDispatchService(t *testing.T) { //nolint: cyclop, maintidx
	t.Parallel()

	t.Run("Run_DispatchesToClients", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)

		// Create service
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Create client with channel
		clientCh := make(chan value.TSPacket, 10)
		clientID := uuid.New()
		client := entity.Client{
			ID:       clientID,
			StreamCh: clientCh,
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}

		// Add client
		sds.AddClient(client)

		// Start service
		readyCh := make(chan bool)
		go sds.Run(readyCh)

		// Wait for service to be ready
		<-readyCh

		// Send packet to stream
		testData := value.TSPacket("test packet")
		packet := &value.Packet{
			Raw: testData,
		}
		streamCh <- packet

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify client received data
		select {
		case receivedData := <-clientCh:
			assert.Equal(t, testData, receivedData)
		default:
			t.Fatal("Client did not receive data")
		}
	})

	t.Run("Run_FiltersPackets", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)

		// Create service
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Create client with filter that blocks packets
		clientCh := make(chan value.TSPacket, 10)
		clientID := uuid.New()
		client := entity.Client{
			ID:       clientID,
			StreamCh: clientCh,
			Filter: value.StreamFilter{
				ProgramID: 1,
				// Block all packets
			},
		}

		// Add client
		sds.AddClient(client)

		// Start service
		readyCh := make(chan bool)
		go sds.Run(readyCh)

		// Wait for service to be ready
		<-readyCh

		// Send packet to stream
		testData := value.TSPacket("test packet")
		packet := &value.Packet{
			Raw: testData,
		}
		streamCh <- packet

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify client did not receive data (channel should be empty)
		select {
		case <-clientCh:
			t.Fatal("Client received data despite filter")
		default:
			// Success - no data received
		}
	})

	t.Run("ClientCount_ReturnsCorrectCount", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Initially should have 0 clients
		assert.Equal(t, 0, sds.ClientCount())

		// Add a client
		client1 := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}
		sds.AddClient(client1)
		assert.Equal(t, 1, sds.ClientCount())

		// Add another client
		client2 := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}
		sds.AddClient(client2)
		assert.Equal(t, 2, sds.ClientCount())
	})

	t.Run("RemoveClient_RemovesCorrectClient", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Add two clients
		client1ID := uuid.New()
		client1 := entity.Client{
			ID:       client1ID,
			StreamCh: make(chan value.TSPacket, 10),
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}
		sds.AddClient(client1)

		client2ID := uuid.New()
		client2 := entity.Client{
			ID:       client2ID,
			StreamCh: make(chan value.TSPacket, 10),
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}
		sds.AddClient(client2)

		// Should have 2 clients
		assert.Equal(t, 2, sds.ClientCount())

		// Remove client1
		sds.RemoveClient(client1ID)
		assert.Equal(t, 1, sds.ClientCount())

		// Remove client2
		sds.RemoveClient(client2ID)
		assert.Equal(t, 0, sds.ClientCount())
	})

	t.Run("RemoveClient_NonExistentClient_NoEffect", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Add a client
		client := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
			Filter: value.StreamFilter{
				ProgramID: 0,
				// Allow all packets
			},
		}
		sds.AddClient(client)
		assert.Equal(t, 1, sds.ClientCount())

		// Try to remove non-existent client
		sds.RemoveClient(uuid.New())

		// Count should still be 1
		assert.Equal(t, 1, sds.ClientCount())
	})

	t.Run("Run_DispatchesToMultipleClientsWithFilters", func(t *testing.T) {
		t.Parallel()

		// Setup
		streamCh := make(chan *value.Packet, 10)
		sds := services.NewStreamDispatchService(zerolog.Nop(), streamCh)

		// Client 1: Accepts all packets
		client1Ch := make(chan value.TSPacket, 10)
		client1ID := uuid.New()
		client1 := entity.Client{
			ID:       client1ID,
			StreamCh: client1Ch,
			Filter: value.StreamFilter{
				ProgramID: 0, // No filtering
			},
		}
		sds.AddClient(client1)

		// Client 2: Filters out ProgramID 1
		client2Ch := make(chan value.TSPacket, 10)
		client2ID := uuid.New()
		client2 := entity.Client{
			ID:       client2ID,
			StreamCh: client2Ch,
			Filter: value.StreamFilter{
				ProgramID: 1, // Only accept ProgramID 1
			},
		}
		sds.AddClient(client2)

		// Start service
		readyCh := make(chan bool)
		go sds.Run(readyCh)
		<-readyCh

		// Send packet 1: ProgramID 0 (should go to client1 only)
		testData1 := value.TSPacket("packet 1")
		packet1 := &value.Packet{
			Raw:       testData1,
			ProgramID: 0,
		}
		streamCh <- packet1

		// Send packet 2: ProgramID 1 (should go to both clients)
		testData2 := value.TSPacket("packet 2")
		packet2 := &value.Packet{
			Raw:       testData2,
			ProgramID: 1,
		}
		streamCh <- packet2

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify client1 received both packets
		select {
		case data := <-client1Ch:
			assert.Equal(t, testData1, data, "client1 should receive packet 1")
		default:
			t.Fatal("client1 did not receive packet 1")
		}
		select {
		case data := <-client1Ch:
			assert.Equal(t, testData2, data, "client1 should receive packet 2")
		default:
			t.Fatal("client1 did not receive packet 2")
		}

		// Verify client2 received only packet 2
		select {
		case data := <-client2Ch:
			assert.Equal(t, testData2, data, "client2 should receive packet 2")
		default:
			t.Fatal("client2 did not receive packet 2")
		}
		select {
		case <-client2Ch:
			t.Fatal("client2 received unexpected packet")
		default:
			// Success: client2 only got packet 2
		}
	})
}
