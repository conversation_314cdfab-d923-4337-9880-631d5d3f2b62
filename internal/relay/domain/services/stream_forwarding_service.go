package services

import (
	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
)

var _ interfaces.StreamForwardingService = &StreamForwardingService{}

type StreamForwardingService struct {
	log               zerolog.Logger
	stream            *entity.Stream
	mpegtsService     interfaces.MPEGTSService
	dispatchServiceCh chan *value.Packet
	stopCh            chan struct{}
}

func NewStreamForwardingService(
	logger zerolog.Logger,
	stream *entity.Stream,
	mpegtsService interfaces.MPEGTSService,
	dispatchServiceCh chan *value.Packet,
) *StreamForwardingService {
	return &StreamForwardingService{
		log: logger.With().
			Str("component", "relay/stream_forwarding_service").
			Logger(),
		stream:            stream,
		mpegtsService:     mpegtsService,
		dispatchServiceCh: dispatchServiceCh,
		stopCh:            make(chan struct{}),
	}
}

func (sfs *StreamForwardingService) Run(readyCh chan bool) {
	sfs.log.Info().
		Str("stream-address", sfs.stream.Address.String()).
		Msg("Starting packet forwarding for stream")
	defer sfs.log.Info().
		Str("stream-address", sfs.stream.Address.String()).
		Msg("Stopped packet forwarding for stream")

	readyCh <- true
	for {
		select {
		case udpPacket, ok := <-sfs.stream.Buffer:
			if !ok {
				sfs.log.Warn().
					Str("address", sfs.stream.Address.String()).
					Msg("Buffer closed for stream")
				return
			}

			if sfs.stream.State() != value.Running {
				sfs.log.Debug().
					Msg("Stream not running, dropping packet")
				continue
			}
			sfs.stream.UpdateStreamReceivingDuration()
			sfs.stream.Metrics.ReceivedUDPPackets.Add(1)

			tsPackets := udpPacket.ParseTSPackets()
			sfs.processTSPackets(tsPackets)

		case <-sfs.stopCh:
			return
		}
	}
}

func (sfs *StreamForwardingService) Close() {
	sfs.stream.SetState(value.Removal)
	close(sfs.stream.Buffer)
}

// processTSPackets processes each TS packet: metrics, MPEGTSService, dispatch, etc.
func (sfs *StreamForwardingService) processTSPackets(tsPackets []value.TSPacket) {
	var hasUDPDiscontinuity bool

	for _, tsPacket := range tsPackets {
		sfs.stream.UpdateReceivedTSPackets()
		packet, err := sfs.mpegtsService.ProcessPacket(tsPacket)
		if err != nil {
			sfs.log.Error().
				Err(err).
				Msg("failed processing packet")
			continue
		}

		if packet.DiscontinuityOccurred {
			hasUDPDiscontinuity = true
			sfs.stream.Metrics.Discontinuities.Add(1)
		}

		program, programFound := sfs.stream.Programs()[packet.ProgramID]
		if programFound {
			program.UpdateMetrics(packet)
		}

		select {
		case sfs.dispatchServiceCh <- packet:
		default:
			sfs.log.Warn().
				Msg("dropping packet due to dispatch service buffer being full")
		}
	}

	if hasUDPDiscontinuity {
		sfs.stream.Metrics.MissingUDPPackets.Add(1)
	}
}
