package services

import (
	"context"
	"io"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"github.com/rs/zerolog"
)

var _ interfaces.ClientWriterService = &ClientWriterService{}

type ClientWriterService struct {
	log    zerolog.Logger
	client entity.Client
	writer io.Writer
}

func NewClientWriterService(
	log zerolog.Logger,
	client entity.Client,
	writer io.Writer,
) *ClientWriterService {
	return &ClientWriterService{
		log:    log.With().Str("component", "relay/client_writer_service").<PERSON><PERSON>(),
		client: client,
		writer: writer,
	}
}

func (cws *ClientWriterService) Run(ctx context.Context) {
	for {
		select {
		case p := <-cws.client.StreamCh:
			_, err := cws.writer.Write(p)
			if err != nil {
				cws.log.Warn().
					Str("clientID", cws.client.ID.String()).
					Msg("write to client writer failed")
			}

		case <-ctx.Done():
			cws.log.Debug().
				Str("clientID", cws.client.ID.String()).
				Msg("client writer exiting")

			return
		}
	}
}
