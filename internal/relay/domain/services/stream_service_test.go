package services_test

import (
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/Comcast/gots/packet"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
)

func TestStreamService_StreamMetrics(t *testing.T) {
	t.<PERSON>llel()

	t.Run("StreamReceivedUDPPacketsTotal_Increments_OnEachPacket", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.Buffer <- makeUDPPacket(256, 0, false)
		stream.Buffer <- makeUDPPacket(256, 1, false)
		time.Sleep(50 * time.Millisecond)
		assert.Equal(t, int64(2), stream.Metrics.ReceivedUDPPackets.Load())
		assert.Equal(t, int64(2*packet.PacketSize), stream.Metrics.ReceivedBytes.Load())
		assert.Equal(t, int64(2), stream.Metrics.ReceivedTSPackets.Load())
	})

	t.Run("StreamReceivedTSPacketsTotal_MultipleTSPacketsInOneUDPPacket", func(t *testing.T) {
		t.Parallel()

		for numTSPackets := 1; numTSPackets <= 7; numTSPackets++ {
			ss, stream := newTestStreamServiceWithRealDeps(t)
			defer ss.Close()
			udpPacket := make(value.UDPPacket, numTSPackets*packet.PacketSize)
			for i := range numTSPackets {
				ts := makeUDPPacket(256, i, false)
				copy(udpPacket[i*packet.PacketSize:], ts)
			}
			stream.Buffer <- udpPacket
			time.Sleep(50 * time.Millisecond)
			assert.Equal(t, int64(1), stream.Metrics.ReceivedUDPPackets.Load())
			assert.Equal(t, int64(numTSPackets), stream.Metrics.ReceivedTSPackets.Load())
			assert.Equal(
				t,
				int64(numTSPackets*packet.PacketSize),
				stream.Metrics.ReceivedBytes.Load(),
			)
		}
	})

	t.Run("StreamReceiverCount_Updates_OnAddRemoveClient", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		client := entity.NewClient(value.StreamFilter{}, 10, false)
		ss.AddClient(client)
		assert.Equal(t, int64(1), stream.Metrics.Receivers.Load())
		ss.RemoveClient(client)
		assert.Equal(t, int64(0), stream.Metrics.Receivers.Load())
	})

	t.Run("StreamReceiverCount_Accurate_OnMultipleAddRemove", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		clients := []entity.Client{
			entity.NewClient(value.StreamFilter{}, 10, false),
			entity.NewClient(value.StreamFilter{}, 10, false),
			entity.NewClient(value.StreamFilter{}, 10, false),
		}
		// Add all clients
		for i, c := range clients {
			ss.AddClient(c)
			assert.Equal(
				t,
				int64(i+1),
				stream.Metrics.Receivers.Load(),
				"Receiver count should increment on add",
			)
		}
		// Remove all clients
		for i := len(clients) - 1; i >= 0; i-- {
			ss.RemoveClient(clients[i])
			assert.Equal(
				t,
				int64(i),
				stream.Metrics.Receivers.Load(),
				"Receiver count should decrement on remove",
			)
		}
		// Add a dummy client (should not increment)
		dummy := entity.NewClient(value.StreamFilter{}, 10, true)
		ss.AddClient(dummy)
		assert.Equal(
			t,
			int64(0),
			stream.Metrics.Receivers.Load(),
			"Dummy client should not increment count",
		)
		// Remove a client not present (should not panic or decrement below zero)
		ss.RemoveClient(clients[0])
		assert.Equal(
			t,
			int64(0),
			stream.Metrics.Receivers.Load(),
			"Removing non-existent client should not decrement count",
		)
	})

	t.Run("StreamDiscontinuitiesTotal_Increments_OnDiscontinuity", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.Buffer <- makeUDPPacket(256, 0, false)
		stream.Buffer <- makeUDPPacket(256, 2, false) // skip cc=1
		time.Sleep(50 * time.Millisecond)
		assert.GreaterOrEqual(t, stream.Metrics.Discontinuities.Load(), int64(1))
	})

	t.Run("StreamMissingUDPPacketsTotal_Increments_OnDiscontinuity", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.Buffer <- makeUDPPacket(256, 0, false)
		stream.Buffer <- makeUDPPacket(256, 2, false) // skip cc=1
		time.Sleep(50 * time.Millisecond)
		assert.GreaterOrEqual(t, stream.Metrics.MissingUDPPackets.Load(), int64(1))
	})

	t.Run("StreamPresentDuration_And_ReceivingDuration_Accumulate", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.Buffer <- makeUDPPacket(256, 0, false)
		time.Sleep(20 * time.Millisecond)
		stream.Buffer <- makeUDPPacket(256, 1, false)
		time.Sleep(50 * time.Millisecond)
		stream.Buffer <- makeUDPPacket(256, 2, false)
		time.Sleep(20 * time.Millisecond)

		assert.GreaterOrEqual(t, ss.Stream().Metrics.PresentDuration.Load(), int64(70))
		assert.GreaterOrEqual(t, ss.Stream().Metrics.ReceivingDuration.Load(), int64(70))
	})

	t.Run("StreamReceivingDuration_DoesNotAccumulate_AfterTimeout", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.Buffer <- makeUDPPacket(256, 0, false)
		time.Sleep(100 * time.Millisecond)
		stream.Buffer <- makeUDPPacket(256, 0, false)
		time.Sleep(1100 * time.Millisecond) // Wait more than the 1s timeout
		stream.Buffer <- makeUDPPacket(256, 1, false)
		time.Sleep(100 * time.Millisecond)
		// Present duration should be at least 1100ms
		assert.GreaterOrEqual(t, ss.Stream().Metrics.PresentDuration.Load(), int64(1100))
		// Receiving duration should be much less (should not include the 1100ms gap)
		assert.Less(t, ss.Stream().Metrics.ReceivingDuration.Load(), int64(200))
	})

	t.Run("NoPackets_ResultsInZeroMetrics", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		time.Sleep(20 * time.Millisecond)
		assert.Equal(t, int64(0), stream.Metrics.ReceivedUDPPackets.Load())
		assert.Equal(t, int64(0), stream.Metrics.ReceivedTSPackets.Load())
		assert.Equal(t, int64(0), stream.Metrics.ReceivedBytes.Load())
		assert.Equal(t, int64(0), stream.Metrics.Discontinuities.Load())
		assert.Equal(t, int64(0), stream.Metrics.MissingUDPPackets.Load())
	})

	t.Run("RapidStateChange_DoesNotCorruptMetrics", func(t *testing.T) {
		t.Parallel()

		ss, stream := newTestStreamServiceWithRealDeps(t)
		defer ss.Close()
		stream.SetState(value.Removal)
		stream.Buffer <- makeUDPPacket(256, 0, false)
		time.Sleep(20 * time.Millisecond)
		// Packet should be dropped, metrics should remain zero
		assert.Equal(t, int64(0), stream.Metrics.ReceivedUDPPackets.Load())
	})
}

func newTestStreamServiceWithRealDeps(t *testing.T) (*services.StreamService, *entity.Stream) {
	t.Helper()

	stream := entity.NewStream(value.MustParseAddress("*********", 5000), 10)
	client := entity.NewClient(value.StreamFilter{}, 1000, true)
	stream.SetPrograms(map[int]*entity.Program{
		0: entity.NewProgram(0, "Test Program", "Provider"),
	})

	mpegts := services.NewMPEGTSService(zerolog.Nop())
	dispatchCh := make(chan *value.Packet, 10)
	forwarding := services.NewStreamForwardingService(zerolog.Nop(), stream, mpegts, dispatchCh)
	dispatch := services.NewStreamDispatchService(zerolog.Nop(), dispatchCh)

	ss := services.NewStreamService(zerolog.Nop(), mpegts, forwarding, dispatch, stream)
	ss.AddClient(client)
	return ss, stream
}

func makeUDPPacket(pid int, cc int, discontinuity bool) value.UDPPacket { //nolint: unparam
	pkt := make(value.UDPPacket, packet.PacketSize)
	pkt[0] = 0x47
	pkt[1] = byte((pid >> 8) & 0x1F)
	pkt[2] = byte(pid & 0xFF)
	pkt[3] = byte(0x10 | (cc & 0x0F))
	if discontinuity {
		pkt[3] |= 0x20 // set adaptation field control
		pkt[4] = 1     // adaptation field length
		pkt[5] = 0x80  // discontinuity indicator
	}
	return pkt
}
