package services

import (
	"sync/atomic"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/rs/zerolog"
)

var _ interfaces.StreamService = &StreamService{}

type StreamService struct {
	log                     zerolog.Logger
	mpegtsService           interfaces.MPEGTSService
	streamForwardingService interfaces.StreamForwardingService
	streamDispatchService   interfaces.StreamDispatchService
	stream                  *entity.Stream
}

func NewStreamService(
	log zerolog.Logger,
	mpegtsService interfaces.MPEGTSService,
	streamForwardingService interfaces.StreamForwardingService,
	streamDispatchService interfaces.StreamDispatchService,
	stream *entity.Stream,
) *StreamService {
	sfsReadyCh := make(chan bool, 1)
	sdsReadyCh := make(chan bool, 1)

	go streamForwardingService.Run(sfsReadyCh)
	go streamDispatchService.Run(sdsReadyCh)

	<-sfsReadyCh
	<-sdsReadyCh

	return &StreamService{
		log: log.With().
			Str("component", "relay/domain_stream_service").
			Logger(),
		mpegtsService:           mpegtsService,
		streamForwardingService: streamForwardingService,
		streamDispatchService:   streamDispatchService,
		stream:                  stream,
	}
}

// AddClient implements interfaces.StreamService.
func (ss *StreamService) AddClient(client entity.Client) {
	ss.log.Trace().
		Str("clientID", client.ID.String()).
		Msg("adding client to stream")

	if !client.IsDummy {
		ss.stream.Metrics.Receivers.Add(1)
		ss.incrementProgramReceiverCount(client.Filter.ProgramID)
	}

	ss.streamDispatchService.AddClient(client)
	ss.stream.SetState(value.Running)
}

// RemoveClient implements interfaces.StreamService.
func (ss *StreamService) RemoveClient(client entity.Client) {
	ss.log.Trace().
		Str("clientID", client.ID.String()).
		Msg("removing client from stream")

	if !client.IsDummy {
		ss.atomicDecrementIfPositive(&ss.stream.Metrics.Receivers)
		ss.decrementProgramReceiverCount(client.Filter.ProgramID)
	}

	ss.streamDispatchService.RemoveClient(client.ID)
	if ss.streamDispatchService.ClientCount() == 0 {
		ss.log.Trace().
			Str("stream-address", ss.stream.Address.String()).
			Msg("removing stream forwarding service as there are no clients left")
		ss.stream.SetState(value.Removal)
	}
}

// ClientCount implements interfaces.StreamService.
func (ss *StreamService) ClientCount() int {
	return ss.streamDispatchService.ClientCount()
}

// Stream implements interfaces.StreamService.
func (ss *StreamService) Stream() *entity.Stream {
	newPrograms := ss.mpegtsService.ProgramInfo()

	// Preserve metrics for existing programs.
	for progNum, newProg := range newPrograms {
		if existingProg, exists := ss.stream.Programs()[progNum]; exists {
			newProg.Metrics = existingProg.Metrics

			// Preserve metrics for existing tracks.
			for pid, track := range newProg.Tracks() {
				if existingTrack, exists := existingProg.Tracks()[pid]; exists {
					track.Metrics = existingTrack.Metrics
				}
			}
		}
	}

	ss.stream.SetPrograms(newPrograms)
	ss.stream.UpdateStreamPresentDuration()
	return ss.stream
}

// Close implements interfaces.StreamService.
func (ss *StreamService) Close() {
	ss.streamForwardingService.Close()
}

func (ss *StreamService) incrementProgramReceiverCount(programID int) {
	if programID == value.DefaultProgramID {
		for _, program := range ss.stream.Programs() {
			program.Metrics.Receivers.Add(1)
		}
	}

	if program, exists := ss.Stream().Programs()[programID]; exists {
		program.Metrics.Receivers.Add(1)
	}
}

func (ss *StreamService) decrementProgramReceiverCount(programID int) {
	if programID == value.DefaultProgramID {
		for _, program := range ss.stream.Programs() {
			ss.atomicDecrementIfPositive(&program.Metrics.Receivers)
		}
	}

	if program, exists := ss.Stream().Programs()[programID]; exists {
		ss.atomicDecrementIfPositive(&program.Metrics.Receivers)
	}
}

// atomicDecrementIfPositive atomically decrements the value only if it is > 0.
func (ss *StreamService) atomicDecrementIfPositive(counter *atomic.Int64) {
	for {
		old := counter.Load()
		if old <= 0 {
			return
		}

		if counter.CompareAndSwap(old, old-1) {
			return
		}
	}
}
