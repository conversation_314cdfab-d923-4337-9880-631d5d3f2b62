package services //nolint: testpackage

import (
	"testing"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/Comcast/gots/packet"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMPEGTSService_ProcessPacket(t *testing.T) {
	t.Parallel()

	logger := zerolog.New(zerolog.NewTestWriter(t))
	service := NewMPEGTSService(logger)

	// Create a null packet (PID 0x1FFF)
	nullPacket := make(value.TSPacket, packet.PacketSize)
	nullPacket[1] = 0x1F
	nullPacket[2] = 0xFF

	p, err := service.ProcessPacket(nullPacket)
	require.NoError(t, err)
	assert.True(t, p.IsMeta)
	assert.Equal(t, nullPacket, p.Raw)

	// Create a PAT packet (PID 0x0000)
	patPacket := make(value.TSPacket, packet.PacketSize)
	patPacket[1] = 0x00
	patPacket[2] = 0x00
	// Add PAT table data
	patData := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x00, 0xC1, 0x00, 0x00, // PAT data
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(patPacket, patData)

	p, err = service.ProcessPacket(patPacket)
	require.NoError(t, err)
	assert.True(t, p.IsMeta)
	assert.Equal(t, patPacket, p.Raw)

	// Create a PMT packet (PID 0x0100)
	pmtPacket := make(value.TSPacket, packet.PacketSize)
	pmtPacket[1] = 0x01
	pmtPacket[2] = 0x00
	// Add PMT table data
	pmtData := value.TSPacket{
		0x47, 0x50, 0x01, 0x10, // TS header
		0x00, 0x02, 0xB0, 0x17, // PMT header
		0x00, 0x01, 0xC1, 0x00, 0x00, // PMT data
		0xE1, 0x00, // PCR PID
		0xF0, 0x00, // Program info length
		0x1B, 0xE1, 0x00, // Video stream
		0x0F, 0xE1, 0x01, // Audio stream
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pmtPacket, pmtData)

	p, err = service.ProcessPacket(pmtPacket)
	require.NoError(t, err)
	assert.True(t, p.IsMeta)
	assert.Equal(t, pmtPacket, p.Raw)

	// Create a video packet (PID 0x0100)
	videoPacket := make(value.TSPacket, packet.PacketSize)
	videoPacket[1] = 0x01
	videoPacket[2] = 0x00
	// Add video data
	for i := 4; i < packet.PacketSize; i++ {
		videoPacket[i] = 0xFF
	}

	p, err = service.ProcessPacket(videoPacket)
	require.NoError(t, err)
	assert.False(t, p.IsMeta)
	assert.Equal(t, videoPacket, p.Raw)
	assert.Equal(t, 1, p.ProgramID)

	invalidPacket := make(value.TSPacket, packet.PacketSize-1)
	p, err = service.ProcessPacket(invalidPacket)
	require.Error(t, err)
	assert.Nil(t, p)
	assert.ErrorIs(t, err, ErrPacketSize)
}

func TestMPEGTSService_StateManagement(t *testing.T) {
	t.Parallel()

	logger := zerolog.New(zerolog.NewTestWriter(t))
	service := NewMPEGTSService(logger)

	// First PAT
	pat1 := make(value.TSPacket, packet.PacketSize)
	pat1[1] = 0x00
	pat1[2] = 0x00
	patData1 := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x00, 0xC1, 0x00, 0x00, // PAT data
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pat1, patData1)

	_, err := service.ProcessPacket(pat1)
	require.NoError(t, err)

	// Setup PMT
	pmtPacket := make(value.TSPacket, packet.PacketSize)
	pmtPacket[1] = 0x01
	pmtPacket[2] = 0x00
	pmtData := value.TSPacket{
		0x47, 0x50, 0x01, 0x10, // TS header
		0x00, 0x02, 0xB0, 0x17, // PMT header
		0x00, 0x01, 0xC1, 0x00, 0x00, // PMT data
		0xE1, 0x00, // PCR PID
		0xF0, 0x00, // Program info length
		0x1B, 0xE1, 0x00, // Video stream
		0x0F, 0xE1, 0x01, // Audio stream
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pmtPacket, pmtData)

	_, err = service.ProcessPacket(pmtPacket)
	require.NoError(t, err)

	// Second PAT with different version
	pat2 := make(value.TSPacket, packet.PacketSize)
	pat2[1] = 0x00
	pat2[2] = 0x00
	patData2 := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x01, 0xC2, 0x00, 0x00, // PAT data (version 1)
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pat2, patData2)

	_, err = service.ProcessPacket(pat2)
	require.NoError(t, err)

	// Verify state was reset
	assert.NotNil(t, service.pat)
	assert.Equal(t, uint8(1), service.patVersion)
	assert.Empty(t, service.pmts)      // PMTs should be cleared
	assert.Empty(t, service.pidMap)    // PID map should be cleared
	assert.NotEmpty(t, service.pmtAcc) // PMT accumulators should be cleared
}

func TestMPEGTSService_PMTAccumulation(t *testing.T) {
	t.Parallel()

	logger := zerolog.New(zerolog.NewTestWriter(t))
	service := NewMPEGTSService(logger)

	// First send PAT to set up PMT PID
	patPacket := make(value.TSPacket, packet.PacketSize)
	patPacket[1] = 0x00
	patPacket[2] = 0x00
	patData := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x00, 0xC1, 0x00, 0x00, // PAT data
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(patPacket, patData)

	_, err := service.ProcessPacket(patPacket)
	require.NoError(t, err)

	// Then send PMT
	pmtPacket := make(value.TSPacket, packet.PacketSize)
	pmtPacket[1] = 0x01
	pmtPacket[2] = 0x00
	pmtData := value.TSPacket{
		0x47, 0x50, 0x01, 0x10, // TS header
		0x00, 0x02, 0xB0, 0x17, // PMT header
		0x00, 0x02, 0xC2, 0x00, 0x00, // PMT data
		0xE1, 0x00, // PCR PID
		0xF0, 0x00, // Program info length
		0x1B, 0xE1, 0x00, // Video stream
		0x0F, 0xE1, 0x01, // Audio stream
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pmtPacket, pmtData)

	_, err = service.ProcessPacket(pmtPacket)
	require.NoError(t, err)

	// Verify PMT was stored
	pmt, exists := service.pmts[1]
	assert.True(t, exists)
	assert.Equal(t, uint8(1), pmt.VersionNumber())
}

func TestMPEGTSService_PIDMapping(t *testing.T) {
	t.Parallel()

	logger := zerolog.New(zerolog.NewTestWriter(t))
	service := NewMPEGTSService(logger)

	// Setup PAT and PMT
	patPacket := make(value.TSPacket, packet.PacketSize)
	patPacket[1] = 0x00
	patPacket[2] = 0x00
	patData := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x00, 0xC1, 0x00, 0x00, // PAT data
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(patPacket, patData)

	_, err := service.ProcessPacket(patPacket)
	require.NoError(t, err)

	pmtPacket := make(value.TSPacket, packet.PacketSize)
	pmtPacket[1] = 0x01
	pmtPacket[2] = 0x00
	pmtData := value.TSPacket{
		0x47, 0x50, 0x01, 0x10, // TS header
		0x00, 0x02, 0xB0, 0x18, // PMT header
		0x00, 0x01, 0xC1, 0x00, 0x00, // PMT data
		0xE1, 0x00, // PCR PID
		0x00, 0x00, // Program info length (0 bytes)
		0x1B, 0x01, 0x00, 0x00, 0x00, // Video stream (PID 0x100)
		0x0F, 0x01, 0x01, 0x00, 0x00, // Audio stream (PID 0x101)
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(pmtPacket, pmtData)

	_, err = service.ProcessPacket(pmtPacket)
	require.NoError(t, err)

	// Verify PID mapping
	assert.Equal(t, 1, service.pidMap[0x100]) // Video PID maps to program 1
	assert.Equal(t, 1, service.pidMap[0x101]) // Audio PID maps to program 1
}

func TestMPEGTSService_ErrorHandling(t *testing.T) {
	t.Parallel()

	logger := zerolog.New(zerolog.NewTestWriter(t))
	service := NewMPEGTSService(logger)

	invalidPacket := make(value.TSPacket, packet.PacketSize-1)
	_, err := service.ProcessPacket(invalidPacket)
	require.Error(t, err)
	require.ErrorIs(t, err, ErrPacketSize)

	// Setup PAT first
	patPacket := make(value.TSPacket, packet.PacketSize)
	patPacket[1] = 0x00
	patPacket[2] = 0x00
	patData := value.TSPacket{
		0x47, 0x40, 0x00, 0x10, // TS header
		0x00, 0x00, 0xB0, 0x0D, // PAT header
		0x00, 0x00, 0xC1, 0x00, 0x00, // PAT data
		0x00, 0x01, 0xF0, 0x01, // Program 1 -> PMT PID 0x100
		0x2E, 0x70, 0x19, 0x7F, // CRC32
	}
	copy(patPacket, patData)

	_, err = service.ProcessPacket(patPacket)
	require.NoError(t, err)

	// Send invalid PMT
	invalidPMT := make(value.TSPacket, packet.PacketSize)
	invalidPMT[1] = 0x01
	invalidPMT[2] = 0x00
	// Invalid PMT data (too short)
	invalidPMTData := value.TSPacket{
		0x47, 0x50, 0x01, 0x10, // TS header
		0x00, 0x02, 0xB0, 0x05, // PMT header (too short)
		0x00, 0x01, 0xC1, 0x00, // Invalid PMT data
	}
	copy(invalidPMT, invalidPMTData)

	_, err = service.ProcessPacket(invalidPMT)
	assert.NoError(t, err) // Should not error, just log and reset accumulator
}
