package services

import (
	"errors"
	"fmt"
	"strings"
	"sync"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/Comcast/gots"
	"github.com/Comcast/gots/packet"
	"github.com/Comcast/gots/psi"
	"github.com/rs/zerolog"
)

var (
	ErrPacketSize     = errors.New("packet size too small")
	ErrVersionExtract = errors.New("failed to extract PAT version")
	ErrSDTParse       = errors.New("failed to parse SDT")
	ErrSDTVersion     = errors.New("SDT version not changed")
)

const (
	NullPacketPid    = 8191
	PATVersionMask   = uint8(0x1F)
	SDTPid           = 0x0011
	SDTHeaderLen     = 11
	SDTServiceIDLen  = 5
	SDTDescriptorTag = 0x48
	MaxMetaPid       = 0x001F // Maximum PID for metadata packets.
)

var _ interfaces.MPEGTSService = &MPEGTSService{}

type MPEGTSService struct {
	log                    zerolog.Logger
	pat                    psi.PAT // Program Association Table.
	patAcc                 packet.Accumulator
	patVersion             uint8
	mu                     sync.RWMutex
	pmts                   map[int]psi.PMT // Program Map Tables by program number.
	pidMap                 map[int]int     // PID to program number (for filtering).
	pidToContinuityCounter map[int]int
	pmtAcc                 map[int]packet.Accumulator // Accumulators for each PMT PID.
	sdt                    value.SDT                  // Service Description Table.
	sdtAcc                 packet.Accumulator
	pidToElementaryStream  map[int]psi.PmtElementaryStream // PID to elementary stream  mapping for fast lookups.
}

func NewMPEGTSService(logger zerolog.Logger) *MPEGTSService {
	return &MPEGTSService{
		log:                    logger.With().Str("component", "relay/mpegts_service").Logger(),
		pmts:                   make(map[int]psi.PMT),
		pidMap:                 make(map[int]int),
		pidToContinuityCounter: make(map[int]int),
		pmtAcc:                 make(map[int]packet.Accumulator),
		sdtAcc:                 packet.NewAccumulator(psi.PmtAccumulatorDoneFunc),
		patAcc:                 packet.NewAccumulator(psi.PmtAccumulatorDoneFunc),
		pidToElementaryStream:  make(map[int]psi.PmtElementaryStream),
	}
}

// ProcessPacket processes an MPEG-TS packet, updating internal tables and returning packet metadata.
func (mts *MPEGTSService) ProcessPacket(rawPacket value.TSPacket) (p *value.Packet, err error) {
	mts.mu.Lock()
	defer mts.mu.Unlock()
	mtsPacket, err := mts.convertToPacket(rawPacket)
	if err != nil {
		err = fmt.Errorf("failed to convert PAT packet: %w", err)
		return
	}

	mts.fillTables(mtsPacket)

	p = &value.Packet{
		Raw: rawPacket,
		PID: packet.Pid(mtsPacket),
	}
	pid := packet.Pid(mtsPacket)

	if pid <= MaxMetaPid || pid == NullPacketPid || mts.isPMTPID(pid) {
		p.IsMeta = true
	} else {
		p.DiscontinuityOccurred = mts.validateFuturePacketDiscontinuity(mtsPacket)
	}

	progNum, ok := mts.pidMap[pid]
	if ok {
		p.ProgramID = progNum
	}

	return
}

// ProgramInfo returns a list of programs currently available in the MPEG-TS stream.
// It combines information from PAT, PMT and SDT tables.
// PAT for program numbers, PMT for program streams and their PIDs, SDT for program titles and provider.
func (mts *MPEGTSService) ProgramInfo() map[int]*entity.Program {
	mts.mu.RLock()
	defer mts.mu.RUnlock()
	if mts.pat == nil {
		return map[int]*entity.Program{}
	}

	programs := make(map[int]*entity.Program, len(mts.pmts))

	// Iterate through all programs in PAT.
	for progNum := range mts.pat.ProgramMap() {
		pmt, hasPMT := mts.pmts[progNum]
		if !hasPMT {
			continue // Skip programs without PMT.
		}

		var title string
		var provider string
		serviceInfo, hasService := mts.sdt.Services[progNum]
		if hasService {
			title = serviceInfo.Name
			provider = serviceInfo.ProviderName
		}
		prog := entity.NewProgram(progNum, title, provider)

		// Add tracks from PMT.
		tracks := make(map[int]*entity.Track, len(pmt.ElementaryStreams()))
		for _, es := range pmt.ElementaryStreams() {
			track := entity.NewTrack(
				es.ElementaryPid(),
				es.StreamType(),
				mts.buildTrackDescription(es.Descriptors(), es.StreamTypeDescription()),
			)
			tracks[es.ElementaryPid()] = track
		}
		prog.SetTracks(tracks)
		programs[progNum] = prog
	}

	return programs
}

// determines if discontinuities should be ignored for a given stream type.
func (mts *MPEGTSService) shouldIgnoreDiscontinuities(pid int) bool {
	es, exists := mts.pidToElementaryStream[pid]

	if !exists {
		return false
	}

	return es.StreamType() == psi.PmtStreamTypePrivateContent
}

func (mts *MPEGTSService) validateFuturePacketDiscontinuity(
	mtsPacket *packet.Packet,
) bool {
	pid := packet.Pid(mtsPacket)
	cc, ok := mts.pidToContinuityCounter[pid]
	mts.pidToContinuityCounter[pid] = mtsPacket.ContinuityCounter()

	// Check if we should ignore discontinuities for this PID's stream type
	if mts.shouldIgnoreDiscontinuities(pid) {
		return false
	}

	// If PUSI, do not report a discontinuity, as per MPEG-TS specification.
	if mtsPacket.PayloadUnitStartIndicator() {
		return false
	}
	var disOk bool
	af, err := mtsPacket.AdaptationField()
	if err == nil {
		disOk, err = af.Discontinuity()
		if err != nil || disOk {
			return false
		}
	}

	if ok && (cc+1)%16 == mtsPacket.ContinuityCounter() { // continuity counter ranges from 0 to 15
		return false
	}
	progNum := mts.pidMap[pid]
	mts.log.Trace().
		Int("pid", pid).
		Int("cc_prev", cc).
		Int("cc_new", mtsPacket.ContinuityCounter()).
		Bool("pusi", mtsPacket.PayloadUnitStartIndicator()).
		Bool("disOk", disOk).
		Int("program", progNum).
		Bool("has_adaptation", err == nil).
		Msg("Discontinuity detected")
	return true
}

func (mts *MPEGTSService) fillTables(mtsPacket *packet.Packet) {
	if packet.IsPat(mtsPacket) {
		mts.fillPAT(mtsPacket)
		return
	}
	pid := packet.Pid(mtsPacket)
	if mts.isPMTPID(pid) {
		mts.fillPMT(mtsPacket)
		return
	}

	if pid == SDTPid {
		mts.fillSDT(mtsPacket)
		return
	}
}

func (mts *MPEGTSService) fillPAT(pkt *packet.Packet) {
	_, err := mts.patAcc.WritePacket(pkt)
	if errors.Is(err, gots.ErrAccumulatorDone) {
		newPat, err := psi.NewPAT(mts.patAcc.Bytes())
		if err != nil {
			mts.log.Error().
				Err(err).
				Msg("failed to parse PAT")
			return
		}

		newVersion, err := version(pkt)
		if err != nil {
			mts.log.Error().
				Err(err).
				Msg("failed to extract PAT version")
			return
		}

		// Reset state if pat table changed.
		mts.updateState(newPat, newVersion)

		// Initialize accumulators for new PMT PIDs.
		for _, pmtPID := range newPat.ProgramMap() {
			_, exists := mts.pmtAcc[pmtPID]
			if !exists {
				mts.pmtAcc[pmtPID] = packet.NewAccumulator(psi.PmtAccumulatorDoneFunc)
			}
		}
	} else if err != nil {
		mts.log.Debug().
			Err(err).
			Msg("PAT accumulator error, resetting")
	}
}

// fillPMT processes a PMT packet for a given PID.
func (mts *MPEGTSService) fillPMT(pkt *packet.Packet) {
	pid := packet.Pid(pkt)
	if mts.pat == nil {
		mts.log.Debug().
			Int("pid", pid).
			Msg("PAT not filled yet, skipping PMT")
		return
	}

	acc, exists := mts.pmtAcc[pid]
	if !exists {
		mts.log.Warn().
			Int("pid", pid).
			Msg("No accumulator for PMT PID, skipping")
		return
	}

	_, err := acc.WritePacket(pkt)
	// Psi tables can span across multiple packets using gots accumulator.
	// function returns ErrAccumulatorDone when the table is complete.
	if errors.Is(err, gots.ErrAccumulatorDone) {
		mts.handleCompletedPMT(acc, pid)
	} else if err != nil {
		mts.log.Debug().
			Err(err).
			Int("pid", pid).
			Msg("PMT accumulator error, resetting")
	}
}

func (mts *MPEGTSService) fillSDT(pkt *packet.Packet) {
	_, err := mts.sdtAcc.WritePacket(pkt)
	if errors.Is(err, gots.ErrAccumulatorDone) {
		err = mts.handleCompletedSDT()
		if errors.Is(err, ErrSDTVersion) {
			mts.log.Debug().
				Err(err).
				Msg("SDT version unchanged, skipping update")
			return
		}
		if err != nil {
			mts.log.Error().
				Err(err).
				Msg("failed to parse accumulated SDT, resetting")
			mts.sdtAcc.Reset()
		}
	} else if err != nil {
		mts.log.Debug().
			Err(err).
			Msg("SDT accumulator error, resetting")
	}
}

func (mts *MPEGTSService) updateState(newPat psi.PAT, newVersion uint8) {
	if mts.pat == nil {
		mts.pat = newPat
		mts.patVersion = newVersion
		return // First PAT, no comparison needed
	}

	if mts.pat.NumPrograms() != newPat.NumPrograms() || mts.patVersion != newVersion {
		mts.log.Debug().
			Int("old_programs", mts.pat.NumPrograms()).
			Int("new_programs", newPat.NumPrograms()).
			Uint8("old_version", mts.patVersion).
			Uint8("new_version", newVersion).
			Msg("PAT changed, resetting tables")
		mts.pmts = make(map[int]psi.PMT)
		mts.pidMap = make(map[int]int)
		mts.pidToElementaryStream = make(map[int]psi.PmtElementaryStream)
		mts.pmtAcc = make(map[int]packet.Accumulator)
		mts.pat = newPat
		mts.patAcc.Reset()
		mts.patVersion = newVersion
		mts.sdtAcc.Reset()
		mts.sdt = value.SDT{}
	}
}

func (mts *MPEGTSService) handleCompletedPMT(acc packet.Accumulator, pid int) {
	payload := acc.Bytes()
	pmt, err := psi.NewPMT(payload)
	if err != nil {
		mts.log.Error().
			Err(err).
			Int("pid", pid).
			Int("len", len(payload)).
			Msg("failed to parse accumulated PMT, resetting")
		acc.Reset()
		return
	}

	for progNum, pmtPID := range mts.pat.ProgramMap() {
		if pmtPID == pid {
			if existingPMT, exists := mts.pmts[progNum]; exists &&
				existingPMT.VersionNumber() == pmt.VersionNumber() {
				return
			}
			mts.pmts[progNum] = pmt
			mts.updatePIDMap(progNum, pmt)
			mts.log.Debug().
				Int("program", progNum).
				Int("pid", pmtPID).
				Msg("Updated PMT")
			return
		}
	}
}

// handleCompletedSDT parses an SDT section from a payload into a valueobjects.SDT.
// Mpegts specification: https://ocw.unican.es/pluginfile.php/2825/course/section/2777/iso13818-1.pdf
//
//nolint:mnd,gocognit,cyclop // Using magic numbers from MPEG-TS specification.
func (mts *MPEGTSService) handleCompletedSDT() error {
	payload := mts.sdtAcc.Bytes()
	if len(payload) < SDTHeaderLen {
		return ErrSDTParse
	}
	// Calculate start of section data after pointer_field.
	// pointer_field (first byte) indicates bytes to skip before section starts.
	start := 1 + int(payload[0])
	if len(payload) < start+8 {
		return fmt.Errorf("%w: payload too short after pointer_field", ErrSDTParse)
	}
	sectionBytes := payload[start:]
	// Verify table_id is valid for SDT (0x42 for actual, 0x46 for other network).
	if sectionBytes[0] != 0x42 && sectionBytes[0] != 0x46 {
		return fmt.Errorf("%w: invalid table_id 0x%02x", ErrSDTParse, sectionBytes[0])
	}
	// Verify payload contains full section length plus 3 bytes for header.
	sectionLength := int(sectionBytes[1]&0x0F)<<8 | int(sectionBytes[2])
	if len(sectionBytes) < sectionLength+3 {
		return fmt.Errorf("%w: payload too short for section_length %d", ErrSDTParse, sectionLength)
	}

	version := (sectionBytes[5] >> 1) & 0x1F

	if mts.sdt.Version == version {
		mts.log.Trace().Uint8("version", version).Msg("SDT version unchanged, skipping update")
		return ErrSDTVersion
	}

	offset := SDTHeaderLen
	services := make(map[int]value.ServiceInfo)
	// Parse service entries until reaching section length (minus 4 bytes for CRC).
	for offset < sectionLength-4 {
		// Ensure enough bytes remain for service ID and descriptor length.
		if offset+SDTServiceIDLen > len(sectionBytes) {
			return fmt.Errorf("%w: incomplete service data at offset %d", ErrSDTParse, offset)
		}
		// Extract 16-bit program ID from two bytes.
		programID := int(sectionBytes[offset])<<8 | int(sectionBytes[offset+1])
		offset += 3 // Skip service_id (2) and reserved/EIT flags (1).
		// Get descriptors length (12-bit field).
		descriptorsLength := int(sectionBytes[offset]&0x0F)<<8 | int(sectionBytes[offset+1])
		offset += 2 // Move past descriptors length field.
		// Calculate end of descriptors for this service.
		end := offset + descriptorsLength
		if end > len(sectionBytes) {
			return fmt.Errorf("%w: descriptors overflow at offset %d", ErrSDTParse, offset)
		}

		for offset < end && offset+2 <= len(sectionBytes) {
			tag := sectionBytes[offset]
			length := int(sectionBytes[offset+1])
			offset += 2
			if offset+length > end {
				return fmt.Errorf("%w: descriptor overflow at offset %d", ErrSDTParse, offset)
			}
			// Handle service descriptor (tag 0x48).
			if tag == SDTDescriptorTag {
				if length < 3 {
					return fmt.Errorf("%w: service descriptor too short", ErrSDTParse)
				}
				// Skip service_type (1 byte), get provider name length.
				providerLength := int(sectionBytes[offset+1])
				if offset+2+providerLength > end {
					return fmt.Errorf("%w: provider name overflow", ErrSDTParse)
				}
				// Extract provider name
				provider := string(sectionBytes[offset+2 : offset+2+providerLength])
				// Calculate position of service name.
				nameLengthOffset := offset + 2 + providerLength
				if nameLengthOffset+1 > end {
					return fmt.Errorf("%w: name length out of bounds", ErrSDTParse)
				}
				// Get service name length and calculate end position.
				nameLength := int(sectionBytes[nameLengthOffset])
				nameEnd := nameLengthOffset + 1 + nameLength
				if nameEnd > end {
					return fmt.Errorf(
						"%w: service name overflow at offset %d",
						ErrSDTParse,
						nameLengthOffset,
					)
				}
				// Extract service name.
				name := string(sectionBytes[nameLengthOffset+1 : nameEnd])
				services[programID] = value.ServiceInfo{
					Name:         name,
					ProviderName: provider,
				}
			}
			offset += length // Move to next descriptor.
		}
	}
	mts.sdt = value.NewSDT(services, version)
	return nil
}

func version(pkt *packet.Packet) (uint8, error) {
	payload, err := packet.Payload(pkt)
	if err != nil || len(payload) < 7 {
		return 0, ErrVersionExtract
	}
	// PAT version is in bits 5-1 of byte 6 in the PAT payload
	// Byte 0: Table ID
	// Byte 1-2: Section syntax indicator and section length
	// Byte 3-4: Transport stream ID
	// Byte 5: Reserved bits
	// Byte 6: Version number (bits 5-1) and current/next indicator (bit 0)
	return (payload[6] >> 1) & PATVersionMask, nil
}

// isPMTPID checks if a PID is a PMT PID.
func (mts *MPEGTSService) isPMTPID(pid int) bool {
	if mts.pat == nil {
		return false
	}
	for _, pmtPID := range mts.pat.ProgramMap() {
		if pmtPID == pid {
			return true
		}
	}
	return false
}

// updatePIDMap builds a PID-to-program mapping for fast filtering.
func (mts *MPEGTSService) updatePIDMap(programNum int, pmt psi.PMT) {
	for _, es := range pmt.ElementaryStreams() {
		pid := es.ElementaryPid()
		mts.pidMap[pid] = programNum
		mts.pidToElementaryStream[pid] = es
	}
	mts.log.Trace().
		Int("program_num", programNum).
		Int("pid_count", len(mts.pidMap)).
		Msg("Updated PID map")
}

func (mts *MPEGTSService) convertToPacket(tsPacket []byte) (*packet.Packet, error) {
	if len(tsPacket) < packet.PacketSize {
		return nil, ErrPacketSize
	}
	return (*packet.Packet)(tsPacket[:packet.PacketSize]), nil
}

// buildTrackDescription creates a human-readable description from PMT descriptors.
//
//nolint:cyclop
func (mts *MPEGTSService) buildTrackDescription(
	descs []psi.PmtDescriptor,
	streamTypeDescription string,
) string {
	var parts []string
	var lang, features []string
	var bitrate string

	for _, desc := range descs {
		if desc.IsIso639LanguageDescriptor() {
			code := desc.DecodeIso639LanguageCode()
			if code != "" {
				lang = append(lang, code)
			}
			typeByte := desc.DecodeIso639AudioType()
			if typeByte != 0 {
				lang = append(lang, fmt.Sprintf("AudioType: 0x%x", typeByte))
			}
		}
		if desc.IsMaximumBitrateDescriptor() {
			br := desc.DecodeMaximumBitRate()
			bitrate = fmt.Sprint("Maximum bitrate: ", br)
		}
		if desc.IsDolbyATMOS() {
			features = append(features, "Dolby ATMOS")
		}
		if desc.IsDolbyVision() {
			features = append(features, "Dolby Vision")
		}
		if desc.IsIFrameProfile() {
			features = append(features, "I-frame only")
		}
		if desc.IsTTMLSubtitlingDescriptor() {
			langCode := desc.DecodeTTMLIso639LanguageCode()
			if langCode != "" {
				features = append(features, fmt.Sprintf("TTML subtitles (%s)", langCode))
			} else {
				features = append(features, "TTML subtitles")
			}
		}
	}

	if streamTypeDescription != "" {
		parts = append(parts, streamTypeDescription)
	}
	if len(lang) > 0 {
		parts = append(parts, "Language: "+strings.Join(lang, ", "))
	}
	if bitrate != "" {
		parts = append(parts, bitrate)
	}
	if len(features) > 0 {
		parts = append(parts, "Features: "+strings.Join(features, ", "))
	}

	return strings.Join(parts, ", ")
}
