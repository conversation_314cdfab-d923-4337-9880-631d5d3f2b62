package services_test

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/services"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
)

func TestClientWriterService(t *testing.T) {
	t.<PERSON>llel()

	t.Run("Run_WritesToWriter", func(t *testing.T) {
		t.Parallel()

		// Setup
		client := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
		}
		mockWriter := &mockWriter{
			writtenData: make([][]byte, 0),
		}

		cws := services.NewClientWriterService(zerolog.Nop(), client, mockWriter)

		ctx, cancel := context.WithCancel(t.Context())
		defer cancel()

		// Start service in background
		go cws.Run(ctx)

		// Send data to channel
		testData := []byte("test data")
		client.StreamCh <- testData

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify data was written
		assert.Equal(t, 1, mockWriter.GetWriteCount())
		assert.Equal(t, testData, mockWriter.writtenData[0])
	})

	t.Run("Run_HandlesWriteErrors", func(t *testing.T) {
		t.Parallel()

		// Setup
		client := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
		}
		mockWriter := &mockWriter{
			shouldError: true,
		}

		cws := services.NewClientWriterService(zerolog.Nop(), client, mockWriter)

		ctx, cancel := context.WithCancel(t.Context())
		defer cancel()

		// Start service in background
		go cws.Run(ctx)

		// Send data to channel
		client.StreamCh <- []byte("test data")

		// Wait for processing
		time.Sleep(50 * time.Millisecond)

		// Verify write was attempted
		assert.Equal(t, 1, mockWriter.GetWriteCount())
	})

	t.Run("Run_ExitsOnContextDone", func(t *testing.T) {
		t.Parallel()

		// Setup
		client := entity.Client{
			ID:       uuid.New(),
			StreamCh: make(chan value.TSPacket, 10),
		}
		mockWriter := &mockWriter{}

		cws := services.NewClientWriterService(zerolog.Nop(), client, mockWriter)

		ctx, cancel := context.WithCancel(t.Context())

		// Start service in background with done channel to detect exit
		done := make(chan struct{})
		go func() {
			cws.Run(ctx)
			close(done)
		}()

		// Cancel context to trigger exit
		cancel()

		// Wait for service to exit
		select {
		case <-done:
			// Success - service exited
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Service did not exit after context cancellation")
		}
	})
}

// mockWriter implements io.Writer for testing.
type mockWriter struct {
	writeCount  int
	writtenData [][]byte
	shouldError bool
	mu          sync.Mutex
}

func (mw *mockWriter) Write(p []byte) (n int, err error) {
	mw.mu.Lock()
	defer mw.mu.Unlock()

	mw.writeCount++

	if mw.shouldError {
		return 0, errors.New("write error")
	}

	dataCopy := make([]byte, len(p))
	copy(dataCopy, p)
	mw.writtenData = append(mw.writtenData, dataCopy)

	return len(p), nil
}

func (mw *mockWriter) GetWriteCount() int {
	mw.mu.Lock()
	defer mw.mu.Unlock()

	return mw.writeCount
}

func (mw *mockWriter) GetWrittenData() [][]byte {
	mw.mu.Lock()
	defer mw.mu.Unlock()

	return mw.writtenData
}
