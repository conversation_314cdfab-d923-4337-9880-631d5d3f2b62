package services

import (
	"slices"
	"sync"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

var _ interfaces.StreamDispatchService = &StreamDispatchService{}

type StreamDispatchService struct {
	log      zerolog.Logger
	streamCh chan *value.Packet

	mu      sync.RWMutex
	clients []entity.Client
}

func NewStreamDispatchService(
	log zerolog.Logger,
	streamCh chan *value.Packet,
) *StreamDispatchService {
	return &StreamDispatchService{
		log:      log.With().Str("component", "relay/stream_dispatch_service").Logger(),
		streamCh: streamCh,
	}
}

func (sdcs *StreamDispatchService) Run(readyCh chan bool) {
	readyCh <- true

	for p := range sdcs.streamCh {
		sdcs.mu.RLock()
		clients := sdcs.clients
		sdcs.mu.RUnlock()

		for _, c := range clients {
			if !c.Filter.FilterPacket(p) {
				continue
			}

			select {
			case c.StreamCh <- p.Raw:
			default:
				sdcs.log.Warn().
					Str("clientID", c.ID.String()).
					Msg("dropping packets due to slow client")
			}
		}
	}
}

// ClientCount returns the number of real clients skipping scan clients.
func (sdcs *StreamDispatchService) ClientCount() int {
	sdcs.mu.Lock()
	defer sdcs.mu.Unlock()
	var count int
	for _, c := range sdcs.clients {
		if !c.IsDummy {
			count++
		}
	}
	return count
}

func (sdcs *StreamDispatchService) AddClient(client entity.Client) {
	sdcs.mu.Lock()
	defer sdcs.mu.Unlock()

	sdcs.clients = append(sdcs.clients, client)
}

func (sdcs *StreamDispatchService) RemoveClient(clientID uuid.UUID) {
	sdcs.mu.Lock()
	defer sdcs.mu.Unlock()

	i := slices.IndexFunc(sdcs.clients, func(c entity.Client) bool {
		return c.ID == clientID
	})

	if i == -1 {
		return
	}

	sdcs.clients = append(sdcs.clients[:i], sdcs.clients[i+1:]...)
}
