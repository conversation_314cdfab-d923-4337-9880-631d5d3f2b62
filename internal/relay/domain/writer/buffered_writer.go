package writer

import (
	"io"
	"sync"

	"github.com/Comcast/gots/packet"
)

var _ io.Writer = &BufferedWriter{}

const (
	bufferSize = 32 * packet.PacketSize
)

type BufferedWriter struct {
	writer io.Writer
	buffer []byte
	mu     sync.Mutex
}

func NewBufferedWriter(writer io.Writer) *BufferedWriter {
	return &BufferedWriter{
		writer: writer,
		buffer: make([]byte, 0, bufferSize),
	}
}

func (bw *BufferedWriter) Write(p []byte) (int, error) {
	bw.mu.Lock()
	defer bw.mu.Unlock()

	// If adding this packet would exceed capacity, flush first
	if len(bw.buffer)+len(p) > cap(bw.buffer) {
		if len(bw.buffer) > 0 {
			_, err := bw.writer.Write(bw.buffer)
			if err != nil {
				return 0, err
			}
			bw.buffer = bw.buffer[:0] // Reset buffer
		}
	}

	bw.buffer = append(bw.buffer, p...)
	return len(p), nil
}
