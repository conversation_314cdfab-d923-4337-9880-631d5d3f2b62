package value

import (
	"errors"
	"fmt"
	"net/netip"
)

var ErrPortOutOfBounds = errors.New("port value is out of bounds")

type Address struct {
	netip.AddrPort
}

func ParseAddress(ip string, port uint16) (a Address, err error) {
	address, err := netip.ParseAddr(ip)
	if err != nil {
		err = fmt.Errorf("failed parsing address ip: %w", err)
		return
	}

	a = Address{
		AddrPort: netip.AddrPortFrom(address, port),
	}

	return
}

func ParseAddressI32(ip string, port int32) (a Address, err error) {
	if port < 0 || port > 65535 {
		err = ErrPortOutOfBounds
		return
	}

	return ParseAddress(ip, uint16(port))
}

func ParseAddressRaw(raw string) (a Address, err error) {
	address, err := netip.ParseAddrPort(raw)
	if err != nil {
		err = fmt.Errorf("failed parsing address: %w", err)
		return
	}

	a = Address{
		AddrPort: address,
	}

	return
}

// MustParseAddress panics when given invalid input.
// This function should only be used in tests.
func MustParseAddress(ip string, port uint16) Address {
	address := netip.MustParseAddr(ip)
	return Address{
		AddrPort: netip.AddrPortFrom(address, port),
	}
}
