package value_test

import (
	"net/netip"
	"testing"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNetworkInterfaces(t *testing.T) {
	t.<PERSON>llel()

	t.Run("TestMatchAddress_NoNetworkInterface_NoInterfaceMatched", func(t *testing.T) {
		t.<PERSON>llel()

		nis := value.NewNetworkInterfaces([]value.NetworkInterface{})
		address := value.MustParseAddress("**********", 5200)

		_, err := nis.MatchAddress(address)
		require.ErrorIs(t, err, value.ErrMatchingNetworkPrefixNotFound)
	})

	t.Run(
		"TestMatchAddress_SingleNetworkInterface_MatchesLoopbackNetworkInterface",
		func(t *testing.T) {
			t.Parallel()

			ni := value.NetworkInterface{
				Name:   "lo",
				Prefix: netip.MustParsePrefix("0.0.0.0/0"),
			}
			nis := value.NewNetworkInterfaces([]value.NetworkInterface{ni})
			address := value.MustParseAddress("**********", 5200)

			matchedNi, err := nis.MatchAddress(address)
			require.NoError(t, err)

			assert.Equal(t, ni, matchedNi)
		},
	)

	t.Run(
		"TestMatchAddress_MultipleNetworkInterfaces_MatchesVirtualNetworkInterface1",
		func(t *testing.T) {
			t.Parallel()

			ni := value.NetworkInterface{
				Name:   "vlan1",
				Prefix: netip.MustParsePrefix("*********/24"),
			}
			nis := value.NewNetworkInterfaces(
				[]value.NetworkInterface{
					{
						Name:   "lo",
						Prefix: netip.MustParsePrefix("0.0.0.0/0"),
					},
					{
						Name:   "vlan2",
						Prefix: netip.MustParsePrefix("*********/16"),
					},
					{
						Name:   "vlan3",
						Prefix: netip.MustParsePrefix("*********/30"),
					},
					ni,
				},
			)
			address := value.MustParseAddress("**********", 5200)

			matchedNi, err := nis.MatchAddress(address)
			require.NoError(t, err)

			assert.Equal(t, ni, matchedNi)
		},
	)

	t.Run(
		"TestMatchAddress_MultipleNetworkInterfaces_MatchesVirtualNetworkInterface2",
		func(t *testing.T) {
			t.Parallel()

			ni := value.NetworkInterface{
				Name:   "vlan2",
				Prefix: netip.MustParsePrefix("*********/16"),
			}
			nis := value.NewNetworkInterfaces(
				[]value.NetworkInterface{
					{
						Name:   "lo",
						Prefix: netip.MustParsePrefix("0.0.0.0/0"),
					},
					{
						Name:   "vlan1",
						Prefix: netip.MustParsePrefix("*********/24"),
					},
					{
						Name:   "vlan3",
						Prefix: netip.MustParsePrefix("*********/30"),
					},
					ni,
				},
			)
			address := value.MustParseAddress("***********", 5200)

			matchedNi, err := nis.MatchAddress(address)
			require.NoError(t, err)

			assert.Equal(t, ni, matchedNi)
		},
	)
}
