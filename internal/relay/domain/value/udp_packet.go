package value

import "github.com/Comcast/gots/packet"

type UDPPacket []byte

// ParseTSPackets extracts valid TS packets from a UDP packet.
func (up UDPPacket) ParseTSPackets() []TSPacket {
	var tsPackets []TSPacket
	for i := 0; i <= len(up)-packet.PacketSize; i += packet.PacketSize {
		if (up)[i] != tsPacketSyncByte {
			continue
		}

		tsPackets = append(tsPackets, TSPacket(up[i:i+packet.PacketSize]))
	}

	return tsPackets
}
