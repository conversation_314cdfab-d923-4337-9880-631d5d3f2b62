package value

import (
	"cmp"
	"errors"
	"net/netip"
	"slices"
)

var ErrMatchingNetworkPrefixNotFound = errors.New("matching network prefix not found")

type NetworkInterface struct {
	Name   string       `json:"name"`
	Prefix netip.Prefix `json:"prefix"`
}

type NetworkInterfaces []NetworkInterface

func NewNetworkInterfaces(networkInterface []NetworkInterface) NetworkInterfaces {
	slices.SortFunc(networkInterface, func(a, b NetworkInterface) int {
		return cmp.Compare(a.Prefix.Bits(), b.Prefix.Bits())
	})

	slices.Reverse(networkInterface)

	return networkInterface
}

func (nis NetworkInterfaces) MatchAddress(address Address) (ni NetworkInterface, err error) {
	for _, ni := range nis {
		if ni.Prefix.Contains(address.Addr()) {
			return ni, nil
		}
	}

	return NetworkInterface{}, ErrMatchingNetworkPrefixNotFound
}
