// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/listeners (interfaces: Listener)
//
// Generated by this command:
//
//	mockgen -destination mocks/listener_mock.go . Listener
//

// Package mock_listeners is a generated GoMock package.
package mock_listeners

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	value "git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	gomock "go.uber.org/mock/gomock"
)

// MockListener is a mock of Listener interface.
type MockListener struct {
	ctrl     *gomock.Controller
	recorder *MockListenerMockRecorder
	isgomock struct{}
}

// MockListenerMockRecorder is the mock recorder for MockListener.
type MockListenerMockRecorder struct {
	mock *MockListener
}

// NewMockListener creates a new mock instance.
func NewMockListener(ctrl *gomock.Controller) *MockListener {
	mock := &MockListener{ctrl: ctrl}
	mock.recorder = &MockListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockListener) EXPECT() *MockListenerMockRecorder {
	return m.recorder
}

// AttachStream mocks base method.
func (m *MockListener) AttachStream(stream *entity.Stream) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachStream", stream)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachStream indicates an expected call of AttachStream.
func (mr *MockListenerMockRecorder) AttachStream(stream any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachStream", reflect.TypeOf((*MockListener)(nil).AttachStream), stream)
}

// Close mocks base method.
func (m *MockListener) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockListenerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockListener)(nil).Close))
}

// DetachStream mocks base method.
func (m *MockListener) DetachStream(address value.Address) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachStream", address)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachStream indicates an expected call of DetachStream.
func (mr *MockListenerMockRecorder) DetachStream(address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachStream", reflect.TypeOf((*MockListener)(nil).DetachStream), address)
}

// HasStreams mocks base method.
func (m *MockListener) HasStreams() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasStreams")
	ret0, _ := ret[0].(bool)
	return ret0
}

// HasStreams indicates an expected call of HasStreams.
func (mr *MockListenerMockRecorder) HasStreams() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasStreams", reflect.TypeOf((*MockListener)(nil).HasStreams))
}

// Listen mocks base method.
func (m *MockListener) Listen() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Listen")
}

// Listen indicates an expected call of Listen.
func (mr *MockListenerMockRecorder) Listen() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Listen", reflect.TypeOf((*MockListener)(nil).Listen))
}
