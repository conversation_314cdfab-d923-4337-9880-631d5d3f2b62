// Code generated by MockGen. DO NOT EDIT.
// Source: git.moderntv.eu/multicast-probe/internal/relay/domain/listeners (interfaces: ListenerService)
//
// Generated by this command:
//
//	mockgen -destination mocks/listener_service_mock.go . ListenerService
//

// Package mock_listeners is a generated GoMock package.
package mock_listeners

import (
	reflect "reflect"

	entity "git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	value "git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	gomock "go.uber.org/mock/gomock"
)

// MockListenerService is a mock of ListenerService interface.
type MockListenerService struct {
	ctrl     *gomock.Controller
	recorder *MockListenerServiceMockRecorder
	isgomock struct{}
}

// MockListenerServiceMockRecorder is the mock recorder for MockListenerService.
type MockListenerServiceMockRecorder struct {
	mock *MockListenerService
}

// NewMockListenerService creates a new mock instance.
func NewMockListenerService(ctrl *gomock.Controller) *MockListenerService {
	mock := &MockListenerService{ctrl: ctrl}
	mock.recorder = &MockListenerServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockListenerService) EXPECT() *MockListenerServiceMockRecorder {
	return m.recorder
}

// AttachStream mocks base method.
func (m *MockListenerService) AttachStream(stream *entity.Stream) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachStream", stream)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachStream indicates an expected call of AttachStream.
func (mr *MockListenerServiceMockRecorder) AttachStream(stream any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachStream", reflect.TypeOf((*MockListenerService)(nil).AttachStream), stream)
}

// DetachStream mocks base method.
func (m *MockListenerService) DetachStream(address value.Address) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachStream", address)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachStream indicates an expected call of DetachStream.
func (mr *MockListenerServiceMockRecorder) DetachStream(address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachStream", reflect.TypeOf((*MockListenerService)(nil).DetachStream), address)
}
