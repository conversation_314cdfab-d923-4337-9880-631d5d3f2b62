package server

import (
	"context"

	"git.moderntv.eu/multicast-probe/internal/relay/api/grpc/mappers"
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
	"github.com/rs/zerolog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ConfigurationServer struct {
	proto.UnimplementedRelayConfigurationServiceServer
	log                  zerolog.Logger
	configurationService interfaces.ConfigurationService
}

func NewConfigurationServer(
	logger zerolog.Logger,
	configurationService interfaces.ConfigurationService,
) *ConfigurationServer {
	return &ConfigurationServer{
		log:                  logger.With().Str("component", "relay/grpc_server").<PERSON>gger(),
		configurationService: configurationService,
	}
}

func (cs *ConfigurationServer) Configuration(_ context.Context, _ *proto.ConfigurationRequest) (
	*proto.ConfigurationResponse,
	error,
) {
	q := query.ConfigurationQuery{}

	r, err := cs.configurationService.Configuration(q)
	if err != nil {
		cs.log.Error().
			Err(err).
			Msg("failed running configuration query")

		return nil, status.Error(codes.Internal, err.Error())
	}

	protoConfiguration := mappers.ConfigurationToProtoConfiguration(r.Configuration)

	return &proto.ConfigurationResponse{
		Configuration: protoConfiguration,
	}, nil
}

func (cs *ConfigurationServer) SetConfiguration(
	_ context.Context,
	req *proto.SetConfigurationRequest,
) (
	*proto.SetConfigurationResponse,
	error,
) {
	configuration, err := mappers.ProtoConfigurationToConfiguration(req.GetConfiguration())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to convert configuration: %v", err)
	}

	err = configuration.Validate()
	if err != nil {
		cs.log.Trace().
			Err(err).
			Msg("failed to validate configuration")

		return nil, status.Errorf(
			codes.InvalidArgument,
			"failed to validate configuration: %v",
			err,
		)
	}

	cmd := command.ConfigurationEditCommand{
		Configuration: configuration,
	}

	_, err = cs.configurationService.SetConfiguration(cmd)
	if err != nil {
		cs.log.Error().
			Err(err).
			Msg("failed running set configuration command")

		return nil, status.Errorf(codes.Internal, "failed to set configuration: %v", err)
	}

	return &proto.SetConfigurationResponse{}, nil
}
