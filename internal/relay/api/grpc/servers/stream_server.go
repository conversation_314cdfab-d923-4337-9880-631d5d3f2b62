package server

import (
	"context"

	"git.moderntv.eu/multicast-probe/internal/relay/api/grpc/mappers"
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/interfaces"
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
	"github.com/rs/zerolog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type StreamServer struct {
	proto.UnimplementedRelayStreamServiceServer
	log           zerolog.Logger
	streamService interfaces.StreamService
}

func NewStreamServer(
	logger zerolog.Logger,
	streamService interfaces.StreamService,
) *StreamServer {
	return &StreamServer{
		log:           logger.With().Str("component", "relay/grpc_stream_server").<PERSON>gger(),
		streamService: streamService,
	}
}

func (ss *StreamServer) Scan(
	_ context.Context,
	req *proto.ScanRequest,
) (res *proto.ScanResponse, err error) {
	address, err := value.ParseAddressI32(req.GetMulticastIp(), req.GetPort())
	if err != nil {
		ss.log.Trace().
			Err(err).
			Str("host", req.GetMulticastIp()).
			Int32("port", req.GetPort()).
			Msg("failed parsing address")

		err = status.Error(codes.InvalidArgument, err.Error())
		return
	}

	ss.log.Debug().
		Interface("address", address).
		Msg("received scan request")

	c := command.ScanCommand{
		Address: address,
	}

	_, err = ss.streamService.Scan(c)
	if err != nil {
		ss.log.Error().
			Interface("address", address).
			Msg("processed scan request")

		err = status.Error(codes.Internal, err.Error())
		return
	}

	ss.log.Debug().
		Interface("address", address).
		Msg("processed scan request")

	res = &proto.ScanResponse{}

	return
}

func (ss *StreamServer) StreamsInfo(
	_ context.Context,
	_ *proto.StreamsInfoRequest,
) (*proto.StreamsInfoResponse, error) {
	ss.log.Debug().
		Msg("received streams info request")

	r, err := ss.streamService.StreamsInfo()
	if err != nil {
		ss.log.Error().
			Err(err).
			Msg("failed processing streams info request")
		return nil, status.Error(codes.Internal, err.Error())
	}

	response := &proto.StreamsInfoResponse{
		Streams: mappers.StreamsInfoQueryResultToProtoStreamInfos(r),
	}

	ss.log.Debug().
		Msg("processed streams info request")

	return response, nil
}

func (ss *StreamServer) StreamInfo(
	_ context.Context,
	req *proto.StreamInfoRequest,
) (*proto.StreamInfoResponse, error) {
	address, err := value.ParseAddressI32(req.GetMulticastIp(), req.GetPort())
	if err != nil {
		ss.log.Trace().
			Err(err).
			Str("host", req.GetMulticastIp()).
			Int32("port", req.GetPort()).
			Msg("failed parsing address")

		return nil, status.Error(codes.InvalidArgument, err.Error())
	}

	ss.log.Debug().
		Interface("address", address).
		Msg("received stream info request")

	q := query.StreamInfoQuery{
		Address: address,
	}

	r, err := ss.streamService.StreamInfo(q)
	if err != nil {
		ss.log.Error().
			Err(err).
			Interface("address", address).
			Msg("failed processing stream info request")
		return nil, status.Error(codes.Internal, err.Error())
	}

	stream := mappers.StreamInfoQueryResultToProtoStreamInfo(r)

	ss.log.Debug().
		Str("stream", stream.String()).
		Msg("processed stream info request")

	return &proto.StreamInfoResponse{
		Stream: stream,
	}, nil
}
