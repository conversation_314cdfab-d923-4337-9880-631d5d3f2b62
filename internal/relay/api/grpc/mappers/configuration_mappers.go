package mappers

import (
	"net/netip"
	"time"

	"git.moderntv.eu/multicast-probe/internal/relay/domain/entity"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func ProtoConfigurationToConfiguration(
	pc *proto.Configuration,
) (c entity.Configuration, err error) {
	networkInterfaces := make(value.NetworkInterfaces, 0, len(pc.GetNetworkInterfaces()))
	for _, ni := range pc.GetNetworkInterfaces() {
		prefix, err := netip.ParsePrefix(ni.GetPrefix())
		if err != nil {
			return entity.Configuration{}, status.Errorf(
				codes.InvalidArgument,
				"found invalid prefix for network interface: %v",
				err,
			)
		}

		networkInterfaces = append(networkInterfaces, value.NetworkInterface{
			Name:   ni.GetName(),
			Prefix: prefix,
		})
	}

	c = entity.Configuration{
		ScanCleanupDelay:        time.Duration(pc.GetCleanupDelay()),
		ScanTimeout:             time.Duration(pc.GetScanTimeout()),
		DispatcherBufferSize:    int(pc.GetDispatcherBufferSize()),
		StreamChannelBufferSize: int(pc.GetStreamChannelBufferSize()),
		JoinsPerSecond:          int(pc.GetJoinsPerSecond()),
		JoinBufferSize:          int(pc.GetJoinBufferSize()),
		ConnectionTimeout:       time.Duration(pc.GetConnectionTimeout()),
		UDPPacketSize:           int(pc.GetUdpPacketSize()),
		NetworkInterfaces:       value.NewNetworkInterfaces(networkInterfaces),
	}

	return
}

func ConfigurationToProtoConfiguration(c entity.Configuration) *proto.Configuration {
	networkInterfaces := make([]*proto.NetworkInterface, 0, len(c.NetworkInterfaces))
	for _, ni := range c.NetworkInterfaces {
		networkInterfaces = append(networkInterfaces, &proto.NetworkInterface{
			Name:   ni.Name,
			Prefix: ni.Prefix.String(),
		})
	}

	return &proto.Configuration{
		ScanTimeout:             int64(c.ScanTimeout),
		CleanupDelay:            int64(c.ScanCleanupDelay),
		DispatcherBufferSize:    int64(c.DispatcherBufferSize),
		StreamChannelBufferSize: int64(c.StreamChannelBufferSize),
		JoinsPerSecond:          int64(c.JoinsPerSecond),
		JoinBufferSize:          int64(c.JoinBufferSize),
		ConnectionTimeout:       int64(c.ConnectionTimeout),
		UdpPacketSize:           int64(c.UDPPacketSize),
		NetworkInterfaces:       networkInterfaces,
	}
}
