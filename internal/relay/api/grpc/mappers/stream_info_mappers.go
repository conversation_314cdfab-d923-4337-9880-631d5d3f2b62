package mappers

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	proto "git.moderntv.eu/multicast-probe/proto/relay/v1"
)

func StreamsInfoQueryResultToProtoStreamInfos(
	r query.StreamsInfoQueryResult,
) []*proto.StreamInfo {
	streamsInfos := make([]*proto.StreamInfo, 0, len(r.Streams))
	for _, sr := range r.Streams {
		streamInfo := StreamInfoQueryResultToProtoStreamInfo(sr)
		streamsInfos = append(streamsInfos, streamInfo)
	}

	return streamsInfos
}

func StreamInfoQueryResultToProtoStreamInfo(
	r query.StreamInfoQueryResult,
) *proto.StreamInfo {
	streamInfo := &proto.StreamInfo{
		MulticastIp: r.Address.Addr().String(),
		Port:        int32(r.Address.Port()),
		Clients:     int64(r.Clients),
		Programs:    make([]*proto.Program, 0, len(r.Programs)),
		Metrics: &proto.StreamMetrics{
			StreamPresentDuration:         r.Metrics.PresentDuration.Load(),
			StreamReceivingDuration:       r.Metrics.ReceivingDuration.Load(),
			StreamReceivedUdpPacketsTotal: r.Metrics.ReceivedUDPPackets.Load(),
			StreamReceivedTsPacketsTotal:  r.Metrics.ReceivedTSPackets.Load(),
			StreamReceivedBytesTotal:      r.Metrics.ReceivedBytes.Load(),
			StreamReceiverCount:           r.Metrics.Receivers.Load(),
			StreamDiscontinuitiesTotal:    r.Metrics.Discontinuities.Load(),
			StreamMissingUdpPacketsTotal:  r.Metrics.MissingUDPPackets.Load(),
		},
	}

	for _, program := range r.Programs {
		protoProgram := &proto.Program{
			Id:       int64(program.ID),
			Title:    program.Title,
			Provider: program.Provider,
			Tracks:   make([]*proto.Track, 0, len(program.Tracks())),
			Metrics: &proto.ProgramMetrics{
				ProgramReceiverCount:          program.Metrics.Receivers.Load(),
				ProgramDiscontinuities:        program.Metrics.Discontinuities.Load(),
				ProgramReceivedBytesTotal:     program.Metrics.ReceivedBytes.Load(),
				ProgramReceivedTsPacketsTotal: program.Metrics.ReceivedTSPackets.Load(),
			},
		}

		for _, track := range program.Tracks() {
			protoProgram.Tracks = append(protoProgram.Tracks, &proto.Track{
				Pid:         int64(track.PID),
				Type:        int64(track.Type),
				Description: track.Description,
				Metrics: &proto.TrackMetrics{
					TrackDiscontinuities:        track.Metrics.Discontinuities.Load(),
					TrackReceivedBytesTotal:     track.Metrics.ReceivedBytes.Load(),
					TrackReceivedTsPacketsTotal: track.Metrics.ReceivedTSPackets.Load(),
				},
			})
		}

		streamInfo.Programs = append(streamInfo.Programs, protoProgram)
	}

	return streamInfo
}
