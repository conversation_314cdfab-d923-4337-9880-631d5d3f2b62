package controllers_test

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.moderntv.eu/multicast-probe/internal/config"
	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/controllers"
	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/dto/response"
	mock_interfaces "git.moderntv.eu/multicast-probe/internal/relay/application/interfaces/mocks"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestStreamController(t *testing.T) {
	t.<PERSON>l()

	conf := setupConfig()

	t.Run("GetStreamUDP_InvalidAddress_ReturnsBadRequest", func(t *testing.T) {
		t.<PERSON>llel()
		r, _ := setupStreamController(t, conf)

		rec := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/udp/123.23.xd.1:5000", nil)

		r.ServeHTTP(rec, req)

		res := rec.Result()
		assert.Equal(t, http.StatusBadRequest, res.StatusCode)

		var resp response.ErrorResponse
		bodyBytes, err := io.ReadAll(res.Body)
		require.NoError(t, err)

		err = json.Unmarshal(bodyBytes, &resp)
		require.NoError(t, err)

		assert.Equal(t,
			response.ErrorResponse{
				Error: "failed to convert request to query: " +
					"failed parsing address: ParseAddr(\"123.23.xd.1\"): unexpected character (at \"xd.1\")",
			},
			resp,
		)
	})

	t.Run("GetStreamUDP_InvalidPort_ReturnsBadRequest", func(t *testing.T) {
		t.Parallel()
		r, _ := setupStreamController(t, conf)

		rec := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/udp/***********:abc", nil)

		r.ServeHTTP(rec, req)

		res := rec.Result()
		assert.Equal(t, http.StatusBadRequest, res.StatusCode)

		var resp response.ErrorResponse
		bodyBytes, err := io.ReadAll(res.Body)
		require.NoError(t, err)

		err = json.Unmarshal(bodyBytes, &resp)
		require.NoError(t, err)

		assert.Equal(t,
			response.ErrorResponse{
				Error: "failed to convert request to query: " +
					"failed parsing address: invalid port \"abc\" parsing \"***********:abc\"",
			},
			resp,
		)
	})

	t.Run("GetStreamUDPProgram_InvalidAddress_ReturnsBadRequest", func(t *testing.T) {
		t.Parallel()
		r, _ := setupStreamController(t, conf)

		rec := httptest.NewRecorder()
		req := httptest.NewRequest(
			http.MethodGet,
			"/udp/123.23.xd.1:5000/pid/123",
			nil,
		)

		r.ServeHTTP(rec, req)

		res := rec.Result()
		assert.Equal(t, http.StatusBadRequest, res.StatusCode)

		var resp response.ErrorResponse
		bodyBytes, err := io.ReadAll(res.Body)
		require.NoError(t, err)

		err = json.Unmarshal(bodyBytes, &resp)
		require.NoError(t, err)

		assert.Equal(t,
			response.ErrorResponse{
				Error: "failed to convert request to query: " +
					"failed parsing address: ParseAddr(\"123.23.xd.1\"): unexpected character (at \"xd.1\")",
			},
			resp,
		)
	})

	t.Run("GetStreamUDPProgram_InvalidPort_ReturnsBadRequest", func(t *testing.T) {
		t.Parallel()
		r, _ := setupStreamController(t, conf)

		rec := httptest.NewRecorder()
		req := httptest.NewRequest(
			http.MethodGet,
			"/udp/***********:abc/pid/123",
			nil,
		)

		r.ServeHTTP(rec, req)

		res := rec.Result()
		assert.Equal(t, http.StatusBadRequest, res.StatusCode)

		var resp response.ErrorResponse
		bodyBytes, err := io.ReadAll(res.Body)
		require.NoError(t, err)

		err = json.Unmarshal(bodyBytes, &resp)
		require.NoError(t, err)

		assert.Equal(t,
			response.ErrorResponse{
				Error: "failed to convert request to query: " +
					"failed parsing address: invalid port \"abc\" parsing \"***********:abc\"",
			},
			resp,
		)
	})

	t.Run("GetStreamUDPProgram_InvalidProgramID_ReturnsBadRequest", func(t *testing.T) {
		t.Parallel()
		r, _ := setupStreamController(t, conf)

		rec := httptest.NewRecorder()
		req := httptest.NewRequest(
			http.MethodGet,
			"/udp/***********:abc/pid/12ab3",
			nil,
		)

		r.ServeHTTP(rec, req)

		res := rec.Result()
		assert.Equal(t, http.StatusBadRequest, res.StatusCode)

		var resp response.ErrorResponse
		bodyBytes, err := io.ReadAll(res.Body)
		require.NoError(t, err)

		err = json.Unmarshal(bodyBytes, &resp)
		require.NoError(t, err)

		assert.Equal(t,
			response.ErrorResponse{
				Error: "failed to bind path parameters: strconv.ParseInt: parsing \"12ab3\": invalid syntax",
			},
			resp,
		)
	})
}

func setupConfig() config.RelayConfig {
	return config.RelayConfig{}
}

func setupStreamController(
	t *testing.T,
	_ config.RelayConfig,
) (r *gin.Engine, ss *mock_interfaces.MockStreamService) { //nolint: unparam
	t.Helper()

	ctrl := gomock.NewController(t)
	ss = mock_interfaces.NewMockStreamService(ctrl)
	pc := controllers.NewStreamController(log.Logger, ss)
	pcr := pc.GetRoutes()

	r = gin.New()
	for path, routes := range pcr.Routes {
		for method, handlers := range routes {
			var handlerFuncs []gin.HandlerFunc
			handlerFuncs = append(handlerFuncs, pcr.Middleware...)
			handlerFuncs = append(handlerFuncs, handlers...)
			r.Handle(method, pcr.Base+path, handlerFuncs...)
		}
	}

	return
}
