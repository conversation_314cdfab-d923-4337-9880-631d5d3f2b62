package controllers_test

import (
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	_ "git.moderntv.eu/multicast-probe/docs/rest-api/relay"
	"git.moderntv.eu/multicast-probe/internal/management/api/rest/controllers"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/swaggo/swag"
)

func TestMetaController(t *testing.T) {
	t.<PERSON><PERSON>()

	openAPISpec := swag.GetSwagger("relay")
	assert.NotNil(t, openAPISpec)

	mc := controllers.NewMetaController(log.Logger, openAPISpec)
	mcr := mc.GetRoutes()

	r := gin.New()
	for path, routes := range mcr.Routes {
		for method, handlers := range routes {
			var handlerFuncs []gin.HandlerFunc
			handlerFuncs = append(handlerFuncs, mcr.Middleware...)
			handlerFuncs = append(handlerFuncs, handlers...)
			r.<PERSON>(method, mcr.Base+path, handlerFuncs...)
		}
	}

	rec := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/_/apidoc", nil)

	r.ServeHTTP(rec, req)

	resp := rec.Result()
	assert.Equal(t, http.StatusOK, resp.StatusCode)
	bodyBytes, err := io.ReadAll(resp.Body)
	require.NoError(t, err)
	assert.Equal(t, []byte(openAPISpec.ReadDoc()), bodyBytes)
}
