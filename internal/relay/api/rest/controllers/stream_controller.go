package controllers

import (
	"net/http"

	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/dto/request"
	"git.moderntv.eu/multicast-probe/internal/relay/api/rest/dto/response"
	"git.moderntv.eu/multicast-probe/internal/relay/application/command"
	"git.moderntv.eu/multicast-probe/internal/relay/application/interfaces"
	"github.com/gin-gonic/gin"
	chttp "github.com/moderntv/cadre/http"
	"github.com/rs/zerolog"
)

type StreamController struct {
	log           zerolog.Logger
	streamService interfaces.StreamService
}

func NewStreamController(
	log zerolog.Logger,
	streamService interfaces.StreamService,
) *StreamController {
	return &StreamController{
		log:           log.With().Str("component", "relay/stream_controller").Logger(),
		streamService: streamService,
	}
}

func (sc *StreamController) GetRoutes() chttp.RoutingGroup {
	return chttp.RoutingGroup{
		Base: "/udp",
		Routes: map[string]map[string][]gin.HandlerFunc{
			"/:address": {
				http.MethodGet: []gin.HandlerFunc{sc.GetStreamUDPController},
			},
			"/:address/pid/:programId": {
				http.MethodGet: []gin.HandlerFunc{sc.GetStreamUDPProgramController},
			},
		},
	}
}

// GetStreamUDPController adds a receiver for a multicast stream and streams data progressively.
//
//	@ID				getStreamUDP
//	@Summary		Progressively stream multicast data
//	@Description	Adds a receiver for the stream identified by multicast address
//	@Description	and writes source stream data as a progressive stream.
//	@Tags			streams
//	@Accept			json
//	@Produce		video/mp2t
//	@Param			address	path	string	true	"Stream address in format multicast_ip:port (e.g., *********:5000)"
//	@Router			/udp/{address} [get]
//	@Success		200		{string}	string	"Progressive stream data in MPEG-TS format"
//	@Failure		400,500	{object}	response.ErrorResponse
func (sc *StreamController) GetStreamUDPController(c *gin.Context) {
	c.Header("Content-Type", "video/mp2t")
	c.Header("Transfer-Encoding", "chunked")

	var req request.GetStreamUDPRequest
	if err := c.ShouldBindUri(&req); err != nil {
		sc.log.Trace().
			Err(err).
			Msg("failed to bind request")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind path parameters: " + err.Error(),
			},
		)
		return
	}

	q, err := req.ToQuery(c)
	if err != nil {
		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to query: " + err.Error(),
			},
		)
		return
	}

	streamQueryResult, err := sc.streamService.Stream(q)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running stream query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running stream query: " + err.Error(),
			},
		)
		return
	}

	// Wait for client disconnection
	<-c.Request.Context().Done()
	teardownCmd := command.StreamTeardownCommand{
		Address: q.Address,
		Client:  streamQueryResult.Client,
	}

	_, err = sc.streamService.TeardownStream(teardownCmd)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("command", teardownCmd).
			Msg("failed running teardown stream command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running stream teardown command: " + err.Error(),
			},
		)
		return
	}

	sc.log.Debug().
		Interface("address", q.Address).
		Str("clientID", streamQueryResult.Client.ID.String()).
		Msg("HTTP client disconnected from stream")

	c.JSON(http.StatusOK, response.DataResponse[response.GetStreamUDPResponse]{})
}

// GetStreamUDPProgramController adds a receiver for a multicast stream and streams program data progressively.
//
//	@ID				getStreamUDPProgram
//	@Summary		Progressively stream program multicast data
//	@Description	Adds a receiver for the stream identified by multicast address, filters packets by program ID,
//	@Description	and writes source stream data as a progressive stream.
//	@Tags			streams
//	@Produce		video/mp2t
//	@Param			address		path	string	true	"Stream address in format multicast_ip:port (e.g., *********:5000)"
//	@Param			programId	path	integer	true	"Program ID to filter the stream (e.g., 423)"
//	@Router			/udp/{address}/pid/{programId} [get]
//	@Success		200		{string}	string	"Progressive stream data in MPEG-TS format filtered by program ID"
//	@Failure		400,500	{object}	response.ErrorResponse
func (sc *StreamController) GetStreamUDPProgramController(c *gin.Context) {
	c.Header("Content-Type", "video/mp2t")
	c.Header("Transfer-Encoding", "chunked")

	var req request.GetStreamUDPProgramRequest
	if err := c.ShouldBindUri(&req); err != nil {
		sc.log.Debug().
			Err(err).
			Msg("failed to bind request")

		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to bind path parameters: " + err.Error(),
			},
		)
		return
	}

	q, err := req.ToQuery(c)
	if err != nil {
		c.JSON(http.StatusBadRequest,
			response.ErrorResponse{
				Error: "failed to convert request to query: " + err.Error(),
			},
		)
		return
	}

	streamQueryResult, err := sc.streamService.Stream(q)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("query", q).
			Msg("failed running stream query")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running stream query: " + err.Error(),
			},
		)
		return
	}

	<-c.Request.Context().Done()
	teardownCmd := command.StreamTeardownCommand{
		Address: q.Address,
		Client:  streamQueryResult.Client,
	}

	_, err = sc.streamService.TeardownStream(teardownCmd)
	if err != nil {
		sc.log.Error().
			Err(err).
			Interface("command", teardownCmd).
			Msg("failed running teardown stream command")

		c.JSON(http.StatusInternalServerError,
			response.ErrorResponse{
				Error: "failed running stream teardown command: " + err.Error(),
			},
		)
		return
	}

	sc.log.Debug().
		Interface("address", q.Address).
		Str("clientID", streamQueryResult.Client.ID.String()).
		Int("programID", q.ProgramID).
		Msg("HTTP client disconnected from stream program")

	c.JSON(http.StatusOK, response.DataResponse[response.GetStreamUDPProgramResponse]{})
}
