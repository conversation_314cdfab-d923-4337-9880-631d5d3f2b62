package request

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/gin-gonic/gin"
)

type GetStreamUDPProgramRequest struct {
	Address   string `binding:"required" uri:"address"`
	ProgramID int    `binding:"required" uri:"programId"`
}

func (gsur *GetStreamUDPProgramRequest) ToQuery(c *gin.Context) (query.StreamUDPQuery, error) {
	address, err := value.ParseAddressRaw(gsur.Address)
	if err != nil {
		return query.StreamUDPQuery{}, err
	}

	return query.StreamUDPQuery{
		Ctx:       c.Request.Context(),
		Address:   address,
		ProgramID: gsur.ProgramID,
		Writer:    c.Writer,
	}, nil
}
