package request

import (
	"git.moderntv.eu/multicast-probe/internal/relay/application/query"
	"git.moderntv.eu/multicast-probe/internal/relay/domain/value"
	"github.com/gin-gonic/gin"
)

type GetStreamUDPRequest struct {
	Address string `binding:"required" uri:"address"`
}

func (gsupr *GetStreamUDPRequest) ToQuery(c *gin.Context) (query.StreamUDPQuery, error) {
	address, err := value.ParseAddressRaw(gsupr.Address)
	if err != nil {
		return query.StreamUDPQuery{}, err
	}

	return query.StreamUDPQuery{
		Ctx:       c.Request.Context(),
		Address:   address,
		Writer:    c.Writer,
		ProgramID: value.DefaultProgramID,
	}, nil
}
